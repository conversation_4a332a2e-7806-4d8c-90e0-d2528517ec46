package com.ruoyi.quartz.service;

import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.ParseException;

@Component
public interface ZzLicenseTaskService {

    /**
     * 定时任务每天0点10分刷新待处理证照列表到期状态
     * 2即将到期
     */
    void updateLicenseBackTimeStatus010();

    /**
     * 定时任务每天下午17点30分刷新待处理证照列表到期状态
     * 3已到期
     */
    void updateLicenseBackTimeStatus1730();

    /**
     * 定时任务每天0点20分刷新证照有效期到期状态和年审到期状态
     * 0未到期 1即将到期 2已到期
     */
    void updateLicenseExpireStatus020() throws ParseException, IOException;

    /**
     * 每10分钟执行一次
     * 证照借用时间结束前，提前30分钟进行提醒(时间做成可配置项)
     */
    void sendQYWXNotify(Integer timeMinutes);

}
