create table bl_mechanism
(
    id                bigint auto_increment comment '主键' primary key,
    mechanism_name  varchar(64) null comment '机构名称',
    mechanism_short_name  varchar(64) null comment '机构简称',
    mechanism_code   varchar(64) UNIQUE null comment '机构编码',
    join_time       datetime   null comment '机构入驻时间',
    sign_end_time       datetime   null comment '机构签约截止时间',
    mechanism_type  varchar(64) null comment '机构类型',
    mechanism_disposal_mode  varchar(64) null comment '处置模式',
    business_manager  varchar(64) null comment '业务负责人',
    settlement_date  datetime   null comment '约定结算日',
    unified_credit_code  varchar(64) null comment '统一信用代码',
    enterprise_name     varchar(64) null comment '企业名称',
    legal_person   varchar(64) null comment '法定代表人',
    incorporation_date  datetime   null comment '成立日期',
    personnel_scale  int  null comment '人员规模',
    collaboration_status varchar(10) default '2' null comment '合作状态  (1开启 2未开启)',
    status  varchar(10) default '1' comment '审核状态',
    process_id              varchar(64)                                           null comment '流程id',
    create_by         varchar(64)                        null comment '创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by         varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '机构表' row_format = DYNAMIC;


create table bl_mechanism_finance_account(
    id                bigint auto_increment comment '主键' primary key,
    mechanism_id bigint null comment '机构 id',
    account_name varchar(100) null comment '账号名称',
    duty_paragraph varchar(100) null comment '税号',
    bank varchar(100) null comment '开户银行',
    bank_account varchar(100) null comment '银行账号'
) comment '机构-结算账号明细表' row_format = DYNAMIC;


create table bl_mechanism_settlement_formula(
    id   bigint auto_increment comment '主键' primary key,
    mechanism_id bigint null comment '机构 id',
    settlement_cycle varchar(500) null comment '结算周期',
    settlement_ratio decimal(6,2) null comment '结算比例(%)',
    enable_status varchar(100) null comment '启用状态'
) comment '机构-结算公式明细表' row_format = DYNAMIC;


create table bl_mechanism_contacts(
    id   bigint auto_increment comment '主键' primary key,
    mechanism_id bigint null comment '机构 id',
    name varchar(64) null comment '姓名',
    sex  char(1) null comment '用户性别（0男 1女 2未知）',
    position varchar(64) null comment '职位',
    wechat varchar(64) null comment '微信',
    phone_number varchar(20) null comment '手机号',
    email varchar(200) null comment '邮箱',
    remark varchar(500) null comment '备注'
) comment '机构-联系人表' row_format = DYNAMIC;

create table bl_company_product(
    id   bigint auto_increment comment '主键' primary key,
    asset_company_id bigint null comment '资产公司',
    product_name  varchar(100) null comment '产品名称',
    version  varchar(100) null comment '版本号',
    interest_method  varchar(10) null comment '计息方式',
    description  varchar(500) null comment '说明',
    enable_status  varchar(10) default '1'  comment '启用状态',
    is_draft          varchar(10)                         null comment '草稿状态 1.草稿 2.非草稿',
    create_by         varchar(64)                        null comment '创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by         varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '不良系统-公司产品' row_format = DYNAMIC;

create table bl_company_product_interest(
     id   bigint auto_increment comment '主键' primary key,
     company_product_id bigint null comment '公司产品',
     interest_type  varchar(10) null comment '计息类型',
     annual_calculation_date  decimal(6,0) null comment '年计算日',
     annualized_interest_rate  decimal(10,4) null comment '年化利率',
     daily_interest_rate  decimal(14,8) null comment '日利率'
) comment '不良系统-公司产品-计息类型' row_format = DYNAMIC;

create table bl_promissory_note(
    id   bigint auto_increment comment '主键' primary key,
    asset_company_id bigint null comment '资产公司',
    company_product_id bigint null comment '产品配置',
    promissory_note_code varchar(64)  UNIQUE     null comment '导入编号',
    import_way         varchar(10)                        null comment '导入方式',
    import_amount     decimal(18,2)  null comment  '导入金额',
    import_number  decimal(8,0)  null comment  '导入笔数',
    import_interest  decimal(18,2)  null comment  '导入利息',
    import_principal_interest  decimal(18,2)  null comment  '导入本息',
    loan_start_date    datetime  null comment '放款开始日',
    loan_end_date      datetime  null comment '放款结束日',
    calculating_interest_date   datetime  null comment '开始计息日',
    file_name   varchar(64) null comment '文件名称',
    file_url    varchar(200) null comment '文件地址',
    rule_explanation  varchar(500) null comment '规则说明',
    import_state       varchar(10)  default '1'                       null comment '导入状态',
    create_by         varchar(64)                        null comment '创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by         varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '不良系统-借据' row_format = DYNAMIC;

create table bl_promissory_note_enclosure(
    id   bigint auto_increment comment '主键' primary key,
    promissory_note_id  bigint               null comment '借据id',
    age  varchar(64)                        null comment '年龄',
    sex  char(1) null comment '性别（0男 1女）',
    hometown  varchar(64)                        null comment '户籍所在地',
    phone_number  varchar(13)    null comment '联系人电话',
    current_address  varchar(100)    null comment '借款人现住址',
    company_product_name  varchar(64)    null comment '产品',
    loan_amount    decimal(18,2)  null comment '借款金额',
    interest_rate_year  decimal(10,4)  null comment '利率(年化)',
    loan_disbursement_date         datetime                       null comment '放款日',
    due_date         datetime                    null comment '到期日',
    remaining_principal  decimal(18,2)  null comment '金额(剩余本金)',
    cumulative_repayment_amount  decimal(18,2)  null comment '客户累计实还本金',
    loan_interest   decimal(18,2)  null comment '借款利息',
    loan_penalty_interest   decimal(18,2)  null comment '借款罚息',
    remaining_due   decimal(18,2)  null comment '剩余应还本息',
    periods   decimal(5,0)  null comment '期数',
    overdue_days  decimal(6,0)  null comment '逾期天数',
    accumulated_compensation   decimal(18,2)  null comment '累计代偿本金'
) comment '不良系统-借据-附件' row_format = DYNAMIC;

create table bl_asset_management(
    id   bigint auto_increment comment '主键' primary key,
    promissory_note_number  varchar(64)                null comment '借据号',
    borrower_name   varchar(64)                        null comment '借款人姓名',
    id_card   varchar(20)                        null comment '身份证',
    sex  char(1) null comment '用户性别（0男 1女 2未知）',
    hometown  varchar(64)                        null comment '户籍所在地',
    phone_number  varchar(13)    null comment '联系人电话',
    current_address  varchar(100)    null comment '借款人现住址',
    company_product_id  bigint    null comment '产品',
    loan_amount    decimal(18,2)  null comment '借款金额',
    interest_rate_year  decimal(10,4)  null comment '利率(年化)',
    loan_disbursement_date         datetime                       null comment '放款日',
    due_date         datetime                    null comment '到期日',
    remaining_principal  decimal(18,2)  null comment '金额(剩余本金)',
    cumulative_repayment_amount  decimal(18,2)  null comment '客户累计实还本金',
    loan_interest   decimal(18,2)  null comment '借款利息',
    loan_penalty_interest   decimal(18,2)  null comment '借款罚息',
    remaining_due   decimal(18,2)  null comment '剩余应还本息',

    overdue_interest    decimal(18,2)  null comment '逾期利息',
    compensatory_interest    decimal(18,2)  null comment '代偿利息',
    guarantee_interest    decimal(18,2)  null comment '担保利息',

    periods   decimal(5,0)  null comment '期数',
    overdue_days  decimal(6,0)  null comment '逾期天数',
    accumulated_compensation   decimal(18,2)  null comment '累计代偿本金',
    overdue_repayment         varchar(3)                        null comment '用户逾期还款',
    compensatory_repayment   decimal(18,2)  null comment '代偿还款',
    loan_contract           varchar(200)                        null comment '借款合同',
    is_overdue        varchar(10)                        null comment '是否逾期',
    customer_repayment_way        varchar(10)         null comment '客户还款方式',
    first_day_overdue       datetime   null comment '用户逾期首日',
    usage_loan        varchar(10)         null comment '借款用途',
    loan_platform       varchar(100)        null comment '借款平台',
    creditor_institutions_id       bigint        null comment '债权机构',
    import_interest_calculation       datetime   null comment '开始计息日(导入后)',
    outsourced_project_id       bigint                                null comment '关联委外方案',
    case_status                 varchar(10) default '1'               null comment '匹配案件状态(1.未匹配 2.已匹配)',
    create_by         varchar(64)                        null comment '创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by         varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '不良系统-资产管理' row_format = DYNAMIC;


create table bl_outsourced_project(
      id   bigint auto_increment comment '主键' primary key,
      outsourced_project_number  varchar(64)    UNIQUE    null comment '分案批次',
      creditor_institutions_id       bigint        null comment '债权机构',
      trustee_institution_id    bigint        null comment '受托机构',
      outsourced_start   datetime null comment '委外开始时间',
      outsourced_end   datetime null comment '委外结束时间',
      disposal_mode    varchar(10) default '1' null comment '处置模式',
      case_status   varchar(10) default '1' null comment '案件状态',
      status  varchar(10)  default '1' comment '审核状态',
      process_id       varchar(64)    null comment '流程 id',
      create_by         varchar(64)                        null comment '创建者',
      create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
      update_by         varchar(64)                        null comment '更新者',
      update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '不良系统-委外方案' row_format = DYNAMIC;

create table bl_outsourced_project_strategy(
    id   bigint auto_increment comment '主键' primary key,
    outsourced_project_id bigint        null comment '债权机构',
    case_range  varchar(300)                        null comment '案件范围',
    quantity   decimal(10)                        null comment '数量',
    amount_money   decimal(16,2)                        null comment '金额',
    allocation_rules  varchar(10)                    null comment '分配规则 (1.随机；2.逾期开始日早到晚；3.逾期开始日晚到早)',
    is_repayment varchar(10)                        null comment '客户逾期后是否发生还款',
    overdue_start datetime      null comment '逾期开始日期',
    overdue_end datetime      null comment '逾期截止日期'
) comment '不良系统-委外方案-分案策略' row_format = DYNAMIC;


create table bl_outsourced_repayment_detail(
     id   bigint auto_increment comment '主键' primary key,
     loan_bill         varchar(64)                        null comment '借款单',
     outsourced_project_number  varchar(64)    null comment '分案批次',
     borrower_name         varchar(64)                        null comment '借款人姓名',
     phone_number  varchar(13)    null comment '联系人电话',
     repayment_amount   decimal(18,2) null comment '还款金额',
     repayment_time       datetime default CURRENT_TIMESTAMP null comment '还款时间',
     serial_number  varchar(64)    null comment '流水号',
     trustee_institution_id    bigint        null comment '受托机构',
     check_status  varchar(10)    null comment '核对状态',
     service_amount   decimal(18,2) null comment '服务费用金额',
     remark  varchar(500)    null comment '流水号',
     create_by         varchar(64)                        null comment ' 创建者',
     create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
     update_by         varchar(64)                        null comment '更新者',
     update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '不良系统-委外还款明细' row_format = DYNAMIC;

create table bl_channel_business_reconciliation(
    id   bigint auto_increment comment '主键' primary key,
    reconciliation_code   varchar(64) UNIQUE  null comment  '对账单单号',
    settlement_institution_id    bigint  comment '结算机构',
    reconciliation_date     datetime     null comment  '对账日',
    verify_difference_status  varchar(10) default '1'  null comment  '校验差异状态',
    status varchar(10)   default '1' comment  '审批状态',
    settlement_status    varchar(10)  null comment  '结算状态',
    total_amount_collected   decimal(18,2)  comment '财务收款总额',
    total_repayment_amount   decimal(18,2)  comment '平台核销金额',
    difference_amount    decimal(18,2)  comment '差额',
    process_id   varchar(64)                        null comment '流程id',
    create_by         varchar(64)                        null comment ' 创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by         varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '不良系统-渠道业务对账单' row_format = DYNAMIC;


create table bl_channel_business_reconciliation_collection(
      id   bigint auto_increment comment '主键' primary key,
      business_reconciliation_id  bigint  comment '业务对账单 id',
      payment_date datetime  comment '日期',
      amount_collected   decimal(18,2)  comment '收款金额',
      payee   varchar(64)                        null comment '收款主体',
      payment   varchar(64)                        null comment '付款主体',
      remark  varchar(64)                        null comment '备注'
) comment '不良系统-渠道业务对账单-财务收款单' row_format = DYNAMIC;

create table bl_channel_business_reconciliation_repayment(
    id   bigint auto_increment comment '主键' primary key,
    business_reconciliation_id  bigint  comment '业务对账单 id',
    batch_number  varchar(64)                        null comment '批次号',
    promissory_note_number  varchar(64)                null comment '借据号',
    repayment_time   datetime  comment '还款时间',
    repayment_amount   decimal(18,2)  comment '委后还款金额'
) comment '不良系统-渠道业务对账单-委后还款明细' row_format = DYNAMIC;



create table bl_financial_settlement(
    id   bigint auto_increment comment '主键' primary key,
    settlement_document_code  varchar(64) UNIQUE   null comment '结算单编号',
    settlement_entity   bigint    null comment '结算主体',
    settlement_status     varchar(10)     default '1' comment '结算状态',
    status     varchar(10)     default '1' comment '审批状态',
    settlement_amount    decimal(18,2)    null comment '本次结算金额',
    financial_receipts_amount       decimal(18,2)    null comment '财务收款总额',
    unsettled_amount       decimal(18,2)    null comment '剩余未结算金额',
    process_id        varchar(64)                        null comment '流程id',
    create_by         varchar(64)                        null comment ' 创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by         varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '不良系统-财务结算单' row_format = DYNAMIC;

create table bl_financial_settlement_reconciliation(
    id   bigint auto_increment comment '主键' primary key,
    financial_settlement_id  bigint    null comment '财务结算单id',
    reconciliation_id  bigint    null comment '财务结算单id',
    reconciliation_code  varchar(64)    null comment '对账单单号',
    reconciliation_date     datetime     null comment  '对账日',
    total_amount_collected   decimal(18,2)  comment '财务收款总额'
) comment '不良系统-财务结算单-业务对账单' row_format = DYNAMIC;


create table bl_financial_settlement_payment(
     id   bigint auto_increment comment '主键' primary key,
     financial_settlement_id  bigint    null comment '财务结算单id',
     payment_date     datetime     null comment  '日期',
     payment_amount  decimal(18,2)  comment '金额',
     remark  varchar(200)     comment '备注',
     payment_state  varchar(10) comment  '付款状态'
) comment '不良系统-财务结算单-付款明细' row_format = DYNAMIC;


create table bl_notify
(
    id              bigint auto_increment comment '主键'
        primary key,
    notify_module   varchar(50)                          null comment '通知模块',
    url             varchar(50)                          null comment '相关url',
    notify_type     char                                 null comment '通知类型 0通知 1待办',
    notify_msg      varchar(500)                         null comment '通知内容',
    dispose_user    bigint                               null comment '待处理人id',
    view_flag       char                                 null comment '阅读状态 0未阅 1已阅',
    status          char                                 null comment '状态 0正常 1禁用',
    remind_text     varchar(600)                         null comment '提醒正文',
    correlation_id  bigint                               null comment '关联id',
    create_by       varchar(64)                          null comment '创建者',
    create_time     datetime   default CURRENT_TIMESTAMP null comment '创建时间',
    update_by       varchar(64)                          null comment '更新人',
    update_time     datetime                             null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '不良提醒表' row_format = DYNAMIC;


INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('不良系统', 0, 22, 'badSystem', null, null, 1, 0, 'M', '0', '0', '', 'bug', 'admin', '2024-11-29 11:26:07', 'admin', '2024-11-29 16:44:45', '');
SELECT @不良系统 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('资产管理', @不良系统, 3, 'assetManagement', 'badSystem/assetManagement/index.vue', null, 1, 0, 'C', '0', '0', '', 'list', 'admin', '2024-11-29 16:35:42', 'admin', '2024-12-13 14:41:10', '');
SELECT @资产管理 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('借据导入', @资产管理, 1, '', null, null, 1, 0, 'F', '0', '0', 'bad:assetManagement:import', '#', 'admin', '2024-11-29 16:37:08', 'admin', '2024-11-29 16:43:02', '');
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('委后还款明细', @不良系统, 4, 'repaymentDetailsAfter', 'badSystem/repaymentDetailsAfter/index.vue', null, 1, 0, 'C', '0', '0', '', 'time', 'admin', '2024-12-03 09:06:50', 'admin', '2024-12-13 14:41:21', '');
SELECT @委后还款明细 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('导入还款明细', @委后还款明细, 1, '', null, null, 1, 0, 'F', '0', '0', 'bad:repaymentDetailsAfter:import', '#', 'admin', '2024-12-03 09:07:40', 'admin', '2024-12-03 09:07:53', '');
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('金融产品配置', @不良系统, 1, 'financialProductConfiguration', 'badSystem/financialProductConfiguration/index.vue', '', 1, 0, 'C', '0', '0', '', 'documentation', 'admin', '2024-12-03 10:40:45', 'admin', '2024-12-13 14:51:11', '');
SELECT @金融产品配置 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('新增产品', @金融产品配置, 1, '', null, null, 1, 0, 'F', '0', '0', 'badSystem:financialProductConfiguration:add', '#', 'admin', '2024-12-03 10:51:20', 'admin', '2024-12-13 14:49:10', '');
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('借据导入规则', @不良系统, 2, 'importPromissoryNotes', 'badSystem/importPromissoryNotes/index.vue', null, 1, 0, 'C', '0', '0', '', 'input', 'admin', '2024-12-03 15:17:10', 'admin', '2024-12-13 14:51:24', '');
SELECT @借据导入规则 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('创建借据导入规则', @借据导入规则, 1, '', null, null, 1, 0, 'F', '0', '0', 'badSystem:importPromissoryNotes:add', '#', 'admin', '2024-12-03 15:18:43', 'admin', '2024-12-13 14:49:02', '');
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('机构管理', @不良系统, 8, 'organizationalManagement', 'badSystem/organizationalManagement/index.vue', null, 1, 1, 'C', '0', '0', '', 'monitor', 'admin', '2024-12-09 11:27:02', 'admin', '2024-12-20 09:12:06', '');
SELECT @机构管理 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('创建机构', @机构管理, 1, '', null, null, 1, 0, 'F', '0', '0', 'bad:organizationalManagement:add', '#', 'admin', '2024-12-09 11:44:40', 'admin', '2024-12-09 11:44:51', '');
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('委外分案', @不良系统, 5, 'outsourced', 'badSystem/outsourced/index.vue', null, 1, 1, 'C', '0', '0', '', 'drag', 'admin', '2024-12-12 09:09:46', 'admin', '2024-12-30 15:02:17', '');
SELECT @委外分案 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('创建分案', @委外分案, 1, '', null, null, 1, 0, 'F', '0', '0', 'bad:outsourecd:add', '#', 'admin', '2024-12-12 09:17:13', '', null, '');
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('渠道业务对账单', @不良系统, 6, 'channelBusiness', 'badSystem/channelBusiness/index.vue', null, 1, 1, 'C', '0', '0', '', 'example', 'admin', '2024-12-16 11:32:05', 'admin', '2024-12-30 15:02:13', '');
SELECT @渠道业务对账单 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('创建对账单', @渠道业务对账单, 1, '', null, null, 1, 0, 'F', '0', '0', 'bad:channelBusiness:add', '#', 'admin', '2024-12-16 11:33:00', '', null, '');
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('财务结算单', @不良系统, 7, 'financialSettlement', 'badSystem/financialSettlement/index.vue', null, 1, 1, 'C', '0', '0', '', 'druid', 'admin', '2024-12-16 16:52:02', 'admin', '2024-12-30 15:02:09', '');
SELECT @财务结算单 := LAST_INSERT_ID();
INSERT INTO sys_menu ( menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES ('创建结算单', @财务结算单, 1, '', null, null, 1, 0, 'F', '0', '0', 'bad:financialSettlement:add', '#', 'admin', '2024-12-16 16:53:18', '', null, '');


INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (26, '不良系统机构审核', 'blMechanism', 'oaModule_type', null, null, 'default', 'N', '0', 'admin', '2024-12-31 13:49:50', 'admin', '2024-12-31 14:24:28', null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (27, '不良系统委外分案审核', 'blOutsourcedProject', 'oaModule_type', null, null, 'default', 'N', '0', 'admin', '2025-01-02 09:30:51', '', null, null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (28, '不良系统渠道业务对账单', 'blChannelBusinessReconciliation', 'oaModule_type', null, null, 'default', 'N', '0', 'admin', '2025-01-02 14:11:25', 'admin', '2025-01-02 14:11:37', null);
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, auxiliary_field, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) VALUES (29, '财务结算单模板', 'blFinancialSettlement', 'oaModule_type', null, null, 'default', 'N', '0', 'admin', '2025-01-02 16:56:20', 'admin', '2025-01-02 16:56:26', null);


alter table oa_process_template
    modify oa_module_type varchar(100) null comment 'oa模块管理';

alter table oa_process_template_his
    modify oa_module_type varchar(100) null comment 'oa模块管理';
