CREATE TABLE `cdlb_project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `cust_id` varchar(100) NOT NULL COMMENT '担保公司id',
  `cust_name` varchar(100) NOT NULL COMMENT '担保公司名称',
  `in_apply_count` int(11) NOT NULL DEFAULT '0' COMMENT '入库申请中绿本数量',
  `in_ok_count` int(11) NOT NULL DEFAULT '0' COMMENT '已入库绿本数量',
  `out_apply_count` int(11) NOT NULL DEFAULT '0' COMMENT '出库审核中绿本数量',
  `out_audit_count` int(11) NOT NULL DEFAULT '0' COMMENT '出库申请中绿本数量',
  `out_ok_count` int(11) NOT NULL DEFAULT '0' COMMENT '已出库绿本数量',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车贷绿本管理主表';


CREATE TABLE `cdlb_project_dynamic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '车贷绿本管理表主键',
  `dynamic_title` varchar(200) DEFAULT '' COMMENT '动态标题',
  `dynamic_msg` text  COMMENT '动态内容',
  `dynamic_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '动态时间',
  `apply_flag` char(2) NOT NULL COMMENT '出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回',
  `oper_id` bigint(20) DEFAULT null COMMENT '操作人员id',
  `oper_name` varchar(64) DEFAULT '' COMMENT '操作人员姓名',
  `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
  `status` int(1) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` text  COMMENT '错误消息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车贷绿本管理-动态表';


CREATE TABLE `cdlb_project_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '车贷绿本管理表主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `user_flag` char(1) NOT NULL COMMENT '用户标识：0项目经理1风险经理2综合管理办公室主任',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车贷绿本管理-成员表';



CREATE TABLE `cdlb_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '车贷绿本管理表主键',
  `project_in_id` bigint(20)  NULL  COMMENT '车贷绿本管理-入库申请表主键',
  `project_out_id` bigint(20)  NULL  COMMENT '车贷绿本管理-出库申请表主键',
  `contract_code` varchar(100)  NULL COMMENT '合同编号',
  `client_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `client_card_id` varchar(20) NOT NULL COMMENT '身份证号码',
  `loan_binding` char(1) NOT NULL DEFAULT 'N' COMMENT '是否绑定借据 Y是 N否',
  `loan_no` varchar(100) NOT NULL DEFAULT 'N' COMMENT '已绑定的借据申请编号,未绑定时默认为N',
  `mail_date` date NOT NULL COMMENT '邮寄日期',
  `lb_flag` char(2) NOT NULL COMMENT '绿本状态标识：10入库录入11入库申请12入库登记13入库完成20出库申请21出库审核22出库登记23出库完成',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车贷绿本信息表';



CREATE TABLE `cdlb_in_out_apply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '车贷绿本管理表主键',
  `garage_state` char(2) NOT NULL COMMENT '出入库状态',
  `user11_id` bigint(20) NOT NULL COMMENT '入库申请人id',
  `user12_id` bigint(20) NOT NULL COMMENT '入库登记人id',
  `user21_id` bigint(20) NOT NULL COMMENT '出库申请人id',
  `user22_id` bigint(20) NOT NULL COMMENT '出库审核人id',
  `user23_id` bigint(20) NOT NULL COMMENT '出库登记人id',
  `remark` text  NULL COMMENT '出入库申请说明',
  `apply_flag` char(2) NOT NULL COMMENT '出入库审核状态标识：10入库录入11入库申请12入库登记13入库完成 19入库驳回 20出库申请21出库审核22出库登记23出库完成29出库驳回',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车贷绿本出入库申请表';



CREATE TABLE `cdlb_loan_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cdlb_binding` char(1) NOT NULL DEFAULT 'N' COMMENT '是否绑定绿本信息 Y是 N否',
  `cdlb_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '已绑定的绿本信息表ID,未绑定时默认为0',
  `platform_no` varchar(100) NOT NULL COMMENT '外部系统平台编码',
  `cust_no` varchar(100) NOT NULL COMMENT '担保公司编码',
  `partner_no` varchar(100) NOT NULL COMMENT '合作方编码',
  `fund_no` varchar(100) NOT NULL COMMENT '资金方编码',
  `product_no` varchar(100) NOT NULL COMMENT '产品编号',
  `loan_no` varchar(100) NOT NULL COMMENT '借据申请编号',
  `client_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `client_card_id` varchar(20) NOT NULL COMMENT '身份证号码',
  `client_card_address` varchar(500) NOT NULL COMMENT '身份证地址',
  `loan_status` varchar(20) NOT NULL COMMENT '借据状态',
  `apply_time` datetime NOT NULL COMMENT '进件时间',
  `loan_amt` decimal(17,2) NOT NULL COMMENT '借款金额（元）',
  `balance_amt` decimal(17,2) NOT NULL COMMENT '在贷余额（元）',
  `total_term` int(10) NOT NULL COMMENT '借款期限（期数）',
  `loan_time` datetime NOT NULL COMMENT '放款时间',
  `due_date` date NOT NULL COMMENT '到期日期',
  `loan_req_no` varchar(100) NOT NULL COMMENT '放款流水号',
  `repay_way` varchar(50) NOT NULL COMMENT '还款方式',
  `loan_use` varchar(50) NOT NULL COMMENT '借款用途',
  `car_brand_name` varchar(20) NOT NULL COMMENT '车辆品牌',
  `car_no` varchar(50) NOT NULL COMMENT '车牌号',
  `car_vin` varchar(50) NOT NULL COMMENT '车架号',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车贷借据信息表';