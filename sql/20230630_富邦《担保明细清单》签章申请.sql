CREATE TABLE `esign_apply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `apply_type` varchar(20) NOT NULL COMMENT '申请签署类型说明 如富邦银行',
  `file_month` varchar(7) NOT NULL COMMENT '担保明细清单月份',
  `real_file_name` varchar(200) DEFAULT NULL COMMENT '实际文件名称',
  `file_name` varchar(200) DEFAULT NULL COMMENT '原文件名称',
  `file_data_size` int(11) DEFAULT NULL COMMENT '数据量',
  `file_page` int(11) DEFAULT NULL COMMENT '文件页数',
  `file_path_excel` varchar(255) DEFAULT NULL COMMENT '上传的Excel文件存放路径',
  `file_path_pdf` varchar(255) DEFAULT NULL COMMENT '生成的pdf文件存放路径',
  `file_path_esign` varchar(255) DEFAULT NULL COMMENT '签章的pdf文件存放路径',
  `flow_status` char(1) NOT NULL DEFAULT '0' COMMENT '申请状态：0申请人待办 1数据校验 2业务总监待办 3风险总监待办 4综合管理部总监待办 5文件待签章 6档案管理员待办 8已完成结束 9审核不通过终止',
  `esign_falg` char(1) NOT NULL DEFAULT 'N' COMMENT '是否签署完成 （是：Y，否：N）',
  `esign_time` datetime DEFAULT NULL COMMENT '签署完成时间',
  `apply_user` varchar(64) NOT NULL COMMENT '申请签章人',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请签章时间',
  `apply_remark` varchar(500) DEFAULT NULL COMMENT '申请签章说明',
  `yw_master_user` varchar(64) DEFAULT NULL COMMENT '业务总监',
  `yw_master_time` datetime DEFAULT NULL COMMENT '业务总监审核时间',
  `yw_master_remark` varchar(500) DEFAULT NULL COMMENT '业务总监审核意见',
  `fx_master_user` varchar(64) DEFAULT NULL COMMENT '风险总监',
  `fx_master_time` datetime DEFAULT NULL COMMENT '风险总监审核时间',
  `fx_master_remark` varchar(500) DEFAULT NULL COMMENT '风险总监审核意见',
  `mg_master_user` varchar(64) DEFAULT NULL COMMENT '综合管理部总监',
  `mg_master_time` datetime DEFAULT NULL COMMENT '综合管理部总监审核时间',
  `mg_master_remark` varchar(500) DEFAULT NULL COMMENT '综合管理部总监审核意见',
  `mg_record_user` varchar(64) DEFAULT NULL COMMENT '档案管理员',
  `mg_record_time` datetime DEFAULT NULL COMMENT '档案管理员审核时间',
  `mg_record_remark` varchar(500) DEFAULT NULL COMMENT '档案管理员审核意见',
  `stop_user` varchar(64) DEFAULT NULL COMMENT '终止人',
  `stop_time` datetime DEFAULT NULL COMMENT '终止时间',
  `stop_remark` varchar(500) DEFAULT NULL COMMENT '终止原因',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='富邦《担保明细清单》签章申请表';










-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('富邦《担保明细清单》签章申请', '3', '9', 'esignapply', 'core/esignapply/index', 1, 0, 'C', '0', '0', 'core:esignapply:list', '#', 'admin', sysdate(), '', null, '富邦《担保明细清单》签章申请菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('查看', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('业务总监审核', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:edit2',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('风险总监审核', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:edit3',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('综合管理部总监审核', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:edit4',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('档案管理员归档', @parentId, '6',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:edit6',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('终止申请审批权限', @parentId, '7',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:edit9',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('待签章文件下载', @parentId, '8',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:downloadPdf',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('已签章文件下载', @parentId, '9',  '#', '', 1, 0, 'F', '0', '0', 'core:esignapply:downloadPdfSign',       '#', 'admin', sysdate(), '', null, '');






























