CREATE TABLE `proc_workflow_approval_record` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `business_key` varchar(64) NOT NULL COMMENT '事务key+',
  `step_id` varchar(32) NOT NULL COMMENT '节点id',
  `task_node_name` varchar(64) DEFAULT NULL COMMENT '任务节点名称',
  `pass` varchar(10) DEFAULT NULL COMMENT '1同意、2不同意、4驳回、5转办、6加签',
  `create_by` varchar(64) CHARACTER SET utf16 DEFAULT '' COMMENT '创建者',
  `detail_by` varchar(64) DEFAULT '' COMMENT '实际处理者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_business_key` (`business_key`,`step_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审批节点流向记录表';