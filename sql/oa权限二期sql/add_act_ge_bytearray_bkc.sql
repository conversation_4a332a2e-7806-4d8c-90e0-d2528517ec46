-- 备份流程定义到表act_ge_bytearray_bkc
DROP TABLE IF EXISTS act_ge_bytearray_bkc;
CREATE TABLE act_ge_bytearray_bkc like act_ge_bytearray; 
insert into act_ge_bytearray_bkc select DISTINCT a.* from act_ge_bytearray a INNER JOIN oa_process_template_his b on a.DEPLOYMENT_ID_ = b.flow_id;

-- ----------------------------
-- Table structure for sys_post_mapping
-- ----------------------------
DROP TABLE IF EXISTS `sys_post_mapping`;
CREATE TABLE `sys_post_mapping`  (
  `post_code_old` varchar(64) NOT NULL COMMENT '需要被替换的岗位code',
  `post_code_new` varchar(64) NOT NULL COMMENT '新的岗位code',
  PRIMARY KEY (`post_code_old`) USING BTREE
) ENGINE = InnoDB COMMENT = '岗位code新旧对应表' ROW_FORMAT = Dynamic;
