<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<parent>
		<groupId>com.ruoyi</groupId>
		<artifactId>ruoyi</artifactId>
		<version>3.8.1</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>ruoyi-core</artifactId>
	<description>
        core核心数据平台模块
    </description>
	<dependencies>

		<!-- 通用工具 -->
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-financial</artifactId>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>30.1.1-jre</version>
		</dependency>
		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-system</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-core</artifactId>
			<version>5.8.11</version>
		</dependency>




		<!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.8.0</version>
        </dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.5</version>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
			<version>3.3.0</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
		</dependency>

		<dependency>
			<groupId>org.activiti</groupId>
			<artifactId>activiti-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.activiti.dependencies</groupId>
			<artifactId>activiti-dependencies</artifactId>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>com.ruoyi</groupId>
			<artifactId>ruoyi-common</artifactId>
		</dependency>
		<dependency>
			<groupId>org.docx4j</groupId>
			<artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
			<version>8.3.4</version>
		</dependency>
		<dependency>
			<groupId>org.docx4j</groupId>
			<artifactId>docx4j-ImportXHTML</artifactId>
			<version>6.1.0</version>
		</dependency>
		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>2.3.1</version> <!-- 旧版需要JAXB API -->
		</dependency>
	</dependencies>
</project>
