package org.ruoyi.core.cwproject.domain.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 财务项目管理-完结项目归档查询详情 CwProjectOverDetailDto
 *
 * <AUTHOR>
 * @date 2022-11-22
 */
@Getter
@Setter
@ToString
public class CwProjectOverDetailDto extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 业务期次 */
    @Excel(name = "业务期次")
    private String termMonth;

    /** 收入 */
    @Excel(name = "收入")
    private BigDecimal incomeAmt;

    /** 出返费公司 */
    @Excel(name = "出返费公司")
    private List<String> custName;

    /** 返费公司 */
    @Excel(name = "返费公司")
    private List<String> feeCustName;

    /** 返费 */
    @Excel(name = "返费", isStatistics = true)
    private List<BigDecimal> feeAmt;

    /** 提成返费 */
    @Excel(name = "提成返费", isStatistics = true)
    private List<BigDecimal> feeAmt2;

    /** 毛利 */
    @Excel(name = "毛利")
    private BigDecimal grossProfitAmt;

    /** 提成毛利 */
    @Excel(name = "提成毛利")
    private BigDecimal grossProfitAmt2;

    /** 返费已结清 */
    @Excel(name = "返费已结清")
    private BigDecimal feeAmtAlready;

    /** 返费未结清 */
    @Excel(name = "返费未结清")
    private BigDecimal feeAmtNoAlready;

    /** 期次状态 */
    @Excel(name = "期次状态")
    private String phaseStatus;

    /** 打款状态 */
    @Excel(name = "打款状态")
    private List<String> payStatus;

    /** 打款日期 */
    @Excel(name = "打款日期")
    private List<String> payDate;

    /** 实际打款金额 */
    @Excel(name = "实际打款金额")
    private List<BigDecimal> payAmt;

    /** 抹平差额 */
    @Excel(name = "抹平差额")
    private List<BigDecimal> differenceAmt;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
