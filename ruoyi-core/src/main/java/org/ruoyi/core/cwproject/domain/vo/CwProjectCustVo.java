package org.ruoyi.core.cwproject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 财务项目管理四期 - 编辑项目页面 - 普通项目 - 返费公司与费率对象
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CwProjectCustVo {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String custName;

    private BigDecimal rate;

    private BigDecimal taxRate;

    private int schemeFlag;

    private Long oaTraderId;

    private String replaceFlag;

    private int schemeFlagUseSituation;
}
