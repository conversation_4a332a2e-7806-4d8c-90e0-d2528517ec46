package org.ruoyi.core.cwproject.mapper;

import org.ruoyi.core.cwproject.domain.CwProjectRejection;

import java.util.List;

/**
 * 财务项目管理驳回Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
public interface CwProjectRejectionMapper
{
    /**
     * 查询财务项目管理驳回
     *
     * @param id 财务项目管理驳回主键
     * @return 财务项目管理驳回
     */
    public CwProjectRejection selectCwProjectRejectionById(Long id);

    /**
     * 查询财务项目管理驳回列表
     *
     * @param cwProjectRejection 财务项目管理驳回
     * @return 财务项目管理驳回集合
     */
    public List<CwProjectRejection> selectCwProjectRejectionList(CwProjectRejection cwProjectRejection);

    /**
     * 新增财务项目管理驳回
     *
     * @param cwProjectRejection 财务项目管理驳回
     * @return 结果
     */
    public int insertCwProjectRejection(CwProjectRejection cwProjectRejection);

    /**
     * 修改财务项目管理驳回
     *
     * @param cwProjectRejection 财务项目管理驳回
     * @return 结果
     */
    public int updateCwProjectRejection(CwProjectRejection cwProjectRejection);

    /**
     * 删除财务项目管理驳回
     *
     * @param id 财务项目管理驳回主键
     * @return 结果
     */
    public int deleteCwProjectRejectionById(Long id);

    //普通项目 - 使用收入表id查对应的驳回信息
    List<CwProjectRejection> selectCwProjectRejectionByProjectIncomeId(Long projectIncomeId);
}
