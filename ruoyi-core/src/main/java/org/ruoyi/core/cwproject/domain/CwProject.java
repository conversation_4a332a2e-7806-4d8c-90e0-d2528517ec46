package org.ruoyi.core.cwproject.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
/**
 * 财务项目管理主对象 cw_project
 *
 * <AUTHOR>
 * @date 2022-11-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CwProject extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目类型 */
    private String projectType;
    private String projectTypeChinese;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 担保公司 */
    @Excel(name = "担保公司")
    private String custName;

    /** 汇款公司 */
    @Excel(name = "汇款公司")
    private String incomeCustName;

    /** 项目状态：0正常 1终止 */
    @Excel(name = "项目状态：0正常 1终止")
    private String projectFlag;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    private String prestoreIncomeFlag;

    //预存收入
    private String prestoreIncome;

    //关联的OA项目id
    private Long oaProjectDeployId;

    //是否生成记账凭证 0-否，1-是
    private String generateCertificateFlag;

    //凭证所属账套id
    private Long accountSetsId;

    //担保费收入类型 0-直接收款，1-混合平台收益
    private String guaranteeIncomeType;

    //担保费收款方
    private Long guarantyPayee;

    //收款人简称（页面展示用）
    private String payeeAbbreviation;

    //项目和类型关系表主键（新权限，一个项目可以有多个不同的类型）
    private Long projectTypeRelevanceTypeId;

    //多选查询
    @JSONField(serialize = false, deserialize = false)
    private List<String> projectTypes;

    private Long projectTypeId;

    private String projectPortfolioCode;
}
