package org.ruoyi.core.cwproject.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.AuthModuleEnum;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.impl.NewAuthorityServiceImpl;
import org.ruoyi.core.cwproject.domain.CwProjectIncome;
import org.ruoyi.core.cwproject.service.ICwProjectIncomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-收入Controller
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@RestController
@RequestMapping("/cwxmgl/income")
public class CwProjectIncomeController extends BaseController
{
    @Autowired
    private ICwProjectIncomeService cwProjectIncomeService;

    @Autowired
    private NewAuthorityServiceImpl newAuthorityService;

    /**
     * 查询财务项目管理-收入列表
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:income:list')")
    @GetMapping("/list")
    public TableDataInfo list(CwProjectIncome cwProjectIncome)
    {
        startPage();
        List<CwProjectIncome> list = cwProjectIncomeService.selectCwProjectIncomeList(cwProjectIncome);
        return getDataTable(list);
    }
    @PreAuthorize("@ss.hasPermi('cwxmgl:income:list')")
    @PostMapping("/list")
    public List<CwProjectIncome> list()
    {
        List<CwProjectIncome> list = cwProjectIncomeService.selectCwProjectIncomeListAll();
        return list;
    }

    /**
     * 导出财务项目管理-收入列表
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:income:export')")
    @Log(title = "财务项目管理-收入", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CwProjectIncome cwProjectIncome)
    {
        List<CwProjectIncome> list = cwProjectIncomeService.selectCwProjectIncomeList(cwProjectIncome);
        ExcelUtil<CwProjectIncome> util = new ExcelUtil<CwProjectIncome>(CwProjectIncome.class);
        util.exportExcel(response, list, "财务项目管理-收入数据");
    }

    /**
     * 获取财务项目管理-收入详细信息
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:income:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(cwProjectIncomeService.selectCwProjectIncomeById(id));
    }

    /**
     * 新增财务项目管理-收入
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:income:add')")
    @Log(title = "财务项目管理-收入", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CwProjectIncome cwProjectIncome)
    {
        return toAjax(cwProjectIncomeService.insertCwProjectIncome(cwProjectIncome));
    }

    /**
     * 修改财务项目管理-收入
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:income:edit')")
    @Log(title = "财务项目管理-收入", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CwProjectIncome cwProjectIncome)
    {
        return toAjax(cwProjectIncomeService.updateCwProjectIncome(cwProjectIncome));
    }

    /**
     * 删除财务项目管理-收入
     */
    @PreAuthorize("@ss.hasPermi('cwxmgl:income:remove')")
    @Log(title = "财务项目管理-收入", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cwProjectIncomeService.deleteCwProjectIncomeByIds(ids));
    }

    /**
     * 查询财务项目管理-待录入收入状态
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:income:list:zero')")
    @GetMapping("/list/flagzero")
    public TableDataInfo listFlagZero(CwProjectIncome cwProjectIncome)
    {
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(userId, AuthModuleEnum.FINANCEPROJ.getCode());
        startPage();
        List<Map<String, Object>> data = cwProjectIncomeService.selectCwProjectIncomeListFlagZero(cwProjectIncome, userId, loginUser, projectIds);
        return getPageDataTable(data);
    }

    /**
     * 查询财务项目管理-已录入未确认收入状态
     */
    // @PreAuthorize("@ss.hasPermi('cwxmgl:income:list:one')")
    @GetMapping("/list/flagone")
    public TableDataInfo listFlagOne(CwProjectIncome cwProjectIncome)
    {
        LoginUser loginUser = getLoginUser();
        Long userId = loginUser.getUserId();
        List<Long> projectIds = newAuthorityService.getNewAuthorityForModuleTypeByUserIdAndModuleType(userId, AuthModuleEnum.FINANCEPROJ.getCode());
        startPage();
        List<Map<String, Object>> data = cwProjectIncomeService.selectCwProjectIncomeListFlagOne(cwProjectIncome, userId, loginUser, projectIds);
        return getPageDataTable(data);
    }

    @PostMapping("/fileUpload")
    public void fileUpload(MultipartFile file) throws Exception {
        cwProjectIncomeService.importExcelOfCwProject(file);
    }
}
