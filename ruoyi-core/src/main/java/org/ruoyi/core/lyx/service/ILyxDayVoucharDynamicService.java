package org.ruoyi.core.lyx.service;

import org.ruoyi.core.lyx.domain.LyxDayVoucharDynamic;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ILyxDayVoucharDynamicService.java
 * @Description TODO
 * @createTime 2024年03月14日 15:54:00
 */
public interface ILyxDayVoucharDynamicService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxDayVoucharDynamic selectLyxDayVoucharDynamicById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param lyxDayVoucharDynamic 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<LyxDayVoucharDynamic> selectLyxDayVoucharDynamicList(LyxDayVoucharDynamic lyxDayVoucharDynamic);

    /**
     * 新增【请填写功能名称】
     *
     * @param lyxDayVoucharDynamic 【请填写功能名称】
     * @return 结果
     */
    public int insertLyxDayVoucharDynamic(LyxDayVoucharDynamic lyxDayVoucharDynamic);

    /**
     * 修改【请填写功能名称】
     *
     * @param lyxDayVoucharDynamic 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxDayVoucharDynamic(LyxDayVoucharDynamic lyxDayVoucharDynamic);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxDayVoucharDynamicByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxDayVoucharDynamicById(Long id);
}
