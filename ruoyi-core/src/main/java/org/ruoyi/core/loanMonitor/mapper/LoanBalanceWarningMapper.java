package org.ruoyi.core.loanMonitor.mapper;

import java.util.List;

import org.ruoyi.core.loanMonitor.domain.LoanBalanceWarning;
import org.ruoyi.core.loanMonitor.domain.vo.MarginMonitoringVo;
import org.ruoyi.core.qiyeVX.domain.VxUser;

/**
 * 在贷余额监控预警Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface LoanBalanceWarningMapper 
{
    /**
     * 查询在贷余额监控预警
     * 
     * @param projectCode 项目唯一编码
     * @return 在贷余额监控预警
     */
    public LoanBalanceWarning selectLoanBalanceWarningById(Integer projectCode);

    /**
     * 查询在贷余额监控列表
     * 
     * @param marginMonitoringVo
     * @return 在贷余额监控列表
     */
    public List<MarginMonitoringVo> selectMarginMonitoringList(MarginMonitoringVo marginMonitoringVo);

    /**
     * 新增在贷余额监控预警
     * 
     * @param loanBalanceWarning 在贷余额监控预警
     * @return 结果
     */
    public int insertLoanBalanceWarning(LoanBalanceWarning loanBalanceWarning);

    /**
     * 修改在贷余额监控预警
     * 
     * @param loanBalanceWarning 在贷余额监控预警
     * @return 结果
     */
    public int updateLoanBalanceWarning(LoanBalanceWarning loanBalanceWarning);

    /**
     * 根据项目唯一编码查询预警数据
     * @param projectCode
     * @return
     */
    // @DataSource(DataSourceType.SPARK)
    LoanBalanceWarning selectBalanceLoanByProjectCode(Integer projectCode);

    /**
     * 根据用户账号查询企业微信信息
     * @param userName 用户账号
     */
    VxUser selectUserQYWXInfoByUserName(String userName);
}
