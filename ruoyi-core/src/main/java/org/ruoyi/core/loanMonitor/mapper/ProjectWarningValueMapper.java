package org.ruoyi.core.loanMonitor.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.loanMonitor.domain.ProjectWarningValue;

import java.util.List;

/**
 * 保证金预警阈值Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
@Mapper
public interface ProjectWarningValueMapper 
{

    /**
     * 查询保证金预警阈值列表
     * @return 保证金预警阈值集合
     */
    public ProjectWarningValue selectProjectWarningValueList(@Param("productCode") String productCode, @Param("projectCode") Integer projectCode);

}
