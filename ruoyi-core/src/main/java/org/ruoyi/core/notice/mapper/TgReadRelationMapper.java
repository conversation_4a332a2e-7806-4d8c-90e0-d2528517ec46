package org.ruoyi.core.notice.mapper;


import com.ruoyi.common.core.domain.entity.SysUser;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.notice.domain.TgNoticeMain;
import org.ruoyi.core.notice.domain.TgReadRelation;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;

import java.util.List;

/**
 * 公告-已读/未读Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
public interface TgReadRelationMapper 
{
    /**
     * 查询公告-已读/未读
     * 
     * @param noticeId 公告-已读/未读主键
     * @return 公告-已读/未读
     */
    public TgReadRelation selectTgReadRelationByNoticeId(Long noticeId);

    /**
     * 查询公告-已读/未读列表
     * 
     * @param tgReadRelation 公告-已读/未读
     * @return 公告-已读/未读集合
     */
    public List<TgReadRelation> selectTgReadRelationList(TgReadRelation tgReadRelation);

    /**
     * 新增公告-已读/未读
     * 
     * @param tgReadRelation 公告-已读/未读
     * @return 结果
     */
    public int insertTgReadRelation(TgReadRelation tgReadRelation);

    /**
     * 修改公告-已读/未读
     * 
     * @param tgReadRelation 公告-已读/未读
     * @return 结果
     */
    public int updateTgReadRelation(TgReadRelation tgReadRelation);

    /**
     * 删除公告-已读/未读
     * 
     * @param noticeId 公告-已读/未读主键
     * @return 结果
     */
    public int deleteTgReadRelationByNoticeId(Long noticeId);

    /**
     * 批量删除公告-已读/未读
     * 
     * @param noticeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTgReadRelationByNoticeIds(Long[] noticeIds);

    /**
     * 根据公司查询所有用户
     * @param companyId
     * @return
     */
    List<SysUser> selectUserListByCompanyId(Long companyId);

    /**
     * 批量插入已读/未读表
     * @param readRelationList
     */
    void batchInsert(@Param("readRelationList") List<TgReadRelation> readRelationList);

    /**
     * 根据通用首选-公司-公告管理获取公司下的所有用户
     * @param moduleType 模块
     * @param roleType 角色
     * @param companyId 公司
     * @return
     */
    List<AuthDetailVo> selectAuthCompanyUsers(@Param("moduleType") String moduleType, @Param("roleType") String roleType, @Param("companyId") Long companyId);

    /**
     * 根据公司id查询该公司下有哪些已发布的公告
     * @param companyId
     * @return
     */
    List<TgNoticeMain> selectLaunchedNoticesByCompanyId(@Param("companyIds") List<Long> companyId);

    /**
     * 根据公告id和用户id批量删除已读未读表中数据
     * @param noticeIdList
     * @param userId
     */
    void deleteTgReadRelationByNoticeIdListAndUserId(@Param("noticeIdList") List<Long> noticeIdList, @Param("userId") Long userId);
}
