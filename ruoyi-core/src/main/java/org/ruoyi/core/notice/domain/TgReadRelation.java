package org.ruoyi.core.notice.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 公告-已读/未读对象 tg_read_relation
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
public class TgReadRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 公告id */
    @Excel(name = "公告id")
    private Long noticeId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 状态(0未读 1已读) */
    @Excel(name = "状态(0未读 1已读)")
    private String readType;

    /** 状态字典值 */
    private String readTypeLabel;

    /** 公司id集合 */
    private List<Long> companyIdList;

    /** 公告类型 */
    private String noticeType;

    /** 公告名称 */
    private String noticeName;

    /** 创建人姓名 */
    private String createNickName;

    /** 分页参数 */
    private Integer pageSize;

    /** 分页参数 */
    private Integer pageNum;

    /** 是否置顶(0否 1是) */
    private String isHeader;

    /** 是否重点(0否 1是) */
    private String isEmphasis;

    /** 版本号 */
    private Integer version;

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getIsHeader() {
        return isHeader;
    }

    public void setIsHeader(String isHeader) {
        this.isHeader = isHeader;
    }

    public String getIsEmphasis() {
        return isEmphasis;
    }

    public void setIsEmphasis(String isEmphasis) {
        this.isEmphasis = isEmphasis;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getReadTypeLabel() {
        return readTypeLabel;
    }

    public void setReadTypeLabel(String readTypeLabel) {
        this.readTypeLabel = readTypeLabel;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getNoticeName() {
        return noticeName;
    }

    public void setNoticeName(String noticeName) {
        this.noticeName = noticeName;
    }

    public String getCreateNickName() {
        return createNickName;
    }

    public void setCreateNickName(String createNickName) {
        this.createNickName = createNickName;
    }

    public List<Long> getCompanyIdList() {
        return companyIdList;
    }

    public void setCompanyIdList(List<Long> companyIdList) {
        this.companyIdList = companyIdList;
    }

    public void setNoticeId(Long noticeId)
    {
        this.noticeId = noticeId;
    }

    public Long getNoticeId() 
    {
        return noticeId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setReadType(String readType) 
    {
        this.readType = readType;
    }

    public String getReadType() 
    {
        return readType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("noticeId", getNoticeId())
            .append("userId", getUserId())
            .append("readType", getReadType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
