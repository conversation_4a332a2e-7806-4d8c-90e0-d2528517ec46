package org.ruoyi.core.badsystem.service;

import org.ruoyi.core.badsystem.domain.OutsourcedRepaymentDetail;
import org.ruoyi.core.badsystem.domain.vo.OutsourcedRepaymentDetailVo;

import java.util.List;

/**
 * 不良系统-委外还款明细Service接口
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
public interface IOutsourcedRepaymentDetailService
{
    /**
     * 查询不良系统-委外还款明细
     *
     * @param id 不良系统-委外还款明细主键
     * @return 不良系统-委外还款明细
     */
    public OutsourcedRepaymentDetail selectOutsourcedRepaymentDetailById(Long id);

    /**
     * 查询不良系统-委外还款明细列表
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 不良系统-委外还款明细集合
     */
    public List<OutsourcedRepaymentDetailVo> selectOutsourcedRepaymentDetailList(OutsourcedRepaymentDetail outsourcedRepaymentDetail);

    /**
     * 新增不良系统-委外还款明细
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 结果
     */
    public int insertOutsourcedRepaymentDetail(OutsourcedRepaymentDetail outsourcedRepaymentDetail);

    /**
     * 修改不良系统-委外还款明细
     *
     * @param outsourcedRepaymentDetail 不良系统-委外还款明细
     * @return 结果
     */
    public int updateOutsourcedRepaymentDetail(OutsourcedRepaymentDetail outsourcedRepaymentDetail);

    /**
     * 批量删除不良系统-委外还款明细
     *
     * @param ids 需要删除的不良系统-委外还款明细主键集合
     * @return 结果
     */
    public int deleteOutsourcedRepaymentDetailByIds(Long[] ids);

    /**
     * 删除不良系统-委外还款明细信息
     *
     * @param id 不良系统-委外还款明细主键
     * @return 结果
     */
    public int deleteOutsourcedRepaymentDetailById(Long id);

    public String importData(List<OutsourcedRepaymentDetailVo> assetManagementVos);
}
