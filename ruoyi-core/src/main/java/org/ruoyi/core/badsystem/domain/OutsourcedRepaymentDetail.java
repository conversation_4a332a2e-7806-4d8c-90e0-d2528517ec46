package org.ruoyi.core.badsystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 不良系统-委外还款明细对象 bl_outsourced_repayment_detail
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Data
public class OutsourcedRepaymentDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 借款单 */
    @Excel(name = "借款单")
    private String loanBill;

    /** 分案批次 */
    @Excel(name = "分案批次")
    private String outsourcedProjectNumber;

    /** 借款人姓名 */
    @Excel(name = "借款人姓名")
    private String borrowerName;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String phoneNumber;

    /** 还款金额 */
    @Excel(name = "还款金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal repaymentAmount;

    /** 还款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "还款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date repaymentTime;

    /** 流水号 */
    @Excel(name = "流水号")
    private String serialNumber;

    /** 受托机构 */
    //@Excel(name = "受托机构")
    private Long trusteeInstitutionId;

    /** 核对状态 */
    @Excel(name = "核对状态", readConverterExp = "1=未核对,2=已核对")
    private String checkStatus;

    /** 服务费用金额 */
    @Excel(name = "服务费用金额", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal serviceAmount;

    /** 分案批次List */
    private List<String> outsourcedProjectNumberList;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("loanBill", getLoanBill())
            .append("outsourcedProjectNumber", getOutsourcedProjectNumber())
            .append("borrowerName", getBorrowerName())
            .append("phoneNumber", getPhoneNumber())
            .append("repaymentAmount", getRepaymentAmount())
            .append("repaymentTime", getRepaymentTime())
            .append("serialNumber", getSerialNumber())
            .append("trusteeInstitutionId", getTrusteeInstitutionId())
            .append("checkStatus", getCheckStatus())
            .append("serviceAmount", getServiceAmount())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
