package org.ruoyi.core.badsystem.service;

import org.ruoyi.core.badsystem.domain.MechanismFinanceAccount;

import java.util.List;

/**
 * 机构-结算账号明细Service接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface IMechanismFinanceAccountService
{
    /**
     * 查询机构-结算账号明细
     *
     * @param id 机构-结算账号明细主键
     * @return 机构-结算账号明细
     */
    public MechanismFinanceAccount selectMechanismFinanceAccountById(Long id);

    /**
     * 查询机构-结算账号明细列表
     *
     * @param mechanismFinanceAccount 机构-结算账号明细
     * @return 机构-结算账号明细集合
     */
    public List<MechanismFinanceAccount> selectMechanismFinanceAccountList(MechanismFinanceAccount mechanismFinanceAccount);

    /**
     * 新增机构-结算账号明细
     *
     * @param mechanismFinanceAccount 机构-结算账号明细
     * @return 结果
     */
    public int insertMechanismFinanceAccount(MechanismFinanceAccount mechanismFinanceAccount);

    /**
     * 修改机构-结算账号明细
     *
     * @param mechanismFinanceAccount 机构-结算账号明细
     * @return 结果
     */
    public int updateMechanismFinanceAccount(MechanismFinanceAccount mechanismFinanceAccount);

    /**
     * 批量删除机构-结算账号明细
     *
     * @param ids 需要删除的机构-结算账号明细主键集合
     * @return 结果
     */
    public int deleteMechanismFinanceAccountByIds(Long[] ids);

    /**
     * 删除机构-结算账号明细信息
     *
     * @param id 机构-结算账号明细主键
     * @return 结果
     */
    public int deleteMechanismFinanceAccountById(Long id);

    /**
     * 批量新增机构-结算账号明细
     *
     * @param  mechanismFinanceAccountList
     * @return 结果
     */
    public int batchMechanismFinanceAccount(List<MechanismFinanceAccount> mechanismFinanceAccountList);

    public int deleteByMechanismId(Long mechanismId);
}
