package org.ruoyi.core.cdlb.service.impl;


import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.cdlb.domain.CdlbLoanInfo;
import org.ruoyi.core.cdlb.mapper.CdlbLoanInfoMapper;
import org.ruoyi.core.cdlb.service.ICdlbLoanInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 车贷借据信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
@Service
public class CdlbLoanInfoServiceImpl implements ICdlbLoanInfoService
{
    @Autowired
    private CdlbLoanInfoMapper cdlbLoanInfoMapper;

    /**
     * 查询车贷借据信息
     * 
     * @param id 车贷借据信息主键
     * @return 车贷借据信息
     */
    @Override
    public CdlbLoanInfo selectCdlbLoanInfoById(Long id)
    {
        return cdlbLoanInfoMapper.selectCdlbLoanInfoById(id);
    }

    /**
     * 查询车贷借据信息列表
     * 
     * @param cdlbLoanInfo 车贷借据信息
     * @return 车贷借据信息
     */
    @Override
    public List<CdlbLoanInfo> selectCdlbLoanInfoList(CdlbLoanInfo cdlbLoanInfo)
    {
        return cdlbLoanInfoMapper.selectCdlbLoanInfoList(cdlbLoanInfo);
    }
    @Override
    public List<CdlbLoanInfo> selectCdlbLoanInfos(CdlbLoanInfo cdlbLoanInfo)
    {
        return cdlbLoanInfoMapper.selectCdlbLoanInfos(cdlbLoanInfo);
    }

    /**
     * 新增车贷借据信息
     * 
     * @param cdlbLoanInfo 车贷借据信息
     * @return 结果
     */
    @Override
    public int insertCdlbLoanInfo(CdlbLoanInfo cdlbLoanInfo)
    {
        cdlbLoanInfo.setCreateTime(DateUtils.getNowDate());
        return cdlbLoanInfoMapper.insertCdlbLoanInfo(cdlbLoanInfo);
    }

    /**
     * 修改车贷借据信息
     * 
     * @param cdlbLoanInfo 车贷借据信息
     * @return 结果
     */
    @Override
    public int updateCdlbLoanInfo(CdlbLoanInfo cdlbLoanInfo)
    {
        cdlbLoanInfo.setUpdateTime(DateUtils.getNowDate());
        return cdlbLoanInfoMapper.updateCdlbLoanInfo(cdlbLoanInfo);
    }

    /**
     * 批量删除车贷借据信息
     * 
     * @param ids 需要删除的车贷借据信息主键
     * @return 结果
     */
    @Override
    public int deleteCdlbLoanInfoByIds(Long[] ids)
    {
        return cdlbLoanInfoMapper.deleteCdlbLoanInfoByIds(ids);
    }

    /**
     * 删除车贷借据信息信息
     * 
     * @param id 车贷借据信息主键
     * @return 结果
     */
    @Override
    public int deleteCdlbLoanInfoById(Long id)
    {
        return cdlbLoanInfoMapper.deleteCdlbLoanInfoById(id);
    }

    @Override
    public Integer selectinfoIdcounts(String clientCardId) {

        return cdlbLoanInfoMapper.selectinfoIdcounts(clientCardId);
    }
}