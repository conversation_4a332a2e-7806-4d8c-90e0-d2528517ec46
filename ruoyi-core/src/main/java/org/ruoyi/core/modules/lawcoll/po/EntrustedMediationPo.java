package org.ruoyi.core.modules.lawcoll.po;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 *@Authoer: huoruidong
 *@Description: 委托调解业务响应数据
 *@Date: 2023/7/20 10:30
 **/
@Data
public class EntrustedMediationPo {

    /**
     * <AUTHOR>
     * @Description sheet页名称
     * @Date 2023/7/31 11:58
     * @Param
     * @return
     **/
    private String sheetName;

    /**
     * <AUTHOR>
     * @Description sheet页索引
     * @Date 2023/7/31 11:58
     * @Param
     * @return
     **/
    private String sheetIndex;

    /**
     * <AUTHOR>
     * @Description 比对数据
     * @Date 2023/7/21 16:18
     * @Param
     * @return
     **/
    private List<EmPo> dataList = new ArrayList<>();

    /**
     * <AUTHOR>
     * @Description 缺失借据
     * @Date 2023/7/21 16:18
     * @Param
     * @return
     **/
    private String missApplyNos;

    /**
     * <AUTHOR>
     * @Description 导入标识
     * @Date 2023/8/1 9:35
     * @Param
     * @return
     **/
    private String importIdentify;

    /**
     * <AUTHOR>
     * @Description 不同条数
     * @Date 2023/7/21 16:18
     * @Param
     * @return
     **/
    private Integer differentTotal;
}
