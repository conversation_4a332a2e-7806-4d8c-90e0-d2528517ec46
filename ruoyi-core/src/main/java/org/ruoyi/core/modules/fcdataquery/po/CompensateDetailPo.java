package org.ruoyi.core.modules.fcdataquery.po;

import lombok.Data;

import java.math.BigDecimal;

/**
 *@Authoer: huoruidong
 *@Description: 平台保证金明细PO类
 *@Date: 2023/7/20 10:30
 **/
@Data
public class CompensateDetailPo {

    /**
     * 项目id
     **/
    private Integer projectId;

    /**
     * 项目名称
     **/
    private String projectName;

    /**
     * 记账日期
     **/
    private String dataDay;

    /**
     * 收到代偿款
     **/
    private BigDecimal receiveCompensateAmt = BigDecimal.ZERO;

    /**
     * 实际代偿款
     **/
    private BigDecimal actCompensateAmt = BigDecimal.ZERO;

    /**
     * 手工录入代偿款
     **/
    private BigDecimal manaulCompensateAmt = BigDecimal.ZERO;
}
