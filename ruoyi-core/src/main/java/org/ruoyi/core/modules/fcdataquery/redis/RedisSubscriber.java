package org.ruoyi.core.modules.fcdataquery.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.redis.RedisConstants;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.mapper.SysConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.ruoyi.core.modules.fcdataquery.mapper.FcDataQueryMapper;
import org.ruoyi.core.modules.fcdataquery.service.FcSubjectDayStsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class RedisSubscriber implements MessageListener {

    @Autowired
    private FcSubjectDayStsService fcSubjectDayStsService;
    @Autowired
    private FcDataQueryMapper fcDataQueryMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        //获取经营分析统计开关
        SysConfig sysConfig = sysConfigMapper.checkConfigKeyUnique("financial.realTime.sts");
        if(!Boolean.parseBoolean(sysConfig.getConfigValue())){
            return;
        }
        String channel = new String(message.getChannel());
        switch (channel){
            case RedisConstants.FINANCIAL_DATAQUERY_ADD_CHANNEL://新增
                String messageBody = new String(message.getBody());
                log.info("接收到来自:" + channel + "频道的消息体："+ messageBody);
                JSONObject publishJson = JSON.parseObject(messageBody);
                String voucherDate = publishJson.getString("dataDay");

                List<String> subjectNameList = fcDataQueryMapper.selectSubjectNameByVoucherId(publishJson.getInteger("voucherId"));
                //科目统计
                fcSubjectDayStsService.subjectDaySts(voucherDate, voucherDate);

                fcSubjectDayStsService.projectDaySts("2024-01-01", LocalDate.now().toString(), "00");
                fcSubjectDayStsService.projectDaySts("2024-01-01", LocalDate.now().toString(), "10");
                fcSubjectDayStsService.projectDaySts("2024-01-01", LocalDate.now().toString(), "20");
                for (String subjectName:subjectNameList) {
                    if(subjectName.contains("存入保证金")){
                        fcSubjectDayStsService.projectDaySts(voucherDate, LocalDate.now().toString(), "A00");
                    }
                    if(subjectName.contains("银行存款") || subjectName.contains("存出保证金")){
                        fcSubjectDayStsService.projectDaySts(voucherDate, LocalDate.now().toString(), "A01");
                    }
                    if(subjectName.contains("收到代偿款") || subjectName.contains("实际代偿款")){
                        fcSubjectDayStsService.projectDaySts(voucherDate, LocalDate.now().toString(), "A02");
                    }
                    if(subjectName.contains("应付账款2")){
                        fcSubjectDayStsService.projectDaySts(voucherDate, LocalDate.now().toString(), "A03");
                    }
                    if(subjectName.contains("应付账款3")){
                        fcSubjectDayStsService.projectDaySts(voucherDate, LocalDate.now().toString(), "A04");
                    }
                    if(subjectName.contains("其他应付款") && subjectName.contains("线下还款")){
                        fcSubjectDayStsService.projectDaySts(voucherDate, LocalDate.now().toString(), "A05");
                    }
                }
                break;
            case RedisConstants.FINANCIAL_DATAQUERY_DEL_CHANNEL://删除
                String subjectId = new String(message.getBody());
                log.info("接收到来自:" + channel + "频道的消息体："+ subjectId);
                Integer subId = Integer.parseInt(subjectId);
                //科目删除后，删除对应的科目日统计数据，并重新统计所有的
                fcSubjectDayStsService.delSubjectSts(subId);

                String subjectName = fcDataQueryMapper.selectSubjectNameBySubjectId(subId);
                String[] subNameArr = {"预收账款","主营业务收入","应付账款2","应付账款3","主营业务成本","存入保证金",
                        "银行存款","存出保证金","收到代偿款","实际代偿款","其他应付款","线下还款"};
                //项目统计
                if(Arrays.asList(subNameArr).contains(subjectName)){
                    fcSubjectDayStsService.projectDaySts("2024-01-01", LocalDate.now().toString(), "all");
                }
                break;
        }
    }
}