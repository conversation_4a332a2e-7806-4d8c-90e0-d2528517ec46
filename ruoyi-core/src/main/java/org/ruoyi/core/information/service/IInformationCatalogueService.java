package org.ruoyi.core.information.service;

import org.ruoyi.core.information.domain.InformationCatalogue;
import org.ruoyi.core.information.domain.vo.InformationCatalogueVO;

import java.text.ParseException;
import java.util.List;
import java.util.Set;

/**
 * 资料目录Service接口
 *
 * <AUTHOR>
 * @date 2023-11-10
 *
 */
public interface IInformationCatalogueService
{
    /**
     * 查询资料目录
     *
     * @param id 资料目录主键
     * @return 资料目录
     */
    public InformationCatalogue selectInformationCatalogueById(Long id);

    public InformationCatalogueVO selectInformationCatalogueVOById(Long id);

    /**
     * 查询资料目录列表
     *
     * @param informationCatalogue 资料目录
     * @return 资料目录集合
     */
    public List<InformationCatalogueVO> selectInformationCatalogueList(InformationCatalogue informationCatalogue);
    /**
     * 查询资料目录列表 没有分页
     *
     * @param informationCatalogue 资料目录
     * @return 资料目录集合
     */
    public List<InformationCatalogueVO> selectInformationCatalogueListNoLimit(InformationCatalogue informationCatalogue);

    /**
     * 新增资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public int insertInformationCatalogue(InformationCatalogue informationCatalogue) throws ParseException;

    /**
     * 修改资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public int updateInformationCatalogue(InformationCatalogue informationCatalogue) throws ParseException;

    /**
     * 批量删除资料目录
     *
     * @param ids 需要删除的资料目录主键集合
     * @return 结果
     */
    public int deleteInformationCatalogueByIds(Long[] ids);

    /**
     * 删除资料目录信息
     *
     * @param id 资料目录主键
     * @return 结果
     */
    public int deleteInformationCatalogueById(Long id);

    /**
     * 查询数量
     * @param createTime
     * @return
     */
    int getCountByCreateTime(String createTime);

    public List<InformationCatalogue> selectInformationCatalogueByParentId(Long parentId);



    public List<InformationCatalogue> selectInformationCatalogueByIds(Set<Long> catalogueIds);

    public List<InformationCatalogueVO> getTreeListOfAuthority(InformationCatalogue informationCatalogue);

    public List<InformationCatalogueVO> selectCatalogueListOfAuthority(InformationCatalogue informationCatalogue);

}
