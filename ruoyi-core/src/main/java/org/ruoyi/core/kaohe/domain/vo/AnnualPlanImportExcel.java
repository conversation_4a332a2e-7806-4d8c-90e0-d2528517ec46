package org.ruoyi.core.kaohe.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 年度计划对象 kh_annual_plan
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
public class AnnualPlanImportExcel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 年度 */
    @Excel(name = "年度")
    private String year;

    /** 所属类型  1.公司 2.部门 3.个人 */
    //@Excel(name = "所属类型  1.公司 2.部门 3.个人")
    private String type;

    @Excel(name = "导入维度(公司、部门、用户)")
    private String typeName;

    /** 所属公司 */
    //@Excel(name = "所属公司")
    private Long companyId;

    /** 所属公司 */
    @Excel(name = "公司名称(简称)")
    private String companyName;

    /** 所属部门 */
    //@Excel(name = "所属部门")
    private Long deptId;

    /** 所属部门 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 所属个人 */
    //@Excel(name = "所属个人")
    private Long userId;

    @Excel(name = "用户姓名")
    private String nickName;

    @Excel(name = "用户登录账号")
    private String userName;

    /** 所属上级id */
    //@Excel(name = "所属上级id")
    private Long parentId;

    /** 一季度总指标 */
    //@Excel(name = "一季度总指标")
    private BigDecimal q1TotalIndex;

    /** 一季度分配项目指标 */
    @Excel(name = "一季度分配项目指标(万元)")
    private BigDecimal q1DistributionIndex;

    /** 一季度自拓项目指标 */
    @Excel(name = "一季度自拓项目指标(万元)")
    private BigDecimal q1ExtensionIndex;

    /** 一季度自拓银行 */
    @Excel(name = "一季度自拓银行(家)")
    private BigDecimal q1ExtensionBank;

    /** 二季度总指标 */
    //@Excel(name = "二季度总指标")
    private BigDecimal q2TotalIndex;

    /** 二季度分配项目指标 */
    @Excel(name = "二季度分配项目指标(万元)")
    private BigDecimal q2DistributionIndex;

    /** 二季度自拓项目指标 */
    @Excel(name = "二季度自拓项目指标(万元)")
    private BigDecimal q2ExtensionIndex;

    /** 二季度自拓银行 */
    @Excel(name = "二季度自拓银行(家)")
    private BigDecimal q2ExtensionBank;

    /** 三季度总指标 */
    //@Excel(name = "三季度总指标")
    private BigDecimal q3TotalIndex;

    /** 三季度分配项目指标 */
    @Excel(name = "三季度分配项目指标(万元)")
    private BigDecimal q3DistributionIndex;

    /** 三季度自拓项目指标 */
    @Excel(name = "三季度自拓项目指标(万元)")
    private BigDecimal q3ExtensionIndex;

    /** 三季度自拓银行 */
    @Excel(name = "三季度自拓银行(家)")
    private BigDecimal q3ExtensionBank;

    /** 四季度总指标 */
    //@Excel(name = "四季度总指标")
    private BigDecimal q4TotalIndex;

    /** 四季度分配项目指标 */
    @Excel(name = "四季度分配项目指标(万元)")
    private BigDecimal q4DistributionIndex;

    /** 四季度自拓项目指标 */
    @Excel(name = "四季度自拓项目指标(万元)")
    private BigDecimal q4ExtensionIndex;

    /** 四季度自拓银行 */
    @Excel(name = "四季度自拓银行(家)")
    private BigDecimal q4ExtensionBank;

}
