package org.ruoyi.core.kaohe.mapper;

import org.ruoyi.core.kaohe.domain.CheckConfigTemplateSlave;

import java.util.List;

/**
 * 考核配置模版从Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface CheckConfigTemplateSlaveMapper
{
    /**
     * 查询考核配置模版从
     *
     * @param id 考核配置模版从主键
     * @return 考核配置模版从
     */
    public CheckConfigTemplateSlave selectCheckConfigTemplateSlaveById(Long id);

    /**
     * 查询考核配置模版从列表
     *
     * @param checkConfigTemplateSlave 考核配置模版从
     * @return 考核配置模版从集合
     */
    public List<CheckConfigTemplateSlave> selectCheckConfigTemplateSlaveList(CheckConfigTemplateSlave checkConfigTemplateSlave);

    /**
     * 新增考核配置模版从
     *
     * @param checkConfigTemplateSlave 考核配置模版从
     * @return 结果
     */
    public int insertCheckConfigTemplateSlave(CheckConfigTemplateSlave checkConfigTemplateSlave);

    /**
     * 修改考核配置模版从
     *
     * @param checkConfigTemplateSlave 考核配置模版从
     * @return 结果
     */
    public int updateCheckConfigTemplateSlave(CheckConfigTemplateSlave checkConfigTemplateSlave);

    /**
     * 删除考核配置模版从
     *
     * @param id 考核配置模版从主键
     * @return 结果
     */
    public int deleteCheckConfigTemplateSlaveById(Long id);

     public int deleteCheckConfigTemplateSlaveByMainId(Long mainId);

    /**
     * 批量删除考核配置模版从
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckConfigTemplateSlaveByIds(Long[] ids);


    public int batchCheckConfigTemplateSlave(List<CheckConfigTemplateSlave> checkConfigSlaves);
}
