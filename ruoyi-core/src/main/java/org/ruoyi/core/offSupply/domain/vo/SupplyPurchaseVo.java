package org.ruoyi.core.offSupply.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.ruoyi.core.offSupply.domain.OffReceivePurchaseDetail;

/**
 * 采购修改状态VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplyPurchaseVo {

    /** 采购单id集合 */
    private Long[] ids;

    /** 采购单状态 0未提交 1提交*/
    private String status;

    /** 流程id */
    private String processId;

    /** 提交流程时选的公司id */
    private Long companyId;

    /** 表单json */
    private OffReceivePurchaseDetail offReceivePurchaseDetail;
}
