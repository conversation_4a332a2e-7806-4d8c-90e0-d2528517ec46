package org.ruoyi.core.meeting.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.meeting.domain.Meeting;
import org.ruoyi.core.meeting.domain.vo.MeetingVo;
import org.ruoyi.core.meeting.service.IMeetingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 会议管理Controller
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@RestController
@RequestMapping("/meeting")
public class MeetingController extends BaseController
{
    @Autowired
    private IMeetingService meetingService;

    /**
     * 查询会议管理列表
     */
    //@PreAuthorize("@ss.hasPermi('system:meeting:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeetingVo meeting)
    {
        startPage();
        List<MeetingVo> list = meetingService.selectMeetingList(meeting);
        return getDataTable(list);
    }

    @GetMapping("/selectNotification")
    public TableDataInfo selectMeetingListNotification(MeetingVo meeting)
    {
        startPage();
        List<MeetingVo> list = meetingService.selectMeetingListNotification(meeting);
        return getDataTable(list);
    }

    @GetMapping("/month/list")
    public AjaxResult Monthist(MeetingVo meeting)
    {

        return AjaxResult.success(meetingService.selectMeetingMonthList(meeting));
    }

    /**
     * 导出会议管理列表
     */
    //@PreAuthorize("@ss.hasPermi('system:meeting:export')")
    @Log(title = "会议管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeetingVo meeting)
    {
        List<MeetingVo> list = meetingService.selectMeetingList(meeting);
        list.forEach(vo -> {
            if (vo.getDepts() != null && !vo.getDepts().isEmpty()) {
                String organizationalDeptString = vo.getDepts().stream()
                        .map(SysDept::getDeptName)  // 获取每个 User 的 nickname
                        .collect(Collectors.joining(","));  // 拼接成一个字符串，逗号分隔
                vo.setOrganizationalDeptString(organizationalDeptString);
            }
        });
        ExcelUtil<MeetingVo> util = new ExcelUtil<MeetingVo>(MeetingVo.class);
        util.exportExcel(response, list, "会议管理数据");
    }

    /**
     * 获取会议管理详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:meeting:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(meetingService.selectMeetingById(id));
    }

    @GetMapping(value = "/processId/{processId}")
    public AjaxResult selectMeetingByProcessId(@PathVariable("processId") String processId)
    {
        return AjaxResult.success(meetingService.selectMeetingByProcessId(processId));
    }

    /**
     * 新增会议管理
     */
    //@PreAuthorize("@ss.hasPermi('system:meeting:add')")
    @Log(title = "会议管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeetingVo meeting)
    {
        return toAjax(meetingService.insertMeeting(meeting));
    }

    /**
     * 修改会议管理
     */
    //@PreAuthorize("@ss.hasPermi('system:meeting:edit')")
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Meeting meeting)
    {
        return toAjax(meetingService.updateMeeting(meeting));
    }

    /**
     * 删除会议管理
     */
    //@PreAuthorize("@ss.hasPermi('system:meeting:remove')")
    @Log(title = "会议管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(meetingService.deleteMeetingByIds(ids));
    }

    /**
     * 删除会议管理
     */
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/passMeeting")
    public AjaxResult passMeeting(@RequestBody Long id)
    {
        return toAjax(meetingService.passMeeting(id));
    }

    /**
     * 删除会议管理
     */
    @Log(title = "会议管理", businessType = BusinessType.UPDATE)
    @PostMapping("/unpassMeeting")
    public AjaxResult unpassMeeting(@RequestBody Long id)
    {
        return toAjax(meetingService.unpassMeeting(id));
    }


    /**
     * 新增会议管理
     */
    //@PreAuthorize("@ss.hasPermi('system:meeting:add')")
    @Log(title = "会议室时间重叠查询", businessType = BusinessType.INSERT)
    @PostMapping("/inspectionTime")
    public AjaxResult inspectionTime(@RequestBody MeetingVo meeting)
    {
        return toAjax(meetingService.inspectionTime(meeting));
    }

    @Anonymous
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
        return meetingService.uploadFile(file);
    }
}
