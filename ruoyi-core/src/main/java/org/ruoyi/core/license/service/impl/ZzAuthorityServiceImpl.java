package org.ruoyi.core.license.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.AuthDetail;
import com.ruoyi.system.domain.AuthMain;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysPostService;
import org.apache.commons.collections.CollectionUtils;
import org.ruoyi.core.license.mapper.ZzAuthorityMapper;
import org.ruoyi.core.xmglproject.mapper.XmglFlowRelationMapper;
import org.ruoyi.core.xmglproject.mapper.XmglProjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.ruoyi.core.license.domain.ZzAuthority;
import org.ruoyi.core.license.service.IZzAuthorityService;

import javax.annotation.Resource;

/**
 * 证照授权Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
@Service
public class ZzAuthorityServiceImpl implements IZzAuthorityService {
    @Autowired
    private ZzAuthorityMapper zzAuthorityMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ISysPostService iSysPostService;

    @Resource
    private XmglFlowRelationMapper xmglFlowRelationMapper;

    @Resource
    private XmglProjectMapper xmglProjectMapper;


    /**
     * 查询证照授权
     *
     * @param billId 证照授权主键
     * @return 证照授权
     */
    @Override
    public ZzAuthority selectZzAuthorityByBillId(Long billId) {
        return zzAuthorityMapper.selectZzAuthorityByBillId(billId);
    }

    /**
     * 查询证照授权列表
     *
     * @param zzAuthority 证照授权
     * @return 证照授权
     */
    @Override
    public List<ZzAuthority> selectZzAuthorityList(ZzAuthority zzAuthority) {
        return zzAuthorityMapper.selectZzAuthorityList(zzAuthority);
    }

    /**
     * 新增证照授权
     *
     * @param zzAuthority 证照授权
     * @return 结果
     */
    @Override
    public int insertZzAuthority(List<ZzAuthority> zzAuthority) {
        List<ZzAuthority> authorityList = new ArrayList<>();
        if (zzAuthority != null && zzAuthority.size() > 0) {
            for (ZzAuthority authority : zzAuthority) {
                // 授权的岗位、人员、部门id集合
                Long[] authorityIds = authority.getAuthorityIds();
                if (authorityIds.length <= 0){
                    return 1;
                }
                if (authorityIds.length > 0 && authorityIds != null) {
                    for (Long authorityId : authorityIds) {
                        // 组装数据
                        ZzAuthority authorityDo = new ZzAuthority();
                        // 关联目录id
                        authorityDo.setBillId(authority.getBillId());
                        // 权限类型(0:部门 1:岗位 2:人员)
                        authorityDo.setAuthorityType(authority.getAuthorityType());
                        // 类型(0目录)
                        authorityDo.setBillType(authority.getBillType());
                        // 授权人
                        authorityDo.setImpower(SecurityUtils.getLoginUser().getUser().getUserName());
                        // 授权时间
                        authorityDo.setImpowerTime(DateUtils.getNowDate());
                        // 授权的岗位或人员或部门的id
                        authorityDo.setAuthorityId(authorityId);

                        authorityList.add(authorityDo);
                    }
                }
            }
        }
        return zzAuthorityMapper.insertZzAuthority(authorityList);
    }

    /**
     * 修改证照授权
     *
     * @param zzAuthority 证照授权
     * @return 结果
     */
    @Override
    public int updateZzAuthority(ZzAuthority zzAuthority) {
        return zzAuthorityMapper.updateZzAuthority(zzAuthority);
    }

    /**
     * 批量删除证照授权
     *
     * @param billIds 需要删除的证照授权主键
     * @return 结果
     */
    @Override
    public int deleteZzAuthorityByBillIds(Long[] billIds) {
        return zzAuthorityMapper.deleteZzAuthorityByBillIds(billIds);
    }

    /**
     * 删除证照授权信息
     *
     * @param billId 证照授权主键
     * @return 结果
     */
    @Override
    public int deleteZzAuthorityByBillId(Long billId) {
        return zzAuthorityMapper.deleteZzAuthorityByBillId(billId);
    }

    /**
     * 目录授权
     *
     * @param authorityList
     * @return
     */
    @Override
    public int accreditAuthority(List<ZzAuthority> authorityList) {
        //删除旧权限
        for (ZzAuthority zzAuthority : authorityList) {
            //根据目录id查询授权数据
            List<ZzAuthority> authority = zzAuthorityMapper.queryZzAuthorityListByBillId(zzAuthority.getBillId());
            if (CollectionUtils.isNotEmpty(authority)) {
                zzAuthorityMapper.deleteZzAuthorityByBillIdAndAuthorityType(zzAuthority.getBillId(),zzAuthority.getAuthorityType());
            }
        }
        return insertZzAuthority(authorityList);
    }

    @Override
    public List<ZzAuthority> selectZzAuthorityListByBillId(Long billId) {
        return zzAuthorityMapper.queryZzAuthorityListByBillId(billId);
    }

    /**
     * 新权限-初始化用户目录权限
     * @param userId
     * @param companyId
     * @param moduleType
     * @param roleType
     */
    private void insertAuthority(Long userId, Long companyId, String moduleType, String roleType) {
        Long creater = SecurityUtils.getLoginUser().getUserId();//创建人
        SysUser sysUser = sysUserMapper.selectUserById(creater);
        //获取主岗位
        Long homePost = iSysPostService.selectHomePostByUserId(creater);
        //获取主岗位信息
        SysPost sysPostToAgency = iSysPostService.selectPostById(homePost);
        AuthMain authMain = new AuthMain();
        authMain.setThirdType("1");
        authMain.setThirdId(userId);
        authMain.setModuleType(moduleType);
        authMain.setRoleType(roleType);
        authMain.setPermissionScope("-1");
        authMain.setPermissionType("0");
        authMain.setPermissionTime(DateUtils.parse("9999-12-31 23:59:59", DateUtils.DateFormat.YYYY_MM_DD_HHMMSS));
        authMain.setStatus("0");
        authMain.setSendNotify("-1");
        authMain.setCreateId(creater);
        authMain.setCreateBy(sysUser.getUserName());
        authMain.setCreateTime(DateUtils.getNowDate());
        authMain.setCreateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authMain.setCreateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());

        authMain.setUpdateBy(sysUser.getUserName());
        authMain.setUpdateTime(DateUtils.getNowDate());
        authMain.setUpdateId(SecurityUtils.getLoginUser().getUserId());
        authMain.setUpdateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authMain.setUpdateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());
        //插入权限主表
        xmglFlowRelationMapper.insertAuthMain(authMain);
        Long id = authMain.getId();

        //插入附表
        AuthDetail authDetail = new AuthDetail();
        authDetail.setAuthMainId(id);
        authDetail.setThirdTableName("zz_license_main");
        authDetail.setThirdTableAliasName("license");
        authDetail.setThirdTableId(companyId);
        authDetail.setStatus("0");
        authDetail.setCreateId(SecurityUtils.getLoginUser().getUserId());
        authDetail.setCreateBy(sysUser.getUserName());
        authDetail.setCreateTime(DateUtils.getNowDate());
        authDetail.setCreateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authDetail.setCreateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());
        authDetail.setUpdateTime(DateUtils.getNowDate());
        authDetail.setUpdateId(SecurityUtils.getLoginUser().getUserId());
        authDetail.setUpdateBy(sysUser.getUserName());
        authDetail.setUpdateUnitId(sysPostToAgency.getUnitId() == null ? -1L : sysPostToAgency.getUnitId());
        authDetail.setUpdateDeptId(sysPostToAgency.getDeptId() == null ? -1L : sysPostToAgency.getDeptId());
        xmglFlowRelationMapper.insertAuthDteail(authDetail);
    }
}
