package org.ruoyi.core.license.constant;

public class ZzConstant {

    public static final Integer INTEGER_0 = 0;
    public static final Integer INTEGER_1 = 1;
    public static final Integer INTEGER_2 = 2;

    public static final String STRING_0 = "0";
    public static final String STRING_1 = "1";
    public static final String STRING_2 = "2";
    public static final String STRING_3 = "3";
    public static final String STRING_4 = "4";
    public static final String STRING_5 = "5";
    public static final String STRING_6 = "6";
    public static final String STRING_10 = "10";
    public static final String STRING_11 = "11";


    public static final Long LONG_1 = 1L;

    //证照是否在库状态
    public static final String ZZ_LICENSE_STATUS = "license_status";

    //证照签领收回状态
    public static final String ZZ_LICENSE_SIGN_STATUS = "license_sign_status";

    //证照到期状态license_time_status
    public static final String ZZ_LICENSE_TIME_STATUS = "license_time_status";

    //证照有效期状态
    public static final String ZZ_LICENSE_INDATE_FLAG_STATUS = "license_indate_flag_status";
    //证照年审到期状态
    public static final String ZZ_LICENSE_ANNUAL_AUDIT_STATUS = "license_annual_Audit_status";

    public static final String JIEYUE_DATE = "借阅";

    public static final String JIEYONG_DATE = "借用";

    //领用页签
    public static final String TABLE_TYPE_LY = "LY";
    //收回页签
    public static final String TABLE_TYPE_SH = "SH";

    public static final String ZZ_EXPIRE_NOTIFY = "证照到期提醒";

    public static final String ZZ_EXPIRE_URL = "/zzCommonNotify/notify";

    public static final String MAX_DATE = "9999-12-31";

    /** 证照年审预警 */
    public static final String WARN_INDATE_TYPE = "indateType";

    /** 证照有效期预警 */
    public static final String WARN_AUDIT_TYPE = "auditType";

    /** 全部开启 */
    public static final String WARN_ALL_TYPE = "all";

    public static final String IS_OPEN = "未开启";

    /** 证照导出标志 */
    public static final String EXPORT_FLAG = "DC";

    /** 按钮类型(BC保存/TJ提交) */
    public static final String BUTTON_TYPE_BC = "BC";
}
