package org.ruoyi.core.oasystem.service;

import org.ruoyi.core.oasystem.domain.OaProjectNameRule;
import org.ruoyi.core.oasystem.domain.vo.OaProjectNameRuleVo;

import java.util.List;

/**
 * 特殊产品分类配置Service接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IOaProjectNameRuleService
{
    /**
     * 查询特殊产品分类配置
     *
     * @param id 特殊产品分类配置主键
     * @return 特殊产品分类配置
     */
    public OaProjectNameRuleVo selectOaProjectNameRuleById(Long id);


    public OaProjectNameRuleVo getOaProjectNameRule(OaProjectNameRuleVo oaProjectNameRule);

    /**
     * 查询特殊产品分类配置列表
     *
     * @param oaProjectNameRule 特殊产品分类配置
     * @return 特殊产品分类配置集合
     */
    public List<OaProjectNameRuleVo> selectOaProjectNameRuleList(OaProjectNameRuleVo oaProjectNameRule);

    /**
     * 新增特殊产品分类配置
     *
     * @param oaProjectNameRule 特殊产品分类配置
     * @return 结果
     */
    public int insertOaProjectNameRule(OaProjectNameRuleVo oaProjectNameRule);

    /**
     * 修改特殊产品分类配置
     *
     * @param oaProjectNameRule 特殊产品分类配置
     * @return 结果
     */
    public int updateOaProjectNameRule(OaProjectNameRuleVo oaProjectNameRule);

    /**
     * 批量删除特殊产品分类配置
     *
     * @param ids 需要删除的特殊产品分类配置主键集合
     * @return 结果
     */
    public int deleteOaProjectNameRuleByIds(Long[] ids);

    /**
     * 删除特殊产品分类配置信息
     *
     * @param id 特殊产品分类配置主键
     * @return 结果
     */
    public int deleteOaProjectNameRuleById(Long id);
}
