package org.ruoyi.core.oasystem.service.impl;

import java.util.List;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import org.ruoyi.core.oasystem.domain.OaDictAo;
import org.ruoyi.core.oasystem.domain.OaDictData;
import org.ruoyi.core.oasystem.domain.OaDictDynamic;
import org.ruoyi.core.oasystem.mapper.OaDictDataMapper;
import org.ruoyi.core.oasystem.mapper.OaDictDynamicMapper;
import org.ruoyi.core.oasystem.service.IOaDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-11
 */
@Service
public class OaDictDataServiceImpl implements IOaDictDataService
{
    @Autowired
    private OaDictDataMapper oaDictDataMapper;

    @Autowired
    private OaDictDynamicMapper oaDictDynamicMapper;
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaDictData selectOaDictDataById(Long id)
    {
        return oaDictDataMapper.selectOaDictDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaDictData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaDictData> selectOaDictDataList(OaDictData oaDictData)
    {
        return oaDictDataMapper.selectOaDictDataList(oaDictData);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaDictData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaDictData(OaDictData oaDictData)
    {
        oaDictData.setCreateTime(DateUtils.getNowDate());
        return oaDictDataMapper.insertOaDictData(oaDictData);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaDictData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateOaDictData(OaDictData oaDictData)
    {
        oaDictData.setUpdateTime(DateUtils.getNowDate());
        return oaDictDataMapper.updateOaDictData(oaDictData);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaDictDataByIds(Long[] ids)
    {
        return oaDictDataMapper.deleteOaDictDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaDictDataById(Long id)
    {
        return oaDictDataMapper.deleteOaDictDataById(id);
    }

    @Override
    public int updateEnable(OaDictData oaDictData, LoginUser loginUser) {

        oaDictData.setUpdateTime(DateUtils.getNowDate());
        oaDictData.setEndUpdateTime(DateUtils.getNowDate());
        int i = oaDictDataMapper.updateOaDictDataEnable(oaDictData);
        OaDictDynamic oaDictDynamic = new OaDictDynamic();
        oaDictDynamic.setDictId(oaDictData.getDictTypeIdent());
        oaDictDynamic.setOperationTime(DateUtils.getNowDate());
        oaDictDynamic.setOperationContent("修改启用状态为"+oaDictData.getIsEnable());
        oaDictDynamic.setOperationById(loginUser.getUserId());
        oaDictDynamic.setOperationBy(loginUser.getUser().getNickName());
        oaDictDynamicMapper.insertOaDictDynamic(oaDictDynamic);
        return i;

    }

    @Override
    public int insertOaDictDataList(OaDictAo oaDictAo, LoginUser loginUser) {
        List<OaDictData> oaDictDataList = oaDictAo.getOaDictDataList();
        String id = UUID.randomUUID().toString();
        String dictName = oaDictAo.getDictName();
        String dictType = oaDictAo.getDictType();
        String isEnable = oaDictAo.getIsEnable();
        for (OaDictData oaDictData : oaDictDataList) {
            oaDictData.setDictTypeIdent(id);
            oaDictData.setDictType(dictType);
            oaDictData.setDictTypeName(dictName);
            oaDictData.setIsEnable(isEnable);
            oaDictData.setEndUpdateTime(DateUtils.getNowDate());
            oaDictData.setCreateBy(loginUser.getUser().getNickName());
            oaDictData.setCreateTime(DateUtils.getNowDate());
        }
        int i = oaDictDataMapper.batchInsert(oaDictDataList);
        OaDictDynamic oaDictDynamic = new OaDictDynamic();
        oaDictDynamic.setDictId(id);
        oaDictDynamic.setOperationTime(DateUtils.getNowDate());
        oaDictDynamic.setOperationContent("新增字典类型");
        oaDictDynamic.setOperationById(loginUser.getUserId());
        oaDictDynamic.setOperationBy(loginUser.getUser().getNickName());
        oaDictDynamicMapper.insertOaDictDynamic(oaDictDynamic);
        return i;
    }

    @Override
    public int updateOaDictDataList(OaDictAo oaDictAo, LoginUser loginUser) {
        List<OaDictData> oaDictDataList = oaDictAo.getOaDictDataList();

        String dictName = oaDictAo.getDictName();
        String dictTypeIdent = oaDictAo.getDictTypeIdent();
        String isEnable = oaDictAo.getIsEnable();
        for (OaDictData oaDictData : oaDictDataList) {
//            oaDictData.setDictTypeIdent(id);
//            oaDictData.setDictType(dictType);
            oaDictData.setDictTypeName(dictName);
            oaDictData.setIsEnable(isEnable);
            oaDictData.setDictTypeIdent(dictTypeIdent);
            oaDictData.setEndUpdateTime(DateUtils.getNowDate());
            oaDictData.setUpdateBy(loginUser.getUser().getNickName());
            oaDictData.setUpdateTime(DateUtils.getNowDate());

        }
        //删除原本的
      int de =  oaDictDataMapper.deleteByDictTypeIdent(dictTypeIdent);

        int i = oaDictDataMapper.batchInsert(oaDictDataList);
        OaDictDynamic oaDictDynamic = new OaDictDynamic();
        oaDictDynamic.setDictId(dictTypeIdent);
        oaDictDynamic.setOperationTime(DateUtils.getNowDate());
        oaDictDynamic.setOperationContent("编辑字典配置");
        oaDictDynamic.setOperationById(loginUser.getUserId());
        oaDictDynamic.setOperationBy(loginUser.getUser().getNickName());
        oaDictDynamicMapper.insertOaDictDynamic(oaDictDynamic);
        return i;
    }

    @Override
    public OaDictAo selectOaDictDataByDictType(String id) {

        OaDictAo oaDictAo = new OaDictAo();
        List<OaDictData> oaDictData = oaDictDataMapper.selectByDictIdel(id);
        if(oaDictData.size()>0){
            OaDictData oaDictData1 = oaDictData.get(0);
            oaDictAo.setDictName(oaDictData1.getDictTypeName());
            oaDictAo.setDictType(oaDictData1.getDictType());
            oaDictAo.setIsEnable(oaDictData1.getIsEnable());
            oaDictAo.setDictTypeIdent(oaDictData1.getDictTypeIdent());
            oaDictAo.setOaDictDataList(oaDictData);
        }
        return oaDictAo;
    }

    @Override
    public int deletebyDictIdel(String ids) {
        int de =  oaDictDataMapper.deleteByDictTypeIdent(ids);
        return de;
    }
}
