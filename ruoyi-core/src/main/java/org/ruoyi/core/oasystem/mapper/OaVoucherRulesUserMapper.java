package org.ruoyi.core.oasystem.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesUser;

import java.util.List;

/**
 * 记账凭证规则用户表 Mapper接口
 * 
 * <AUTHOR>
 * @date 2023/12/13 11:09
 */
public interface OaVoucherRulesUserMapper
{
    /**
     * 根据记账凭证主表id查找对应的用户
     *
     * @param oaVoucherRulesMainId 记账凭证主表id
     * @return 用户集合
     */
    public List<OaVoucherRulesUser> selectOaVoucherRulesUserListByOaVoucherRulesMainId(Long oaVoucherRulesMainId);

    /**
     * 新增记账凭证规则用户
     *
     * @param oaVoucherRulesUser 记账凭证规则用户 - 实体类
     * @return
     */
    public int insertOaVoucherRulesUser(OaVoucherRulesUser oaVoucherRulesUser);

    /**
     * 根据记账凭证主表id删除对应的所有用户
     *
     * @param oaVoucherRulesMainId 记账凭证主表id
     * @return
     */
    int deleteOaVoucherRulesUserByOaVoucherRulesMainId(Long oaVoucherRulesMainId);

    /**
     * 根据记账凭证主表id查找对应的用户
     *
     * @param oaVoucherRulesMainId 记账凭证主表id
     * @param userId 用户id
     * @return 用户集合
     */
    OaVoucherRulesUser selectOaVoucherRulesUserByOaVoucherRulesMainIdAndUserId(@Param("oaVoucherRulesMainId") Long oaVoucherRulesMainId, @Param("userId") Long userId);
}
