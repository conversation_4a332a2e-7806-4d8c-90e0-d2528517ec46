package org.ruoyi.core.oasystem.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.ruoyi.core.cwproject.domain.TopNotify;
import org.ruoyi.core.cwproject.mapper.TopNotifyMapper;
import org.ruoyi.core.modules.fcdataquery.mapper.FcDataQueryMapper;
import org.ruoyi.core.modules.fcdataquery.po.AccountSetsInfoPo;
import org.ruoyi.core.oasystem.domain.*;
import org.ruoyi.core.oasystem.domain.bo.OaTraderBo;
import org.ruoyi.core.oasystem.domain.bo.UpdateNewOaTraderBo;
import org.ruoyi.core.oasystem.domain.vo.OaTraderVo;
import org.ruoyi.core.oasystem.mapper.*;
import org.ruoyi.core.oasystem.service.IOaTraderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
@Service
public class OaTraderServiceImpl implements IOaTraderService
{
    @Autowired
    private OaTraderMapper oaTraderMapper;

    @Autowired
    private OaTraderDynamicMapper oaTraderDynamicMapper;

    @Autowired
    private OaProjectPayerAssociationMapper oaProjectPayerAssociationMapper;

    @Autowired
    private OaEditApproveGeneralityUserMapper oaEditApproveGeneralityUserMapper;

    @Autowired
    private OaEditApproveGeneralityEditRecordsMapper oaEditApproveGeneralityEditRecordsMapper;

    @Autowired
    private TopNotifyMapper topNotifyMapper;

    @Autowired
    private OaEditApproveGeneralityRecordsMapper oaEditApproveGeneralityRecordsMapper;

    @Autowired
    private FcDataQueryMapper fcDataQueryMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaTraderBo selectOaTraderById(Long id)
    {
        OaTrader oaTrader = oaTraderMapper.selectOaTraderById(id);
        OaTraderBo oaTraderBo = new OaTraderBo();
        BeanUtil.copyProperties(oaTrader, oaTraderBo);
        String oaApplyType = "0".equals(oaTrader.getTraderType()) ? "1" : "2";
        //组装财务责任人和业务责任人
        List<OaEditApproveGeneralityUser> oaEditApproveGeneralityUsers = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(oaApplyType, id);
        List<Long> salesmanList = oaEditApproveGeneralityUsers.stream().filter(t -> "0".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
        List<Long> financialStaffList = oaEditApproveGeneralityUsers.stream().filter(t -> "1".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserId).collect(Collectors.toList());
        oaTraderBo.setSalesmanList(salesmanList);
        oaTraderBo.setFinancialStaffList(financialStaffList);
        //组装修改前JSON对应的id
        //找最新的、已经知悉的审批记录表（未知悉进不来修改页面，放心处理）
        PageHelper.clearPage();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, id, "1");
        //查出来的最新的记录表修改后id是要修改的之前的id
        if (oaEditApproveGeneralityEditRecords != null) {
            oaTraderBo.setOaApplyRecordsOldId(oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId());
        } else {
            oaTraderBo.setOaApplyRecordsOldId(null);
        }
        return oaTraderBo;
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaTrader 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaTrader> selectOaTraderList(OaTrader oaTrader)
    {
        return oaTraderMapper.selectOaTraderList(oaTrader);
    }

    @Override
    public List<OaTraderVo> selectOaTraderListByParam(OaTrader oaTrader, String selectType, List<Long> companyIdList, List<Long> filterOaApplyId) {
        if (!"0".equals(selectType)) {
            //加入新的查询条件，公司id，过滤后的主表id（主要是涉及其他视图的查询）
            if (filterOaApplyId == null) {
                return new ArrayList<>();
            }
            List<OaTrader> oaTraders = oaTraderMapper.selectOaTraderListByOaTraderAndFilterOaApplyIdAndCompanyIdList(oaTrader, companyIdList, filterOaApplyId);
            //组装剩余属性
            List<OaTraderVo> oaTraderVos = this.getOaTraderVoListByOaTraderList(oaTraders, selectType);
            return oaTraderVos;
        }
        List<OaTrader> oaTraders = oaTraderMapper.selectOaTraderList(oaTrader);
        //组装剩余属性
        List<OaTraderVo> oaTraderVos = this.getOaTraderVoListByOaTraderList(oaTraders, selectType);
        return oaTraderVos;
    }

    private List<OaTraderVo> getOaTraderVoListByOaTraderList(List<OaTrader> oaTraders, String selectType) {
        List<OaTraderVo> oaTraderVos = new ArrayList<>();
        Long currentUserId = SecurityUtils.getLoginUser().getUserId();
        TopNotify topNotify = new TopNotify();
        topNotify.setNotifyType("1");
        topNotify.setOaNotifyStep("1");
        TopNotify topNotify1 = new TopNotify();
        topNotify1.setNotifyType("1");
        topNotify1.setOaNotifyStep("2");
        for (OaTrader oaTrader:oaTraders) {
            OaTraderVo oaTraderVo = new OaTraderVo();
            BeanUtil.copyProperties(oaTrader, oaTraderVo);
            //补充页面缺失数据
            //查找对应的负责人
            String oaApplyType = "0".equals(oaTrader.getTraderType()) ? "1" : "2";
            topNotify.setOaNotifyType(oaApplyType);
            topNotify1.setOaNotifyType(oaApplyType);
            List<OaEditApproveGeneralityUser> oaEditApproveGeneralityUsers = oaEditApproveGeneralityUserMapper.selectOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(oaApplyType, oaTrader.getId());
            String salesman = oaEditApproveGeneralityUsers.stream().filter(t -> "0".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserNickName).collect(Collectors.joining(","));
            String financialStaff = oaEditApproveGeneralityUsers.stream().filter(t -> "1".equals(t.getUserFlag())).map(OaEditApproveGeneralityUser::getUserNickName).collect(Collectors.joining(","));
            oaTraderVo.setSalesman(salesman);
            oaTraderVo.setFinancialStaff(financialStaff);
            //查最新的审核状态 - 未知悉的情况，说明流程未结束
            PageHelper.clearPage();
            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectNewOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaTrader.getId(), "0");
            if (oaEditApproveGeneralityEditRecords != null) {
                oaTraderVo.setCheckStatus(oaEditApproveGeneralityEditRecords.getCheckStatus());
                oaTraderVo.setRejectFlag(oaEditApproveGeneralityEditRecords.getRejectFlag());
                oaTraderVo.setConfirmFlag(oaEditApproveGeneralityEditRecords.getConfirmFlag());
                //修改的判断方法 oa_apply_records_old_id和oa_apply_records_new_id都不为空而且不相等
                if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() != null && !oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId().equals(oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId())) {
                    oaTraderVo.setUpdateType("1");
                }
                //删除的判断方法 oa_apply_records_old_id 不为空 并且oa_apply_records_new_id 为空
                if (oaEditApproveGeneralityEditRecords.getOaApplyRecordsOldId() != null && oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId() == null) {
                    oaTraderVo.setUpdateType("2");
                }
                if ("1".equals(selectType)) {
                    //查询的是待我审批，找到提交人  --->  也就是找编辑人员的姓名
                    oaTraderVo.setEditUserNickName(oaEditApproveGeneralityEditRecords.getEditUserNickName());
                    //找到提交时间
                    oaTraderVo.setEditTime(oaEditApproveGeneralityEditRecords.getEditTime());
                    //修改说明
                    oaTraderVo.setEditInfo(oaEditApproveGeneralityEditRecords.getEditInfo());
                } else if ("2".equals(selectType)) {
                    //查询的是我的提交，找到审批人
                    oaTraderVo.setCheckUserNickName(oaEditApproveGeneralityEditRecords.getCheckUserNickName());
                    //找到审核时间
                    oaTraderVo.setCheckTime(oaEditApproveGeneralityEditRecords.getCheckTime());
                }

                //查询当前用户的权限问题，是否是有本条记录的审核，是否有本条记录的已知悉操作
                //审核权限
                topNotify.setOaApplyId(oaTrader.getId());
                List<TopNotify> topNotifies = topNotifyMapper.selectTopNotifyList1(topNotify);
                //知悉权限
                topNotify1.setOaApplyId(oaTrader.getId());
                List<TopNotify> topNotifies1 = topNotifyMapper.selectTopNotifyList1(topNotify1);
                boolean checkFlag = topNotifies.stream().anyMatch(t -> t.getDisposeUser().equals(currentUserId));
                boolean confirmFlag = topNotifies1.stream().anyMatch(t -> t.getDisposeUser().equals(currentUserId));
                if (checkFlag) {
                    oaTraderVo.setShowCheckFlag("1");
                } else {
                    oaTraderVo.setShowCheckFlag("0");
                }
                if (confirmFlag) {
                    oaTraderVo.setShowConfirmFlag("1");
                } else {
                    oaTraderVo.setShowConfirmFlag("0");
                }
            } else {
                oaTraderVo.setConfirmFlag("1");
                oaTraderVo.setShowCheckFlag("0");
                oaTraderVo.setShowConfirmFlag("0");
            }
            oaTraderVos.add(oaTraderVo);
        }
        return oaTraderVos;
    }


    /**
     * 新增【请填写功能名称】
     * 
     * @param oaTrader 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaTrader(OaTrader oaTrader, LoginUser loginUser)
    {
        oaTrader.setCreateBy(loginUser.getUser().getNickName());
        oaTrader.setUpdateBy(loginUser.getUser().getNickName());
        oaTrader.setUpdateTime(DateUtils.getNowDate());
        oaTrader.setAddNotApprove("96");
        oaTrader.setEndUpdateTime(DateUtils.getNowDate());
        oaTrader.setCreateTime(DateUtils.getNowDate());
        int i = oaTraderMapper.insertOaTrader(oaTrader);
        //得到插入生成的id 添加动态表
        OaTraderDynamic oaTraderDynamic = new OaTraderDynamic();
        oaTraderDynamic.setOaTraderId(oaTrader.getId());
        oaTraderDynamic.setOperationTime(DateUtils.getNowDate());
        oaTraderDynamic.setOperationContent("创建收付款人");
        oaTraderDynamic.setOperationBrId(loginUser.getUserId());
        oaTraderDynamic.setOperationBr(loginUser.getUser().getNickName());
        oaTraderDynamicMapper.insertOaTraderDynamic(oaTraderDynamic);
        return i;
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaTrader 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateOaTrader(OaTrader oaTrader, LoginUser loginUser)
    {
        oaTrader.setEndUpdateTime(DateUtils.getNowDate());
        oaTrader.setUpdateTime(DateUtils.getNowDate());
        //查询原本的数据 进行对比启用停用，如果状态没变则不添加动态
        OaTrader oaTrader1 = oaTraderMapper.selectOaTraderById(oaTrader.getId());
        if(!(oaTrader1.getIsEnable().equals(oaTrader.getIsEnable()))){
            //得到插入生成的id 添加动态表
            OaTraderDynamic oaTraderDynamic = new OaTraderDynamic();
            oaTraderDynamic.setOaTraderId(oaTrader.getId());
            oaTraderDynamic.setOperationTime(DateUtils.getNowDate());

            if(oaTrader.getIsEnable().equals("Y")){
                oaTraderDynamic.setOperationContent("启用");
            }else if(oaTrader.getIsEnable().equals("N")){
                oaTraderDynamic.setOperationContent("停用");
            }

            oaTraderDynamic.setOperationBrId(loginUser.getUserId());
            oaTraderDynamic.setOperationBr(loginUser.getUser().getNickName());
            oaTraderDynamicMapper.insertOaTraderDynamic(oaTraderDynamic);
        }

        if( !((oaTrader.getType().equals(oaTrader1.getType())) && (oaTrader.getUserName().equals(oaTrader1.getUserName())) &&(oaTrader.getBankOfDeposit().equals(oaTrader1.getBankOfDeposit())) &&(oaTrader.getAccountNumber().equals(oaTrader1.getAccountNumber())) && (oaTrader.getAbbreviation().equals(oaTrader1.getAbbreviation())) && (oaTrader.getIsAccount().equals(oaTrader1.getIsAccount())) && (oaTrader.getAccountId().equals(oaTrader1.getAccountId())))){
            //得到插入生成的id 添加动态表
            OaTraderDynamic oaTraderDynamic = new OaTraderDynamic();
            oaTraderDynamic.setOaTraderId(oaTrader.getId());
            oaTraderDynamic.setOperationTime(DateUtils.getNowDate());
            oaTraderDynamic.setOperationContent("修改信息");
            oaTraderDynamic.setOperationBrId(loginUser.getUserId());
            oaTraderDynamic.setOperationBr(loginUser.getUser().getNickName());
            oaTraderDynamicMapper.insertOaTraderDynamic(oaTraderDynamic);
        }

        int i = oaTraderMapper.updateOaTrader(oaTrader);
        //更新项目与流程关联中的
        OaProjectPayerAssociation oaProjectPayerAssociation = new OaProjectPayerAssociation();
        if(oaTrader.getTraderType().equals("0")){
            //付款人
            oaProjectPayerAssociation.setPayId(oaTrader.getId());
        } else if(oaTrader.getTraderType().equals("1")){
            //收款人
            oaProjectPayerAssociation.setCollId(oaTrader.getId());
        }
        List<OaProjectPayerAssociation> oaProjectPayerAssociations = oaProjectPayerAssociationMapper.selectOaProjectPayerAssociationList(oaProjectPayerAssociation);
        //判断是否有相关的配置有则修改
        if(oaProjectPayerAssociations.size()>0){
            for (OaProjectPayerAssociation projectPayerAssociation : oaProjectPayerAssociations) {

                if(oaTrader.getTraderType().equals("0")){
                    //付款人
                    projectPayerAssociation.setPayId(oaTrader.getId());
                    projectPayerAssociation.setPayName(oaTrader.getUserName());
                    projectPayerAssociation.setPayBankOfDeposit(oaTrader.getBankOfDeposit());
                    projectPayerAssociation.setPayAccountNumber(oaTrader.getAccountNumber());
                    projectPayerAssociation.setPayAbbreviation(oaTrader.getAbbreviation());
                } else if(oaTrader.getTraderType().equals("1")){
                    //收款人
                    projectPayerAssociation.setCollId(oaTrader.getId());
                    projectPayerAssociation.setCollName(oaTrader.getUserName());
                    projectPayerAssociation.setCollBankOfDeposit(oaTrader.getBankOfDeposit());
                    projectPayerAssociation.setCollAccountNumber(oaTrader.getAccountNumber());
                    projectPayerAssociation.setCollAbbreviation(oaTrader.getAbbreviation());
                }

                oaProjectPayerAssociationMapper.updateOaProjectPayerAssociation(projectPayerAssociation);
            }

        }


        return i;
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaTraderByIds(Long[] ids)
    {
        List<OaTrader> oaTraders = oaTraderMapper.selectOaTraderListByIds(ids);
        for (OaTrader oaTrader:oaTraders) {
            String oaApplyType = "0".equals(oaTrader.getTraderType()) ? "1" : "2";
            Long oaApplyId = oaTrader.getId();
            int i = topNotifyMapper.deleteTopNotifyByOaNotifyTypeAndOaApplyId(oaApplyType, oaApplyId);
            int i1 = oaEditApproveGeneralityEditRecordsMapper.deleteOaEditApproveGeneralityEditRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
            int i2 = oaEditApproveGeneralityRecordsMapper.deleteOaEditApproveGeneralityRecordsByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
            int i3 = oaEditApproveGeneralityUserMapper.deleteOaEditApproveGeneralityUserByOaApplyTypeAndOaApplyId(oaApplyType, oaApplyId);
        }
        //删除动态表信息
        int i = oaTraderDynamicMapper.deleteByOaTraderId(ids);
        List<OaProjectPayerAssociation> collIds =  oaProjectPayerAssociationMapper.selectDataByCollId(ids);
        //置空相关
        for (OaProjectPayerAssociation collId : collIds) {
            collId.setCollId(null);
            collId.setCollName(null);
            collId.setCollBankOfDeposit(null);
            collId.setCollAbbreviation(null);
            collId.setCollAccountNumber(null);
            //删除原本的数据，因为修改的sql如果字段为空就不修改
            oaProjectPayerAssociationMapper.deleteOaProjectPayerAssociationById(collId.getId());
            oaProjectPayerAssociationMapper.insertOaProjectPayerAssociation(collId);
        }
        //处理付款人
        List<OaProjectPayerAssociation> payIds =  oaProjectPayerAssociationMapper.selectDataByPayId(ids);
        for (OaProjectPayerAssociation payId : payIds) {
            payId.setPayId(null);
            payId.setPayName(null);
            payId.setPayBankOfDeposit(null);
            payId.setPayAbbreviation(null);
            payId.setPayAccountNumber(null);
            oaProjectPayerAssociationMapper.deleteOaProjectPayerAssociationById(payId.getId());
            oaProjectPayerAssociationMapper.insertOaProjectPayerAssociation(payId);
        }

        int  i1= oaTraderMapper.deleteOaTraderByIds(ids);

        return  i1;
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaTraderById(Long id)
    {
        return oaTraderMapper.deleteOaTraderById(id);
    }

    @Override
    public int updateEnable(OaTrader user,LoginUser loginUser) {

        //得到插入生成的id 添加动态表
        OaTraderDynamic oaTraderDynamic = new OaTraderDynamic();
        oaTraderDynamic.setOaTraderId(user.getId());
        oaTraderDynamic.setOperationTime(DateUtils.getNowDate());

        if(user.getIsEnable().equals("Y")){
            oaTraderDynamic.setOperationContent("启用");
        }else if(user.getIsEnable().equals("N")){
            oaTraderDynamic.setOperationContent("停用");
        }

        oaTraderDynamic.setOperationBrId(loginUser.getUserId());
        oaTraderDynamic.setOperationBr(loginUser.getUser().getNickName());
        oaTraderDynamicMapper.insertOaTraderDynamic(oaTraderDynamic);
        return oaTraderMapper.updateOaTrader(user);
    }

    @Override
    public Map<String, Object> checkOaTrader(OaTrader oaTrader) {
        HashMap<String, Object> returnMap = new HashMap<>();
        OaTrader oaTrader1 = new OaTrader();
        oaTrader1.setAbbreviation(oaTrader.getAbbreviation());
//        oaTrader1.setAccountNumber(oaTrader.getAccountNumber());
//        oaTrader1.setBankOfDeposit(oaTrader.getBankOfDeposit());
//        oaTrader1.setTraderType(oaTrader.getTraderType());
//        oaTrader1.setType(oaTrader.getType());
        oaTrader1.setUserName(oaTrader.getUserName());
        oaTrader1.setId(oaTrader.getId());
        List<OaTrader> oaTraders = oaTraderMapper.selectOaTrader(oaTrader1);
        if(oaTraders.size() > 0){
            returnMap.put("isok","N");
        } else {
            returnMap.put("isok","Y");
        }
        return returnMap;
    }

    @Override
    public List<Map<String, Object>> getDataByParams(OaTrader oaTrader) {
        List<Map<String, Object>> maps = oaTraderMapper.queryDataByParams(oaTrader);
        return maps;
    }

    @Override
    public Long selectOaTraderListTotal(OaTrader oaTrader, List<Long> companyIdList) {
        return oaTraderMapper.selectOaTraderListTotal(oaTrader, companyIdList);
    }

    @Override
    public Long selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList(OaTrader oaTrader, List<Long> companyIdList, List<Long> filterOaApplyId) {
        return oaTraderMapper.selectOaTraderListTotalByOaTraderAndCompanyIdListAndFilterOaApplyIdList(oaTrader, companyIdList, filterOaApplyId);
    }

    @Override
    public List<OaTraderVo> selectOaTraderListNew(OaTrader oaTrader, List<Long> companyIdList, String isEnable) {
        //两种情况，一种是审批未打开，一种是审批打开
        //未打开的情况
//        if ("0".equals(isEnable)) {
        return oaTraderMapper.selectOaTraderListByCompanyIdList(oaTrader, companyIdList, isEnable);
//        } else {
//            return oaTraderMapper.selectOaTraderListByCompanyIdListByOaCheck(oaTrader, companyIdList);
//        }
    }

    @Override
    public List<AccountSetsInfoPo> selectAccountInfo(List<Long> companyIdList, String queryAllFlag) {
        List<AccountSetsInfoPo> accountSetsInfos = new ArrayList<>();
        if (queryAllFlag == null) {
            if (companyIdList.size() > 0) {
                accountSetsInfos = fcDataQueryMapper.getAccountSetsByCompanyIds(companyIdList);
            }
        } else {
            return fcDataQueryMapper.getAccountSetsByCompanyIds(companyIdList);
        }
        return accountSetsInfos;
    }

    @Override
    public int insertOaTraderNew(OaTrader oaTrader, LoginUser loginUser) {
        String currentOaApplyType = "0".equals(oaTrader.getTraderType())?"1":"1".equals(oaTrader.getTraderType())?"2":"9";
        String nickName = loginUser.getUser().getNickName();
        Date nowDate = DateUtils.getNowDate();
        oaTrader.setCreateBy(nickName);
        oaTrader.setUpdateBy(nickName);
        oaTrader.setEndUpdateTime(nowDate);
        oaTrader.setCreateTime(nowDate);
        oaTrader.setUpdateTime(nowDate);
        oaTrader.setAddNotApprove("96");
        int i = oaTraderMapper.insertOaTrader(oaTrader);
        Long oaTraderId = oaTrader.getId();
//        String jsonString = JSONObject.toJSONString(oaTrader);
        String oaApplyRecordsOldData = oaTrader.getOaApplyRecordsOldData();
        String oaApplyRecordsNewData = oaTrader.getOaApplyRecordsNewData();
//        //新增一条记录到oa_edit_approve_generality_records表中
//        OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//        oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//        oaEditApproveGeneralityRecords.setOaApplyId(oaTraderId);
//        oaEditApproveGeneralityRecords.setData(jsonString);
//        oaEditApproveGeneralityRecords.setStatus("0");
//        oaEditApproveGeneralityRecords.setCreateBy(nickName);
//        oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//        oaEditApproveGeneralityRecords.setUpdateBy(nickName);
//        oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//        int i22 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
        if (i > 0) {
//            Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
            OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = new OaEditApproveGeneralityEditRecords();
            oaEditApproveGeneralityEditRecords.setOaApplyType(currentOaApplyType);
            oaEditApproveGeneralityEditRecords.setOaApplyId(oaTraderId);
//            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldId(null);
//            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewId(oaApplyRecordsNewId);
            oaEditApproveGeneralityEditRecords.setEditUserId(loginUser.getUserId());
            oaEditApproveGeneralityEditRecords.setEditTime(DateUtils.getNowDate());
            oaEditApproveGeneralityEditRecords.setCheckStatus("96");
            oaEditApproveGeneralityEditRecords.setStatus("1");
            oaEditApproveGeneralityEditRecords.setEditType("0");
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsOldData(oaApplyRecordsOldData);
            oaEditApproveGeneralityEditRecords.setOaApplyRecordsNewData(oaApplyRecordsNewData);
            int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords);
        }
        return i;
    }

    @Override
    public int updateOaTraderNew(UpdateNewOaTraderBo updateNewOaTraderBo, LoginUser loginUser) {
        OaTrader newOaTrader = updateNewOaTraderBo.getNewOaTrader();
        Long oaTraderId = updateNewOaTraderBo.getOaTraderId();
        String nickName = loginUser.getUser().getNickName();
        Date nowDate = DateUtils.getNowDate();
        String currentOaApplyType = "0".equals(newOaTrader.getTraderType())?"1":"1".equals(newOaTrader.getTraderType())?"2":"9";
//        List<String> oaApplyTypeList = new ArrayList<>();
//        oaApplyTypeList.add("1");
//        oaApplyTypeList.add("2");
//        oaApplyTypeList.add("9");
//        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords = oaEditApproveGeneralityEditRecordsMapper.selectLastOaEditApproveGeneralityEditRecordsByOaApplyTypeListAndoaApplyId(oaApplyTypeList, oaTraderId);
//        Long lastOaApplyRecordsNewId = null;
//        if (oaEditApproveGeneralityEditRecords != null) {
//            lastOaApplyRecordsNewId = oaEditApproveGeneralityEditRecords.getOaApplyRecordsNewId();
//        }
        newOaTrader.setUpdateBy(nickName);
        newOaTrader.setEndUpdateTime(nowDate);
        newOaTrader.setUpdateTime(nowDate);
        newOaTrader.setAddNotApprove("96");
        int i = oaTraderMapper.updateOaTrader(newOaTrader);
        String oaApplyRecordsOldData = newOaTrader.getOaApplyRecordsOldData();
        String oaApplyRecordsNewData = newOaTrader.getOaApplyRecordsNewData();
//        String jsonStringNew = JSONObject.toJSONString(newOaTrader);
        //新增一条记录到oa_edit_approve_generality_records表中
//        OaEditApproveGeneralityRecords oaEditApproveGeneralityRecords = new OaEditApproveGeneralityRecords();
//        oaEditApproveGeneralityRecords.setOaApplyType(currentOaApplyType);
//        oaEditApproveGeneralityRecords.setOaApplyId(oaTraderId);
//        oaEditApproveGeneralityRecords.setData(jsonStringNew);
//        oaEditApproveGeneralityRecords.setStatus("0");
//        oaEditApproveGeneralityRecords.setCreateBy(nickName);
//        oaEditApproveGeneralityRecords.setCreateTime(nowDate);
//        oaEditApproveGeneralityRecords.setUpdateBy(nickName);
//        oaEditApproveGeneralityRecords.setUpdateTime(nowDate);
//        int i1 = oaEditApproveGeneralityRecordsMapper.insertOaEditApproveGeneralityRecords(oaEditApproveGeneralityRecords);
        //新增的id获取到，添加一个记录到oa_edit_approve_generality_edit_records表中
//        if (i1 > 0) {
//        Long oaApplyRecordsNewId = oaEditApproveGeneralityRecords.getId();
        OaEditApproveGeneralityEditRecords oaEditApproveGeneralityEditRecords1 = new OaEditApproveGeneralityEditRecords();
        oaEditApproveGeneralityEditRecords1.setOaApplyType(currentOaApplyType);
        oaEditApproveGeneralityEditRecords1.setOaApplyId(oaTraderId);
//        oaEditApproveGeneralityEditRecords1.setOaApplyRecordsOldId(lastOaApplyRecordsNewId);
//        oaEditApproveGeneralityEditRecords1.setOaApplyRecordsNewId(oaApplyRecordsNewId);
        oaEditApproveGeneralityEditRecords1.setEditUserId(loginUser.getUserId());
        oaEditApproveGeneralityEditRecords1.setEditTime(DateUtils.getNowDate());
        oaEditApproveGeneralityEditRecords1.setCheckStatus("96");
        oaEditApproveGeneralityEditRecords1.setStatus("1");
        oaEditApproveGeneralityEditRecords1.setEditType("1");
        oaEditApproveGeneralityEditRecords1.setOaApplyRecordsOldData(oaApplyRecordsOldData);
        oaEditApproveGeneralityEditRecords1.setOaApplyRecordsNewData(oaApplyRecordsNewData);
        int i2 = oaEditApproveGeneralityEditRecordsMapper.insertOaEditApproveGeneralityEditRecords(oaEditApproveGeneralityEditRecords1);
//        }
        return i;
    }
}
