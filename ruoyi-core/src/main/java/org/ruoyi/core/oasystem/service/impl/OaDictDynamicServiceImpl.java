package org.ruoyi.core.oasystem.service.impl;

import java.util.List;

import org.ruoyi.core.oasystem.domain.OaDictDynamic;
import org.ruoyi.core.oasystem.mapper.OaDictDynamicMapper;
import org.ruoyi.core.oasystem.service.IOaDictDynamicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-11
 */
@Service
public class OaDictDynamicServiceImpl implements IOaDictDynamicService
{
    @Autowired
    private OaDictDynamicMapper oaDictDynamicMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public OaDictDynamic selectOaDictDynamicById(Long id)
    {
        return oaDictDynamicMapper.selectOaDictDynamicById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param oaDictDynamic 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<OaDictDynamic> selectOaDictDynamicList(OaDictDynamic oaDictDynamic)
    {
        return oaDictDynamicMapper.selectOaDictDynamicList(oaDictDynamic);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param oaDictDynamic 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertOaDictDynamic(OaDictDynamic oaDictDynamic)
    {
        return oaDictDynamicMapper.insertOaDictDynamic(oaDictDynamic);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param oaDictDynamic 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateOaDictDynamic(OaDictDynamic oaDictDynamic)
    {
        return oaDictDynamicMapper.updateOaDictDynamic(oaDictDynamic);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaDictDynamicByIds(Long[] ids)
    {
        return oaDictDynamicMapper.deleteOaDictDynamicByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteOaDictDynamicById(Long id)
    {
        return oaDictDynamicMapper.deleteOaDictDynamicById(id);
    }
}
