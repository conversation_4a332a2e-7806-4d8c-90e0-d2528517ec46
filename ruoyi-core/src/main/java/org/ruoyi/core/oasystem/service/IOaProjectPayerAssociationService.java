package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.oasystem.domain.OaProjectPayerAssociation;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IOaProjectPayerAssociationService.java
 * @Description TODO
 * @createTime 2023年07月06日 13:39:00
 */
public interface IOaProjectPayerAssociationService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaProjectPayerAssociation selectOaProjectPayerAssociationById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaProjectPayerAssociation 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<OaProjectPayerAssociation> selectOaProjectPayerAssociationList(OaProjectPayerAssociation oaProjectPayerAssociation);

    /**
     * 新增【请填写功能名称】
     *
     * @param oaProjectPayerAssociation 【请填写功能名称】
     * @return 结果
     */
    public int insertOaProjectPayerAssociation(OaProjectPayerAssociation oaProjectPayerAssociation);

    /**
     * 修改【请填写功能名称】
     *
     * @param oaProjectPayerAssociation 【请填写功能名称】
     * @return 结果
     */
    public int updateOaProjectPayerAssociation(OaProjectPayerAssociation oaProjectPayerAssociation);
    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaProjectPayerAssociationByIds(Long[] ids);
    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaProjectPayerAssociationById(Long id);
}
