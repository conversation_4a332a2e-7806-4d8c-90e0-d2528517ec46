package org.ruoyi.core.oasystem.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 【请填写功能名称】对象 oa_caiwu_query
 * 
 * <AUTHOR>
 * @date 2024-03-26
 */
public class OaCaiwuQuery extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 流程id */
    private String pfdId;

    /** 流程关联的公司id */
    private Long companyId;
    /** 所属公司 */
    @Excel(name = "所属公司")
    private String companyName;

    /** 流程模板id */
    private Long templateId;

    @Excel(name = "流程模板名称")
    private String templateName;

    /** 申请流程的用户id */
    private Long userId;

    @Excel(name = "用户名称")
    private String userName;
    /** 主题 */
    @Excel(name = "主题")
    private String theme;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkTime;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal amount;

    /** 0已审核1未审核 */
    @Excel(name = "0已审核1未审核")
    private String chunaCheck;

    /** 0已生成1未全部生成2未生成异常3未生成 */
    @Excel(name = "0已生成1未全部生成2未生成异常3未生成")
    private String voucharGenerate;

    /** 凭证数量 */
    @Excel(name = "凭证数量")
    private String voucharNum;


    private BigDecimal minAmount;
    private BigDecimal maxAmount;

   private Date beginTime;
   private Date endTime;

   /** 查询范围 1-我发起的流程 2-我已审批*/
   @NotBlank(message = "查询范围不可为空")
   @Pattern(regexp = "[1-3]",message = "查询范围只能为1、2、3")
   private String queryScope;

    public BigDecimal getMinAmount() {
        return minAmount;
    }

    public void setMinAmount(BigDecimal minAmount) {
        this.minAmount = minAmount;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPfdId() {
        return pfdId;
    }

    public void setPfdId(String pfdId) {
        this.pfdId = pfdId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getChunaCheck() {
        return chunaCheck;
    }

    public void setChunaCheck(String chunaCheck) {
        this.chunaCheck = chunaCheck;
    }

    public String getVoucharGenerate() {
        return voucharGenerate;
    }

    public void setVoucharGenerate(String voucharGenerate) {
        this.voucharGenerate = voucharGenerate;
    }

    public String getVoucharNum() {
        return voucharNum;
    }

    public void setVoucharNum(String voucharNum) {
        this.voucharNum = voucharNum;
    }

    public String getQueryScope() {
        return queryScope;
    }

    public void setQueryScope(String queryScope) {
        this.queryScope = queryScope;
    }

    public OaCaiwuQuery() {
    }

    public OaCaiwuQuery(Long id, String pfdId, Long companyId, String companyName, Long templateId, String templateName, Long userId, String userName, String theme, Date checkTime, BigDecimal amount, String chunaCheck, String voucharGenerate, String voucharNum, BigDecimal minAmount, BigDecimal maxAmount, Date beginTime, Date endTime,String queryScope) {
        this.id = id;
        this.pfdId = pfdId;
        this.companyId = companyId;
        this.companyName = companyName;
        this.templateId = templateId;
        this.templateName = templateName;
        this.userId = userId;
        this.userName = userName;
        this.theme = theme;
        this.checkTime = checkTime;
        this.amount = amount;
        this.chunaCheck = chunaCheck;
        this.voucharGenerate = voucharGenerate;
        this.voucharNum = voucharNum;
        this.minAmount = minAmount;
        this.maxAmount = maxAmount;
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.queryScope =queryScope;
    }
}
