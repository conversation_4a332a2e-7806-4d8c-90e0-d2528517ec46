package org.ruoyi.core.oasystem.service;

import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.oasystem.domain.OaVoucherRulesVice;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IOaVoucherRulesViceService.java
 * @Description TODO
 * @createTime 2023年11月09日 14:28:00
 */
public interface IOaVoucherRulesViceService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaVoucherRulesVice selectOaVoucherRulesViceById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaVoucherRulesVice 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<OaVoucherRulesVice> selectOaVoucherRulesViceList(OaVoucherRulesVice oaVoucherRulesVice);

    /**
     * 新增【请填写功能名称】
     *
     * @param oaVoucherRulesVice 【请填写功能名称】
     * @return 结果
     */
    public int insertOaVoucherRulesVice(OaVoucherRulesVice oaVoucherRulesVice);

    /**
     * 修改【请填写功能名称】
     *
     * @param oaVoucherRulesVice 【请填写功能名称】
     * @return 结果
     */
    public int updateOaVoucherRulesVice(OaVoucherRulesVice oaVoucherRulesVice);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaVoucherRulesViceByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaVoucherRulesViceById(Long id);
}
