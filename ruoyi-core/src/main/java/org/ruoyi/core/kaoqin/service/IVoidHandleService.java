package org.ruoyi.core.kaoqin.service;

import org.ruoyi.core.kaoqin.domain.VoidHandle;
import org.ruoyi.core.kaoqin.domain.vo.VoidHandleVo;
import java.util.List;

/**
 * 废弃考勤处理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface IVoidHandleService
{
    /**
     * 查询废弃考勤处理
     *
     * @param id 废弃考勤处理主键
     * @return 废弃考勤处理
     */
    public VoidHandle selectVoidHandleById(Long id);

    /**
     * 查询废弃考勤处理列表
     *
     * @param voidHandle 废弃考勤处理
     * @return 废弃考勤处理集合
     */
    public List<VoidHandleVo> selectVoidHandleList(VoidHandleVo voidHandle);

    /**
     * 新增废弃考勤处理
     *
     * @param voidHandle 废弃考勤处理
     * @return 结果
     */
    public int insertVoidHandle(VoidHandle voidHandle);

    /**
     * 修改废弃考勤处理
     *
     * @param voidHandle 废弃考勤处理
     * @return 结果
     */
    public int updateVoidHandle(VoidHandle voidHandle);

    public int notify(VoidHandleVo voidHandle);

    /**
     * 批量删除废弃考勤处理
     *
     * @param ids 需要删除的废弃考勤处理主键集合
     * @return 结果
     */
    public int deleteVoidHandleByIds(Long[] ids);

    /**
     * 删除废弃考勤处理信息
     *
     * @param id 废弃考勤处理主键
     * @return 结果
     */
    public int deleteVoidHandleById(Long id);
}
