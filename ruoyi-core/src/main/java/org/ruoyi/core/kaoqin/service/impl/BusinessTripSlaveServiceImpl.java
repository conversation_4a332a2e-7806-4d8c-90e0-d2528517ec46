package org.ruoyi.core.kaoqin.service.impl;

import java.util.List;

import org.ruoyi.core.kaoqin.domain.BusinessTripSlave;
import org.ruoyi.core.kaoqin.mapper.BusinessTripSlaveMapper;
import org.ruoyi.core.kaoqin.service.IBusinessTripSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 出差申请-从Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
@Service
public class BusinessTripSlaveServiceImpl implements IBusinessTripSlaveService
{
    @Autowired
    private BusinessTripSlaveMapper businessTripSlaveMapper;

    /**
     * 查询出差申请-从
     *
     * @param id 出差申请-从主键
     * @return 出差申请-从
     */
    @Override
    public BusinessTripSlave selectBusinessTripSlaveById(Long id)
    {
        return businessTripSlaveMapper.selectBusinessTripSlaveById(id);
    }

    /**
     * 查询出差申请-从列表
     *
     * @param businessTripSlave 出差申请-从
     * @return 出差申请-从
     */
    @Override
    public List<BusinessTripSlave> selectBusinessTripSlaveList(BusinessTripSlave businessTripSlave)
    {
        return businessTripSlaveMapper.selectBusinessTripSlaveList(businessTripSlave);
    }

    /**
     * 新增出差申请-从
     *
     * @param businessTripSlave 出差申请-从
     * @return 结果
     */
    @Override
    public int insertBusinessTripSlave(BusinessTripSlave businessTripSlave)
    {
        return businessTripSlaveMapper.insertBusinessTripSlave(businessTripSlave);
    }

    /**
     * 修改出差申请-从
     *
     * @param businessTripSlave 出差申请-从
     * @return 结果
     */
    @Override
    public int updateBusinessTripSlave(BusinessTripSlave businessTripSlave)
    {
        return businessTripSlaveMapper.updateBusinessTripSlave(businessTripSlave);
    }

    /**
     * 批量删除出差申请-从
     *
     * @param ids 需要删除的出差申请-从主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTripSlaveByIds(Long[] ids)
    {
        return businessTripSlaveMapper.deleteBusinessTripSlaveByIds(ids);
    }

    /**
     * 删除出差申请-从信息
     *
     * @param id 出差申请-从主键
     * @return 结果
     */
    @Override
    public int deleteBusinessTripSlaveById(Long id)
    {
        return businessTripSlaveMapper.deleteBusinessTripSlaveById(id);
    }

    @Override
    public int insertBusinessTripSlaveBatch(List<BusinessTripSlave> businessTripSlaveList) {
        return businessTripSlaveMapper.insertBusinessTripSlaveBatch(businessTripSlaveList);
    }

    @Override
    public int deleteBusinessTripSlaveByMainId(Long mainId) {
        return businessTripSlaveMapper.deleteBusinessTripSlaveByMainId(mainId);
    }
}
