package org.ruoyi.core.controller.data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import com.ruoyi.system.mapper.SystemSelectTypeMapper;
import com.ruoyi.system.service.impl.SysCompanyServiceImpl;
import org.ruoyi.core.domain.SysSelectDataRef;
import org.ruoyi.core.oasystem.domain.ProjectCompanyRelevance;
import org.ruoyi.core.service.ISysSelectDataRefService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@RestController
@RequestMapping("/system/selectRef")
public class SysSelectDataRefController extends BaseController
{
    @Autowired
    private ISysSelectDataRefService sysSelectDataRefService;

    @Autowired
    private SystemSelectTypeMapper systemSelectTypeMapper;

    @Autowired
    private SysCompanyServiceImpl sysCompanyService;
    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:ref:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysSelectDataRef sysSelectDataRef)
    {
        startPage();
        List<SysSelectDataRef> list = sysSelectDataRefService.selectSysSelectDataRefList(sysSelectDataRef);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:ref:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSelectDataRef sysSelectDataRef)
    {
        List<SysSelectDataRef> list = sysSelectDataRefService.selectSysSelectDataRefList(sysSelectDataRef);
        ExcelUtil<SysSelectDataRef> util = new ExcelUtil<SysSelectDataRef>(SysSelectDataRef.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:ref:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(sysSelectDataRefService.selectSysSelectDataRefById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:ref:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysSelectDataRef sysSelectDataRef)
    {
        return toAjax(sysSelectDataRefService.insertSysSelectDataRef(sysSelectDataRef));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:ref:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysSelectDataRef sysSelectDataRef)
    {
        return toAjax(sysSelectDataRefService.updateSysSelectDataRef(sysSelectDataRef));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:ref:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysSelectDataRefService.deleteSysSelectDataRefByIds(ids));
    }


    @GetMapping("/getSelectDataList")
    public List<Map<String,Object>> getUserComPany(ProjectCompanyRelevance projectCompanyRelevance)
    {
        List<Map<String, Object>> mapList = new ArrayList<>();
                LoginUser loginUser = getLoginUser();
        String unitType = projectCompanyRelevance.getUnitType();
        if (unitType.equals("0") || unitType.equals("1") || unitType.equals("2")) {
            String selectType = null;
            switch (unitType){
                case "0":
                    selectType = "cust";
                    break;
                case "1":
                    selectType = "partner";
                    break;
                case "2":
                    selectType = "fund";
                    break;
            }

            List<String> companyTypeId =  systemSelectTypeMapper.queryDataByModelCode(selectType,projectCompanyRelevance.getModuleTypeOfNewAuth());

            //
            if(companyTypeId.size() > 0){
                List<SysCompanyVo>  sysCompanyVos =  sysCompanyService.getCompanyBySelectType(companyTypeId);
                List<Map<String, Object>> ruleCompanyList = sysSelectDataRefService.queryCompanyByProjectId(projectCompanyRelevance, loginUser);
                for (SysCompanyVo sysCompanyVo : sysCompanyVos) {
                    for (Map<String, Object> map : ruleCompanyList) {
                        if((sysCompanyVo.getId().toString()).equals(map.get("value").toString())){
                            mapList.add(map);
                            break;
                        }
                    }
                    continue;
                }

            }else {
                mapList =  sysSelectDataRefService.queryCompanyByProjectId(projectCompanyRelevance, loginUser);
            }
        }else {
            mapList =  sysSelectDataRefService.queryCompanyByProjectId(projectCompanyRelevance, loginUser);
        }





//        List<Map<String, Object>> mapList = sysSelectDataRefService.queryCompanyByProjectId(projectCompanyRelevance, loginUser);
        return mapList;
    }

}
