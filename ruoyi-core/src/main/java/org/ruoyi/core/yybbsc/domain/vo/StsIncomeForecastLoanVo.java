package org.ruoyi.core.yybbsc.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收入预测报表 - 放款表前端展示VO
 *
 * <AUTHOR>
 * @date 2023-01-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class StsIncomeForecastLoanVo {

    private static final long serialVersionUID = 1L;

    /** 放款月份 */
    @Excel(name = "放款月份")
    private String loanMonth;

    /** 放款表主键 */
    private Long loanId;

    /** 放款金额 */
    @Excel(name = "放款金额")
    private BigDecimal loanAmt;

    /** 0-无，1-随借随还，2-等本等息，3-先息后本 */
    @Excel(name = "产品类型")
    private String productType;

    /** 期数 */
    @Excel(name = "期数")
    private Integer phase;

    /** 还款月份 */
    @Excel(name = "还款月份")
    private String repaymentMonth;

    /** 还款本金 */
    @Excel(name = "还款本金")
    private BigDecimal repaymentPrintAmount;

    /** 还款利息 */
    @Excel(name = "还款利息")
    private BigDecimal repaymentIntAmount;

    /** 还款罚息 */
    @Excel(name = "还款罚息")
    private BigDecimal repaymentOintAmt;

    /** 还款复利 */
    @Excel(name = "还款复利")
    private BigDecimal repaymentFlAmt;

    /** 提前还款违约金 */
    @Excel(name = "提前还款违约金")
    private BigDecimal advDefineAmt;

    /** 活动抵扣金额 */
    @Excel(name = "活动抵扣金额")
    private BigDecimal deductAmt;

    /** 红线减免金额 */
    @Excel(name = "红线减免金额")
    private BigDecimal reduceAmt;

    /** 月末余额 */
    @Excel(name = "月末余额")
    private BigDecimal monthEndBalanceAmt;

    /** 技术服务费 */
    @Excel(name = "技术服务费")
    private BigDecimal technicalServiceFee;
}
