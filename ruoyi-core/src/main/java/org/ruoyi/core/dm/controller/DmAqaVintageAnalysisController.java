package org.ruoyi.core.dm.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.Gson;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.dm.domain.DmAqaVintageAnalysis;
import org.ruoyi.core.dm.domain.vo.DmAqaVintageAnalysisVo;
import org.ruoyi.core.dm.service.IDmAqaVintageAnalysisService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 产品vintage分析Controller
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@RestController
@RequestMapping("/vintage/analysis")
public class DmAqaVintageAnalysisController extends BaseController
{
    @Autowired
    private IDmAqaVintageAnalysisService dmAqaVintageAnalysisService;

    /**
     * 查询产品vintage分析列表
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody DmAqaVintageAnalysisVo dmAqaVintageAnalysis)
    {
        List<DmAqaVintageAnalysisVo> list = dmAqaVintageAnalysisService.selectDmAqaVintageAnalysisList(dmAqaVintageAnalysis);
        return getDataTable(list);
    }

    //@PreAuthorize("@ss.hasPermi('system:analysis:list')")
    @GetMapping("/echarts")
    public Map<String,Object> echarts(DmAqaVintageAnalysisVo dmAqaVintageAnalysis)
    {
        //startPage();
        Map<String, Object> stringObjectMap = dmAqaVintageAnalysisService.selectDmAqaVintageAnalysisEcharts(dmAqaVintageAnalysis);
        return stringObjectMap;
    }

    /**
     * 导出产品vintage分析列表
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:export')")
    @Log(title = "产品vintage分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,DmAqaVintageAnalysisVo dmAqaVintageAnalysis)
    {
        Gson gson = new Gson();
        Map<String,String> resultMap = gson.fromJson(dmAqaVintageAnalysis.getSortMapString(), Map.class);
        dmAqaVintageAnalysis.setSortMap(resultMap);
        List<DmAqaVintageAnalysisVo> list = dmAqaVintageAnalysisService.selectDmAqaVintageAnalysisList(dmAqaVintageAnalysis);
        ExcelUtil<DmAqaVintageAnalysisVo> util = new ExcelUtil<DmAqaVintageAnalysisVo>(DmAqaVintageAnalysisVo.class);
        util.exportExcel(response, list, "产品vintage分析数据");
    }

    /**
     * 获取产品vintage分析详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dmAqaVintageAnalysisService.selectDmAqaVintageAnalysisById(id));
    }

    /**
     * 新增产品vintage分析
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:add')")
    @Log(title = "产品vintage分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DmAqaVintageAnalysis dmAqaVintageAnalysis)
    {
        return toAjax(dmAqaVintageAnalysisService.insertDmAqaVintageAnalysis(dmAqaVintageAnalysis));
    }

    /**
     * 修改产品vintage分析
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:edit')")
    @Log(title = "产品vintage分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DmAqaVintageAnalysis dmAqaVintageAnalysis)
    {
        return toAjax(dmAqaVintageAnalysisService.updateDmAqaVintageAnalysis(dmAqaVintageAnalysis));
    }

    /**
     * 删除产品vintage分析
     */
    //@PreAuthorize("@ss.hasPermi('system:analysis:remove')")
    @Log(title = "产品vintage分析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dmAqaVintageAnalysisService.deleteDmAqaVintageAnalysisByIds(ids));
    }
}
