package org.ruoyi.core.ancun.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ancun.netsign.model.ApiRespBody;
import com.ancun.netsign.model.AuthInput;
import com.ancun.netsign.model.AuthOutput;
import org.ruoyi.core.ancun.AncunClient;
import org.ruoyi.core.ancun.service.IAuthPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class AuthPersonService implements IAuthPersonService{

    @Autowired
    private AncunClient ancunClient;

    public ApiRespBody<AuthOutput> getIdentifyUrl(AuthInput input) {
        ApiRespBody<AuthOutput> apiRespBody = ancunClient.netSignClient.getPersonIdentifyUrl(input);
        if (apiRespBody.success()) {
            System.out.println(JSONObject.toJSONString(apiRespBody.getData()));
        }
        return apiRespBody;
    }
}
