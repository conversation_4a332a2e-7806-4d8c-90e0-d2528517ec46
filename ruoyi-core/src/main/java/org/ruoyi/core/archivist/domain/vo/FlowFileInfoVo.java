package org.ruoyi.core.archivist.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
/**
 * 审批流程文件对象
 *
 * @Description
 * @<PERSON> <PERSON><PERSON>
 * @Date 2023/9/22 14:59
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FlowFileInfoVo {
        private static final long serialVersionUID = 1L;

        private String businessId;

        private String stepId;

        private String soleFlag;

        private String fileName;

        private String url;

        private Long uploadUserId;

        private String createBy;

        private Date createTime;

        private String updateBy;

        private Date updateTime;
    }

