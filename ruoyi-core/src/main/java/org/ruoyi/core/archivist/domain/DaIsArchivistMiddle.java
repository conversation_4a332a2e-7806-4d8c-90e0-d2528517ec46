package org.ruoyi.core.archivist.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 流程是否归档中间对象 da_is_archivist_middle
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public class DaIsArchivistMiddle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 流程id */
    @Excel(name = "流程id")
    private String flowId;

    /** 档案管理员登录用户名 */
    @Excel(name = "档案管理员登录用户名")
    private String archivistBy;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private String auditStatus;

    /** 是否归档 (0已归档，默认1未归档)*/
    @Excel(name = "是否归档")
    private String isArchivist;

    /** 档案id */
    @Excel(name = "档案id")
    private String archivistId;

    /** 是否归档模板 */
    private String isFiling;

    /** 创建人 */
    private String createBy;

    /** 当前用户是否是档案管理员标识 */
    private Boolean dagly1Flag;

    public Boolean getDagly1Flag() {
        return dagly1Flag;
    }

    public void setDagly1Flag(Boolean dagly1Flag) {
        this.dagly1Flag = dagly1Flag;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getIsFiling() {
        return isFiling;
    }

    public void setIsFiling(String isFiling) {
        this.isFiling = isFiling;
    }

    public void setFlowId(String flowId)
    {
        this.flowId = flowId;
    }

    public String getFlowId()
    {
        return flowId;
    }
    public void setArchivistBy(String archivistBy)
    {
        this.archivistBy = archivistBy;
    }

    public String getArchivistBy()
    {
        return archivistBy;
    }
    public void setAuditStatus(String auditStatus)
    {
        this.auditStatus = auditStatus;
    }

    public String getAuditStatus()
    {
        return auditStatus;
    }
    public void setIsArchivist(String isArchivist)
    {
        this.isArchivist = isArchivist;
    }

    public String getIsArchivist()
    {
        return isArchivist;
    }
    public void setArchivistId(String archivistId)
    {
        this.archivistId = archivistId;
    }

    public String getArchivistId()
    {
        return archivistId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("flowId", getFlowId())
                .append("archivistBy", getArchivistBy())
                .append("auditStatus", getAuditStatus())
                .append("isArchivist", getIsArchivist())
                .append("archivistId", getArchivistId())
                .append("isFiling", getIsFiling())
                .toString();
    }
}
