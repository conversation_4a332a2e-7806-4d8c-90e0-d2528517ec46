package org.ruoyi.core.superviseInformation.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.constant.UploadFeatureConstants;
import org.ruoyi.core.superviseInformation.domain.SuperviseInformation;
import org.ruoyi.core.superviseInformation.domain.SuperviseInformationReleaseStatistics;
import org.ruoyi.core.superviseInformation.domain.vo.BArchivist;
import org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationFileUtils;
import org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationVO;
import org.ruoyi.core.superviseInformation.service.ISuperviseInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;

/**
 * 资料Controller
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@RestController
@RequestMapping("/supervise/information")
public class SuperviseInformationController extends BaseController
{
    @Autowired
    private ISuperviseInformationService informationService;

    /**
     * 查询【资料】列表
     *
     */

    //@PreAuthorize("@ss.hasPermi('system:information:list')")
    @GetMapping("/list")
    public TableDataInfo list(SuperviseInformationVO information)
    {
        //startPage();
        List<SuperviseInformationVO> list = informationService.selectInformationList(information);
        return getDataTable(list);
    }

    //@PreAuthorize("@ss.hasPermi('system:information:list')")
    @GetMapping("/allList")
    public TableDataInfo AllList(SuperviseInformationVO information)
    {
        //startPage();
        List<SuperviseInformationVO> list = informationService.selectAllInformationList(information);
        return getDataTable(list);
    }

    /**
     * 废弃列表
     * @param information
     * @return
     */
    @GetMapping("/abandonedList")
    public TableDataInfo abandonedList(SuperviseInformationVO information)
    {
        //startPage();
        List<SuperviseInformationVO> list = informationService.selectInformationAbandonedList(information);
        return getDataTable(list);
    }

    /**
     * 导出资料列表
     */
    //@PreAuthorize("@ss.hasPermi('system:information:export')")
    @Log(title = "导出授权资料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SuperviseInformationVO information)
    {
        List<SuperviseInformationVO> list = informationService.exportInformationList(information);
        ExcelUtil<SuperviseInformationVO> util = new ExcelUtil<SuperviseInformationVO>(SuperviseInformationVO.class);
        util.exportExcel(response, list, "资料数据");
    }

    /**
     * 导出资料列表
     */
    //@PreAuthorize("@ss.hasPermi('system:information:export')")
    @Log(title = "导出全部资料", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAll")
    public void exportAll(HttpServletResponse response, SuperviseInformationVO information)
    {
        List<SuperviseInformationVO> list = informationService.exportAllInformationList(information);
        ExcelUtil<SuperviseInformationVO> util = new ExcelUtil<SuperviseInformationVO>(SuperviseInformationVO.class);
        util.exportExcel(response, list, "资料数据");
    }

    /**
     * 获取资料详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:information:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(informationService.selectInformationById(id));
    }

    /**
     * 新增【资料新增】
     */
    //@PreAuthorize("@ss.hasPermi('system:information:add')")
    @Log(title = "【资料新增】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SuperviseInformation information)
    {
        return toAjax(informationService.insertInformation(information));
    }

    /**
     * 新增【资料新增】
     */
    //@PreAuthorize("@ss.hasPermi('system:information:add')")
    @Log(title = "【资料新增】", businessType = BusinessType.INSERT)
    @PostMapping(value = "/insertCommitInformation")
    public AjaxResult insertCommitInformation(@Validated @RequestBody SuperviseInformationVO information)
    {
        return informationService.insertCommitInformation(information);
    }


    /**
     * 新增临时【资料新增】
     */
    //@PreAuthorize("@ss.hasPermi('system:information:add')")
    @Log(title = "【资料新增】", businessType = BusinessType.INSERT)
    @PostMapping(value = "/temporary")
    public AjaxResult addTemporary(@RequestBody SuperviseInformation information)
    {
        return informationService.insertTemporaryInformation(information);
    }

    /**
     * 修改资料
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "修改资料", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SuperviseInformation information)
    {
        return toAjax(informationService.updateInformation(information));
    }

    /**
     * 删除资料
     */
    //@PreAuthorize("@ss.hasPermi('system:information:remove')")
    @Log(title = "删除资料", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(informationService.deleteInformationByIds(ids));
    }

    /**
     * 提交资料
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "提交资料", businessType = BusinessType.UPDATE)
    @PostMapping("/commitInformations")
    public AjaxResult commitInformationByIds(@RequestBody Long[] ids)
    {
        return toAjax(informationService.commitInformationByIds(ids));
    }

    @Log(title = "校验提交资料", businessType = BusinessType.UPDATE)
    @PostMapping("/commitCheck")
    public AjaxResult commitCheck(@RequestBody Long[] ids)
    {
        return informationService.commitCheck(ids);
    }


    /**
     * 赋权资料
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "赋权资料", businessType = BusinessType.UPDATE)
    @PostMapping("/empowerInformations")
    public AjaxResult empowerInformationByIds(@RequestBody Long[] ids)
    {
        return toAjax(informationService.empowerInformationByIds(ids));
    }

    /**
     * 查询【资料】列表
     */

    //@PreAuthorize("@ss.hasPermi('system:information:list')")
//    @GetMapping("/downloadList")
//    public TableDataInfo downloadList()
//    {
//        startPage();
//        List<SuperviseInformation> list = informationService.getDownloadList();
//        return getDataTable(list);
//    }

    @Anonymous
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(@RequestParam("file")MultipartFile file) {
        try {
            String name = file.getOriginalFilename();
            String url = FileUploadUtils.uploadOSS(UploadFeatureConstants.INFORMATION_SYSTEM, file);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("name", name);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取过期资料
     * @param information
     * @return
     */
    //@PreAuthorize("@ss.hasPermi('system:information:list')")
    @GetMapping("/getExpireList")
    public TableDataInfo getExpireList(SuperviseInformation information)
    {
        startPage();
        List<SuperviseInformation> list = informationService.getExpireList(information);
        return getDataTable(list);
    }

    //@PreAuthorize("@ss.hasPermi('system:information:list')")
    @GetMapping("/getListByIds")
    public TableDataInfo getListByIds(@RequestParam("ids") Long[] ids)
    {
        startPage();
        List<SuperviseInformation> list = informationService.getListByIds(ids);
        return getDataTable(list);
    }

    /**
     * 修改资料--审核通过
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "修改资料--审核通过", businessType = BusinessType.UPDATE)
    @PostMapping("/passInformations")
    public AjaxResult passInformationByIds(@RequestBody Long[] ids)
    {
        return toAjax(informationService.passInformationByIds(ids));
    }

    /**
     * 修改资料--审核不通过
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "修改资料--审核不通过", businessType = BusinessType.UPDATE)
    @PostMapping("/unPassInformations")
    public AjaxResult unPassInformationByIds(@RequestBody Long[] ids)
    {
        return toAjax(informationService.unPassInformationByIds(ids));
    }


    /**
     * 修改资料--废弃
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "修改资料--废弃", businessType = BusinessType.UPDATE)
    @PostMapping("/abandonedInformation")
    public AjaxResult abandonedInformationByIds(@RequestBody Long[] ids)
    {
        return toAjax(informationService.abandonedInformationByIds(ids));
    }

    /**
     * 修改资料--废弃重新启用
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "修改资料--废弃重新启用", businessType = BusinessType.UPDATE)
    @PostMapping("/unabandonedInformation")
    public AjaxResult unabandonedInformationByIds(@RequestBody Long[] ids)
    {
        return toAjax(informationService.unabandonedInformationByIds(ids));
    }

    /**
     * 获取资料详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:information:query')")
    @GetMapping(value = "/isAuthority/{catalogueId}")
    public AjaxResult isAuthority(@PathVariable("catalogueId") Long catalogueId)
    {
        return AjaxResult.success(informationService.isAuthority(catalogueId));
    }

    /**
     * 获取全量公司名称--查询条件用
     * @return  全量公司名称
     */
    @GetMapping("/getUnit")
    public TableDataInfo getUnit()
    {
        return getDataTable(informationService.getUnit());
    }

    /**
     * 获取资料发放统计列表
     * @param information
     * @return
     */
    @GetMapping("/getReleaseStatistics")
    public TableDataInfo getReleaseStatistics(SuperviseInformationReleaseStatistics information)
    {
        List<SuperviseInformationReleaseStatistics> list = informationService.getReleaseStatistics(information);
        return getDataTable(list);
    }

    /**
     * 导出发放统计报表
     */
    //@PreAuthorize("@ss.hasPermi('system:information:export')")
    //@Log(title = "导出资料发放统计报表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportReleaseStatistics")
    public void exportReleaseStatistics(HttpServletResponse response, SuperviseInformationReleaseStatistics information)
    {
        List<SuperviseInformationReleaseStatistics> list = informationService.exportReleaseStatistics(information);
        ExcelUtil<SuperviseInformationReleaseStatistics> util = new ExcelUtil<SuperviseInformationReleaseStatistics>(SuperviseInformationReleaseStatistics.class);
        util.exportExcel(response, list, "资料数据");
    }

    /**
     * 整理资料
     */
    //@PreAuthorize("@ss.hasPermi('system:information:edit')")
    @Log(title = "修改资料", businessType = BusinessType.UPDATE)
    @PutMapping("/arrangeInformation")
    public AjaxResult arrangeInformation(@RequestBody SuperviseInformationVO information)
    {
        return toAjax(informationService.arrangeInformation(information));
    }

    @PostMapping("/downloadFileZip")
    public void overDetailExport(HttpServletResponse response, SuperviseInformationVO ids){
        List<SuperviseInformation> auditPassList = informationService.getListByIds(ids.getIdArray().toArray(new Long[0]));
        SuperviseInformationFileUtils.createZipFromOSSFiles(response,auditPassList);
    }

    @GetMapping("/statisticsList")
    public TableDataInfo getinformationStatisticsList(SuperviseInformationVO information)
    {
        List<SuperviseInformationVO> list = informationService.getinformationStatisticsList(information);
        return getDataTable(list);
    }


    @Log(title = "【归档至资料】", businessType = BusinessType.INSERT)
    @PostMapping("/bArchivist")
    public AjaxResult addBContract(@Validated @RequestBody BArchivist bArchivist) throws ParseException {
        return toAjax(informationService.addBContract(bArchivist));
    }
}
