<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cwproject.mapper.CwProjectRejectionMapper">

    <resultMap type="CwProjectRejection" id="CwProjectRejectionResult">
        <result property="id"    column="id"    />
        <result property="incomeId"    column="income_id"    />
        <result property="parameterCode"    column="parameter_code"    />
        <result property="rejectionTime"    column="rejection_time"    />
        <result property="rejectionUserName"    column="rejection_user_name"    />
        <result property="rejectionReason"    column="rejection_reason"    />
    </resultMap>

    <sql id="selectCwProjectRejectionVo">
        select id, income_id, parameter_code, rejection_time, rejection_user_name, rejection_reason from cw_project_rejection
    </sql>

    <select id="selectCwProjectRejectionList" parameterType="CwProjectRejection" resultMap="CwProjectRejectionResult">
        <include refid="selectCwProjectRejectionVo"/>
        <where>
            <if test="incomeId != null "> and income_id = #{incomeId}</if>
            <if test="parameterCode != null  and parameterCode != ''"> and parameter_code = #{parameterCode}</if>
            <if test="rejectionTime != null "> and rejection_time = #{rejectionTime}</if>
            <if test="rejectionUserName != null  and rejectionUserName != ''"> and rejection_user_name like concat('%', #{rejectionUserName}, '%')</if>
            <if test="rejectionReason != null  and rejectionReason != ''"> and rejection_reason = #{rejectionReason}</if>
        </where>
    </select>

    <select id="selectCwProjectRejectionById" parameterType="Long" resultMap="CwProjectRejectionResult">
        <include refid="selectCwProjectRejectionVo"/>
        where id = #{id}
    </select>

    <insert id="insertCwProjectRejection" parameterType="CwProjectRejection" useGeneratedKeys="true" keyProperty="id">
        insert into cw_project_rejection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="incomeId != null">income_id,</if>
            <if test="parameterCode != null and parameterCode != ''">parameter_code,</if>
            <if test="rejectionTime != null">rejection_time,</if>
            <if test="rejectionUserName != null and rejectionUserName != ''">rejection_user_name,</if>
            <if test="rejectionReason != null and rejectionReason != ''">rejection_reason,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="incomeId != null">#{incomeId},</if>
            <if test="parameterCode != null and parameterCode != ''">#{parameterCode},</if>
            <if test="rejectionTime != null">#{rejectionTime},</if>
            <if test="rejectionUserName != null and rejectionUserName != ''">#{rejectionUserName},</if>
            <if test="rejectionReason != null and rejectionReason != ''">#{rejectionReason},</if>
         </trim>
    </insert>

    <update id="updateCwProjectRejection" parameterType="CwProjectRejection">
        update cw_project_rejection
        <trim prefix="SET" suffixOverrides=",">
            <if test="incomeId != null">income_id = #{incomeId},</if>
            <if test="parameterCode != null and parameterCode != ''">parameter_code = #{parameterCode},</if>
            <if test="rejectionTime != null">rejection_time = #{rejectionTime},</if>
            <if test="rejectionUserName != null and rejectionUserName != ''">rejection_user_name = #{rejectionUserName},</if>
            <if test="rejectionReason != null and rejectionReason != ''">rejection_reason = #{rejectionReason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCwProjectRejectionById" parameterType="Long">
        delete from cw_project_rejection where id = #{id}
    </delete>

    <select id="selectCwProjectRejectionByProjectIncomeId" resultType="org.ruoyi.core.cwproject.domain.CwProjectRejection">
        SELECT CASE WHEN parameter_code='0' THEN '收入驳回' WHEN parameter_code='1' THEN '返费驳回' ELSE '' END AS parameterCode,rejection_time AS rejectionTime,
        rejection_user_name AS rejectionUserName,rejection_reason AS rejectionReason
        FROM cw_project_rejection WHERE income_id=#{projectIncomeId,jdbcType=BIGINT} ORDER BY rejection_time DESC
    </select>
</mapper>