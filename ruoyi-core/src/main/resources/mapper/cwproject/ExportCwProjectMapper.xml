<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cwproject.mapper.ExportCwProjectMapper">
    

    <select id="selectExportCwProjectInfo"  resultType="org.ruoyi.core.cwproject.domain.export.ExcelCwProject">
         select prestore_income_flag AS prestoreIncomeFlag,id, project_name as projectName, cust_name as custName, income_cust_name as incomeCustName, project_flag as projectFlag
         from cw_project 
         where id = #{id} and status='0'
    </select>
    
    
    <select id="selectExportCwProjectCustList"  resultType="org.ruoyi.core.cwproject.domain.export.ExcelCwProjectCust">
        select id, project_id as projectId, cust_name as custName, rate, tax_rate as taxRate,
        CONCAT('方案', scheme_flag) AS schemeFlag,0 AS schemeFlagUseSituation
        from cw_project_cust
         where project_id = #{projectId} and status='0'
    </select>
    
    
    <select id="selectExportCwProjectIncomeList"  resultType="org.ruoyi.core.cwproject.domain.export.ExcelCwProjectIncome">
          select collection_time AS collectionTime, rejection_flag as rejectionFlag,id, project_id as projectId, term, term_month as termMonth, term_begin as termBegin, term_end as termEnd,
          income_amt as incomeAmt, gross_profit_amt as grossProfitAmt, gross_profit_amt2 as grossProfitAmt2,
          case when term='0' then term_month when term='1' then date_format(term_begin,'%Y-%m') else '' end as termMonthCompare,
          fee_amt as feeAmt, unfee_amt as unfeeAmt, remark, status, income_flag as incomeFlag, phase_status as phaseStatus  
          from cw_project_income 
         where project_id = #{projectId} and status='0' ORDER BY termMonthCompare DESC
    </select>
    
    <select id="selectExportCwProjectFeeList"  resultType="org.ruoyi.core.cwproject.domain.export.ExcelCwProjectFee">
         select f.cust_remark as custRemark,f.id as id, f.project_id as projectId, f.project_income_id as projectIncomeId, f.cust_name as custName, c.cust_name as feeCustName,
         f.calculate_type as calculateType, f.fee_amt as feeAmt, f.fee_amt2 as feeAmt2, f.status, f.fee_flag as feeFlag, CONCAT('方案', c.scheme_flag) AS schemeFlag,
         CASE WHEN f.calculate_type='0' THEN should_pay_fee_amt
         WHEN f.calculate_type='1' THEN '-' ELSE '' END AS shouldPayFeeAmt,
         actually_pay_fee_amt AS actuallyPayFeeAmt, f.cust_id AS custId
         from cw_project_fee f
         LEFT JOIN cw_project_cust c ON f.cust_id=c.id
         where f.project_id = #{projectId} and f.project_income_id = #{projectIncomeId} and f.status='0'
    </select>
    
    
    <select id="selectExportCwProjectPayList" resultType="org.ruoyi.core.cwproject.domain.export.ExcelCwProjectPay">
         select id, project_id as projectId, project_income_id as projectIncomeId, project_fee_id as projectFeeId, pay_date as payDate, 
         pay_amt as payAmt, difference_amt as differenceAmt, status, pay_flag as payFlag 
         from cw_project_pay 
         where project_id = #{projectId} and project_income_id = #{projectIncomeId} and project_fee_id = #{projectFeeId} and status='0'
    </select>
    
    
    
    
    <select id="selectCwProjectOverListDetileByProjectIdV2" resultType="org.ruoyi.core.cwproject.domain.view.CwProjectDetailView">
        select cpf.cust_id as feeCustId,cpf.should_pay_fee_amt as shouldPayFeeAmt,cpf.actually_pay_fee_amt as actuallyPayFeeAmt,cpi.collection_time as collectionTime,cpi.income_rejection_reason as incomeRejectionReason,cpi.fee_rejection_reason as feeRejectionReason,cpi.project_id as projectId, cpi.id as projectIncomeId, cpf.id as projectFeeId
<!--        ,cpp.id as projectPayId-->
        ,cpc.id projectCustId,
        case when cpi.term='0' then cpi.term_month when cpi.term='1' then date_format(cpi.term_begin,'%Y-%m') else '' end as termMonthCompare,
          case when cpi.term='0' then CONCAT(substring(cpi.term_month,1,4),'年',substring(cpi.term_month,6,2),'月') when cpi.term='1' then CONCAT(date_format(cpi.term_begin,'%Y.%m.%d'),'-', date_format(cpi.term_end,'%Y.%m.%d')) 
          else '' end as termMonth,
          
          cpi.income_amt as incomeAmt, cpi.gross_profit_amt as grossProfitAmt, cpi.gross_profit_amt2 as grossProfitAmt2, 
<!--          cpi.fee_amt as feeAmtSum, cpi.unfee_amt as unfeeAmtSum, -->
          cpi.remark,
<!--            todo 原来-->
<!--          case when cpi.phase_status='0' then '待录入收入'-->
<!--          when cpi.phase_status='1' then '待确认收入' -->
<!--          when cpi.phase_status='2'  and (cpi.rejection_flag='' or cpi.rejection_flag=null) then '待录入返费'-->
<!--          when cpi.phase_status='2'  and cpi.rejection_flag='1' then '收入金额驳回'-->
<!--          when cpi.phase_status='3' then '待确认返费'-->
<!--          when cpi.phase_status='4' and (cpi.rejection_flag='' or cpi.rejection_flag=null) then '待出纳打款'-->
<!--          when cpi.phase_status='4' and cpi.rejection_flag='2' then '返费金额驳回'-->
<!--          when cpi.phase_status='5' then '部分打款已完成'-->
<!--          when cpi.phase_status='6' then '已完成'  else '' end as phaseStatus -->
          case when cpi.phase_status='0' then '待录入'
          when cpi.phase_status='1' then '待确认'
          when cpi.phase_status='2'  and cpi.rejection_flag='1' then '业务驳回'
          when cpi.phase_status='2' and (cpi.rejection_flag='' or cpi.rejection_flag=null) then '已完成'
<!--          when cpi.phase_status='3' then '部分打款'-->
<!--          when cpi.phase_status='4' then '已完成'  -->
          else '' end as phaseStatus,
          case when cpf.cust_id=-999 then '暂不确定公司'
          when cpf.cust_id!=-999 then cpc.cust_name else '' end as feeCustName,
          cpf.cust_name as custName,
<!--           cpc.cust_name as feeCustName, -->
           cpf.fee_amt as feeAmt, cpf.fee_amt2 as feeAmt2,
           cpf.reveal_fee_company_id AS revealFeeCompanyId
<!--          , DATE_FORMAT(cpp.pay_date,'%Y.%m.%d') as payDate, cpp.pay_amt as payAmt, cpp.difference_amt as differenceAmt,-->
<!--           case when cpp.pay_flag='0' then '未打款' -->
<!--          when cpp.pay_flag='1' then '已打款' -->
<!--          when cpp.pay_flag='2' then '已确认' -->
<!--          else '' end as payFlag-->
          from cw_project_income cpi 
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
<!--          LEFT JOIN cw_project_pay cpp ON (cpp.project_fee_id=cpf.id AND cpp.status='0')-->
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
         where cpi.project_id = #{projectId} and cpi.status='0'  order by cpi.project_id,cpi.id,cpf.id
<!--         ,cpp.id-->
    </select>

<!--    <select id="selectExportLawCwProjectIncomeList" resultType="org.ruoyi.core.cwproject.domain.export.ExcelLawCwProjectIncome">-->
<!--        select phase_id AS phaseId,service_provider_flag AS serviceProviderFlag,service_provider AS serviceProvider,-->
<!--        service_provider_second AS serviceProviderSecond,service_provider_second_income AS serviceProviderSecondIncome,-->
<!--        true_come_amt AS trueComeAmt,service_fee AS serviceFee,principal,id, project_id as projectId, term, term_month as termMonth, term_begin as termBegin, term_end as termEnd,-->
<!--          income_amt as incomeAmt, gross_profit_amt as grossProfitAmt, gross_profit_amt2 as grossProfitAmt2,-->
<!--          case when term='0' then term_month when term='1' then date_format(term_begin,'%Y-%m') else '' end as termMonthCompare,-->
<!--          fee_amt as feeAmt, unfee_amt as unfeeAmt, remark, status, income_flag as incomeFlag, phase_status as phaseStatus,phase_flag AS phaseFlag-->
<!--          from cw_project_income-->
<!--         where project_id = #{projectId,jdbcType=BIGINT} and status='0'-->
<!--    </select>-->

    <select id="selectExportLawCwProjectInfo" resultType="org.ruoyi.core.cwproject.domain.export.ExcelLawCwProject">
        select id, project_name as projectName, cust_name as custName, income_cust_name as incomeCustName, project_flag as projectFlag
         from cw_project
         where id = #{projectId,jdbcType=BIGINT} and status='0'
    </select>

    <select id="selectExportCwProjectMemberInfoByProjectId" resultType="org.ruoyi.core.cwproject.domain.export.ExcelCwProjectMember">
        SELECT cpu.id,cpu.project_id,
            CASE WHEN cpu.user_flag='0' THEN '会计'
            WHEN cpu.user_flag='1' THEN '出纳'
            WHEN cpu.user_flag='2' THEN '业务'
            WHEN cpu.user_flag='3' THEN '查看权限'
            WHEN cpu.user_flag='4' THEN '导出权限' ELSE '' END AS role,
            GROUP_CONCAT(su.nick_name) AS nickName
        FROM cw_project_user cpu LEFT JOIN sys_user su ON cpu.user_id=su.user_id WHERE cpu.project_id=#{id,jdbcType=BIGINT}
        GROUP BY cpu.user_flag
    </select>

    <select id="selectCwProjectOverListDetileByIncomeIdList" resultType="org.ruoyi.core.cwproject.domain.view.CwProjectDetailView">
        SELECT * FROM
		(
        select
        cpf.cust_id as feeCustId,
        cpi.id as projectIncomeId,
        cpc.id projectCustId,
        case
            when cpi.term='0' then cpi.term_month
            when cpi.term='1' then date_format(cpi.term_begin,
            '%Y-%m')
            else ''
        end as termMonthCompare,
          case
            when cpi.term='0' then CONCAT(substring(cpi.term_month,
            1,
            4),
            '年',
            substring(cpi.term_month,
            6,
            2),
            '月')
            when cpi.term='1' then CONCAT(date_format(cpi.term_begin,
            '%Y.%m.%d'),
            '-',
            date_format(cpi.term_end,
            '%Y.%m.%d'))
            else ''
        end as termMonth,
        cpi.income_amt as incomeAmt,
          case
            when cpf.cust_id=-999 then '暂不确定公司'
            when cpf.cust_id!=-999 then cpc.cust_name
            else ''
        end as feeCustName,
          cpf.cust_name as custName,
           cpf.reveal_fee_company_id AS revealFeeCompanyId
          from cw_project_income cpi
          LEFT JOIN cw_project_fee cpf ON (cpf.project_income_id=cpi.id AND  cpf.status='0')
          LEFT JOIN cw_project_cust cpc ON cpf.cust_id=cpc.id
         where cpi.id in
         <foreach collection="incomeIdList" item="incomeId" open="(" separator="," close=")">
         #{incomeId,jdbcType=BIGINT}
        </foreach>
          and cpi.status='0'  order by cpi.id
          ) a GROUP BY projectIncomeId ORDER BY termMonthCompare DESC
    </select>

<!--    <select id="selectExportLawCwProjectFeeList" resultType="org.ruoyi.core.cwproject.domain.export.ExcelLawCwProjectFee">-->
<!--        select f.id as id, f.project_id as projectId, f.project_income_id as projectIncomeId, f.cust_name as custName, c.cust_name as feeCustName,-->
<!--         f.calculate_type as calculateType, f.fee_amt as feeAmt, f.fee_amt2 as feeAmt2, f.status, f.fee_flag as feeFlag,f.fee_round AS feeRound,f.suspend_flag AS suspendFlag-->
<!--         from cw_project_fee f-->
<!--         LEFT JOIN cw_project_cust c ON f.cust_id=c.id-->
<!--         where f.project_id = #{projectId} and f.project_income_id = #{projectIncomeId} and f.status='0'-->
<!--    </select>-->
    

</mapper>