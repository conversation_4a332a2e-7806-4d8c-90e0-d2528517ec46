<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cwproject.mapper.TopNotifyMapper">

    <resultMap type="org.ruoyi.core.cwproject.domain.TopNotify" id="TopNotifyResult">
        <result property="id" column="id"/>
        <result property="notifyModule" column="notify_module"/>
        <result property="notifyType" column="notify_type"/>
        <result property="notifyMsg" column="notify_msg"/>
        <result property="url" column="url"/>
        <result property="projectId" column="project_id"/>
        <result property="incomeId" column="income_id"/>
        <result property="phaseStatus" column="phase_status"/>
        <result property="disposeUser" column="dispose_user"/>
        <result property="viewFlag" column="view_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_Time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_Time"/>
        <result property="buttonType" column="button_type"/>
        <result property="cdlbStatus" column="cdlb_status"/>
        <result property="applyId" column="apply_id"/>
        <result property="oaNotifyType" column="oa_notify_type"/>
        <result property="oaNotifyStep" column="oa_notify_step"/>
        <result property="oaApplyId" column="oa_apply_id"/>
        <result property="yurrId" column="yurr_id"/>
        <result property="processId" column="process_id"/>
        <result property="businessId" column="business_id"/>
    </resultMap>

    <sql id="selectTopNotifyVo">
        select id,cdlb_status, apply_id,notify_module,button_type, notify_type, notify_msg, url, project_id, income_id, phase_status, dispose_user, view_flag, status, create_by, create_Time, update_by, update_Time, button_type, cdlb_status, apply_id, oa_notify_type, oa_notify_step, oa_apply_id, process_id ,business_id ,yurr_id from top_notify
    </sql>

    <select id="selectTopNotifyList" parameterType="org.ruoyi.core.cwproject.domain.TopNotify" resultType="org.ruoyi.core.cwproject.domain.TopNotify">
        select ts.* from (
        SELECT
        id,
        cdlb_status cdlbStatus,
        apply_id applyId,
        notify_module notifyModule,
        button_type buttonType,
        notify_type notifyType,
        notify_msg notifyMsg,
        url,
        project_id projectId,
        income_id incomeId,
        phase_status phaseStatus,
        dispose_user disposeUser,
        view_flag viewFlag,
        STATUS,
        create_by createBy,
        create_Time createTime,
        '' AS remindText,
        '' AS remindId,
        update_by updateBy,
        update_Time updateTime,
        '' AS licenseId,
        '' AS correlationId,
        oa_notify_type oaNotifyType,
        oa_notify_step oaNotifyStep,
        oa_apply_id oaApplyId,
        yurr_id yurrId,
        process_id processId
        FROM
        top_notify
        <where>
            <if test="topNotify.notifyModule != null and topNotify.notifyModule != ''"> and notify_module = #{topNotify.notifyModule}</if>
            <if test="topNotify.notifyType != null and topNotify.notifyType != ''"> and notify_type = #{topNotify.notifyType}</if>
            <if test="topNotify.notifyMsg != null and topNotify.notifyMsg != ''"> and notify_msg = #{topNotify.notifyMsg}</if>
            <if test="topNotify.url != null and topNotify.url != ''"> and url = #{topNotify.url}</if>
            <if test="topNotify.projectId != null "> and project_id = #{topNotify.projectId}</if>
            <if test="topNotify.incomeId != null "> and income_id = #{topNotify.incomeId}</if>
            <if test="topNotify.phaseStatus != null and topNotify.phaseStatus != ''"> and phase_status = #{topNotify.phaseStatus}</if>
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and view_flag = #{topNotify.viewFlag}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and status = #{topNotify.status}</if>
            <if test="topNotify.createTime != null "> and create_Time = #{topNotify.createTime}</if>
            <if test="topNotify.updateTime != null "> and update_Time = #{topNotify.updateTime}</if>
            <if test="topNotify.cdlbStatus != null "> and cdlb_status = #{topNotify.cdlbStatus}</if>
            <if test="topNotify.applyId != null "> and apply_id = #{topNotify.applyId}</if>
            <if test="topNotify.oaNotifyType != null "> and oa_notify_type = #{topNotify.oaNotifyType}</if>
            <if test="topNotify.oaNotifyStep != null "> and oa_notify_step = #{topNotify.oaNotifyStep}</if>
            <if test="topNotify.oaApplyId != null "> and oa_apply_id = #{topNotify.oaApplyId}</if>
            <if test="topNotify.oaApplyId != null "> and oa_apply_id = #{topNotify.oaApplyId}</if>
            <if test="topNotify.processId != null "> and process_id = #{topNotify.processId}</if>
        </where>

        union all
        select
        id,
        '' as cdlbStatus,
        '' as applyId,
        notify_module as notifyModule,
        '' as buttonType,
        notify_type as notifyType,
        notify_msg as notifyMsg,
        '' as url,
        - 99 as projectId,
        - 99 as incomeId,
        '' as phaseStatus,
        dispose_user as disposeUser,
        view_flag as viewFlag,
        STATUS,
        create_by as createBy,
        create_Time as createTime,
        '' as remindText,
        '' as remindId,
        update_by as updateBy,
        update_time as updateTime,
        license_id as licenseId,
        '' AS correlationId,
        '' AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as yurrId,
        '' AS processId
        from zz_common_notify
        <where>
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.notifyType != null and topNotify.notifyType != ''"> and notify_type = #{topNotify.notifyType}</if>
            <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and view_flag = #{topNotify.viewFlag}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and status = #{topNotify.status}</if>
        </where>
        union all
          select
          id,
          '1'  AS cdlbStatus,
          '' as applyId,
          notify_module as notifyModule,
          '' AS buttonType,
          notify_type AS notifyType,
          notify_msg  AS notifyMsg,
          '' as url,
              '' as projectId,
          '' as incomeId,
          '' as phaseStatus,
           dispose_user AS disposeUser,
          view_flag AS viewFlag,
          kn.status as STATUS,
          user.nick_name AS createBy,
          kn.create_time AS createTime,
          remind_text  AS remindText,
          '' AS remindId,
          kn.update_by as updateBy,
          kn.update_time as updateTime,
          '' AS licenseId,
          correlation_id AS correlationId,
          '' AS oaNotifyType,
          '' AS oaNotifyStep,
          '' AS oaApplyId,
        '' as yurrId,
          '' AS processId
          from kq_notify kn
          left join sys_user user on user.user_name = kn.create_by
          <where>
              <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
              <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and view_flag = #{topNotify.viewFlag}</if>
              <if test="topNotify.status != null and topNotify.status != ''"> and kn.status = #{topNotify.status}</if>
          </where>
        union all
        select
        id,
        '1'  AS cdlbStatus,
        '' as applyId,
        notify_module as notifyModule,
        '' AS buttonType,
        notify_type AS notifyType,
        notify_msg AS notifyMsg,
        CONCAT(url, id) AS url,  -- 拼接 url 和 id
        '' as projectId,
        '' as incomeId,
        '' as phaseStatus,
        dispose_user AS disposeUser,
        view_flag AS viewFlag,
        bn.status as STATUS,
        user.nick_name AS createBy,
        bn.create_time AS createTime,
        remind_text AS remindText,
        '' AS remindId,
        bn.update_by as updateBy,
        bn.update_time as updateTime,
        '' AS licenseId,
        correlation_id AS correlationId,
        'bl' AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as yurrId,
        '' as processId
        from bl_notify bn
        left join sys_user user on bn.create_by = user.user_name
        <where>
            and view_flag = '1'
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and bn.status = #{topNotify.status}</if>
        </where>
        UNION ALL
        select
        id,
        '1'  AS cdlbStatus,
        '' as applyId,
        notify_module as notifyModule,
        '' AS buttonType,
        notify_type AS notifyType,
        notify_msg AS notifyMsg,
        CONCAT(url, id) AS url,  -- 拼接 url 和 id
        '' as projectId,
        '' as incomeId,
        '' as phaseStatus,
        dispose_user AS disposeUser,
        view_flag AS viewFlag,
        kn.status as STATUS,
        user.nick_name AS createBy,
        kn.create_time AS createTime,
        remind_text AS remindText,
        '' AS remindId,
        kn.update_by as updateBy,
        kn.update_time as updateTime,
         '' AS licenseId,
         correlation_id AS correlationId,
        CASE
        WHEN kn.hy_notify_type = 2 THEN '7'
        WHEN kn.hy_notify_type = 4 THEN '7'
        WHEN kn.hy_notify_type = 1 THEN '8'
        WHEN kn.hy_notify_type = 3 THEN '7'
        ELSE ''
        END AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as yurrId,
        '' as processId
        from hy_meeting_notify kn
        left join sys_user user on user.user_name = kn.create_by
        <where>
            and  NOW() >= kn.create_time
            and view_flag = '1'
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and view_flag = #{topNotify.viewFlag}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and kn.status = #{topNotify.status}</if>
        </where>
        UNION ALL
        select
        -2 AS id,
        '-2' AS cdlbStatus,
        '' AS applyId,
        def.name AS notifyModule,
        '-2' AS buttonType,
        '1' AS notifyType,
        flow.remind_title AS notifyMsg,
        '' AS url,
        '' AS projectId,
        '' AS incomeId,
        '-2' AS phaseStatus,
        '' AS disposeUser,
        '0' AS viewFlag,
        '0' AS status,
        us.nick_name AS createBy,
        rem.create_time AS createTime,
        flow.remind_text AS remindText,
        rem.id AS remindId,
        per.remind_person updateBy,
        per.notarize_time updateTime,
        '' as licenseId,
        '' AS correlationId,
        '' AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as yurrId,
        '' AS processId
        from noa_need_remind rem
        left join noa_workflow_remind flow on flow.id = rem.remind_id
        left join oa_process_template tem on tem.flow_id = rem.flow_id AND tem.flow_full_id = rem.business_id
        left join proc_form_def def on def.id = tem.form_id
        left join sys_user us on us.user_name = rem.create_by
        left join noa_remind_person per on per.remind_id = rem.id
        where per.remind_person = #{userName} and per.state = '1'
        ) ts
        ORDER BY ts.createTime desc
    </select>

    <select id="selectTopNotifyList1" resultMap="TopNotifyResult">
        <include refid="selectTopNotifyVo"/>
        <where>
            <if test="notifyModule != null and notifyModule != ''"> and notify_module = #{notifyModule}</if>
            <if test="notifyType != null and notifyType != ''"> and notify_type = #{notifyType}</if>
            <if test="notifyMsg != null and notifyMsg != ''"> and notify_msg = #{notifyMsg}</if>
            <if test="url != null and url != ''"> and url = #{url}</if>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="incomeId != null "> and income_id = #{incomeId}</if>
            <if test="phaseStatus != null and phaseStatus != ''"> and phase_status = #{phaseStatus}</if>
            <if test="disposeUser != null "> and dispose_user = #{disposeUser}</if>
            <if test="viewFlag != null and viewFlag != ''"> and view_flag = #{viewFlag}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
            <if test="cdlbStatus != null "> and cdlb_status = #{cdlbStatus}</if>
            <if test="applyId != null "> and apply_id = #{applyId}</if>
            <if test="oaNotifyType != null "> and oa_notify_type = #{oaNotifyType}</if>
            <if test="oaNotifyStep != null "> and oa_notify_step = #{oaNotifyStep}</if>
            <if test="oaApplyId != null "> and oa_apply_id = #{oaApplyId}</if>
            <if test="processId != null "> and process_id = #{processId}</if>
        </where>
    </select>

    <select id="selectTopNotifyById" parameterType="Long" resultMap="TopNotifyResult">
        <include refid="selectTopNotifyVo"/>
        where id = #{id}
    </select>

    <select id="selectTopNotifyListAndRemind" resultType="org.ruoyi.core.cwproject.domain.TopNotify">
        select ts.* from (
        select
        stn.id,
        stn.cdlb_status cdlbStatus,
        stn.apply_id applyId,
        stn.notify_module notifyModule,
        stn.button_type buttonType,
        stn.notify_type notifyType,
        stn.notify_msg notifyMsg,
        stn.url,
        stn.project_id projectId,
        stn.income_id incomeId,
        stn.phase_status phaseStatus,
        stn.dispose_user disposeUser,
        stn.view_flag viewFlag,
        stn.STATUS,
        stn.create_by createBy,
        stn.create_Time createTime,
        '' AS remindText,
        '' AS remindId,
        '' AS licenseId,
        '' AS correlationId,
        stn.oa_notify_type oaNotifyType,
        stn.oa_notify_step oaNotifyStep,
        stn.oa_apply_id oaApplyId,
        stn.business_id businessId,
        stn.yurr_id yurrId,
        stn.process_id processId,
        pfd.theme processName
        FROM
        top_notify stn left join proc_form_data pfd on pfd.business_id = stn.process_id
        <where>
            <if test="topNotify.notifyModule != null and topNotify.notifyModule != ''"> and stn.notify_module = #{topNotify.notifyModule}</if>
            <if test="topNotify.notifyType != null and topNotify.notifyType != ''"> and stn.notify_type = #{topNotify.notifyType}</if>
            <if test="topNotify.notifyMsg != null and topNotify.notifyMsg != ''"> and stn.notify_msg = #{topNotify.notifyMsg}</if>
            <if test="topNotify.url != null and topNotify.url != ''"> and stn.url = #{topNotify.url}</if>
            <if test="topNotify.projectId != null "> and stn.project_id = #{topNotify.projectId}</if>
            <if test="topNotify.incomeId != null "> and stn.income_id = #{topNotify.incomeId}</if>
            <if test="topNotify.phaseStatus != null and topNotify.phaseStatus != ''"> and stn.phase_status = #{topNotify.phaseStatus}</if>
            <if test="topNotify.disposeUser != null "> and stn.dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and stn.view_flag = #{topNotify.viewFlag}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and stn.status = #{topNotify.status}</if>
            <if test="topNotify.createTime != null "> and stn.create_Time = #{topNotify.createTime}</if>
            <if test="topNotify.updateTime != null "> and stn.update_Time = #{topNotify.updateTime}</if>
            <if test="topNotify.cdlbStatus != null "> and stn.cdlb_status = #{topNotify.cdlbStatus}</if>
            <if test="topNotify.applyId != null "> and stn.apply_id = #{topNotify.applyId}</if>
            <if test="topNotify.oaNotifyType != null "> and stn.oa_notify_type = #{topNotify.oaNotifyType}</if>
            <if test="topNotify.oaNotifyStep != null "> and stn.oa_notify_step = #{topNotify.oaNotifyStep}</if>
            <if test="topNotify.oaApplyId != null "> and stn.oa_apply_id = #{topNotify.oaApplyId}</if>
            <if test="topNotify.processId != null "> and stn.process_id = #{topNotify.processId}</if>
            <if test="topNotify.yurrId != null and topNotify.yurrId != ''"> and stn.yurr_id = #{topNotify.yurrId}</if>

        </where>
        union all
        select  id, '' as cdlbStatus, '' as applyId,  notify_module as notifyModule, '' as buttonType,  notify_type as notifyType,
            notify_msg as notifyMsg,  url, -99 as projectId, -99 as incomeId, '' as phaseStatus,  dispose_user as disposeUser,
        view_flag as viewFlag,  status,  create_by AS createBy,  create_Time AS createTime,  '' AS remindText,  '' AS remindId, license_id AS licenseId, '' as correlationId, '' AS oaNotifyType, '' AS oaNotifyStep, '' AS oaApplyId, '' as yurrId ,'' as processId
        ,'' as   businessId,'' as processName
        from zz_common_notify
        <where>
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and view_flag = #{topNotify.viewFlag}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and status = #{topNotify.status}</if>
        </where>
        union all
        select
        id, '1'  AS cdlbStatus, '' as applyId, notify_module as notifyModule,'' AS buttonType, notify_type AS notifyType,
        notify_msg  AS notifyMsg, '' as url,'' as projectId, '' as incomeId, '' as phaseStatus, dispose_user AS disposeUser,
        view_flag AS viewFlag, kn.status as STATUS, user.nick_name AS createBy, kn.create_time AS createTime, remind_text  AS remindText, '' AS remindId,
        '' AS licenseId,correlation_id AS correlationId,
        '' AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as   businessId,
        '' as yurrId,
        '' as processId,
        '' as processName
        from kq_notify kn
        left join sys_user user on user.user_name = kn.create_by
        <where>
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and view_flag = #{topNotify.viewFlag}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and kn.status = #{topNotify.status}</if>
        </where>
        union all
        select
        id,
        '1'  AS cdlbStatus,
        '' as applyId,
        notify_module as notifyModule,
        '' AS buttonType,
        notify_type AS notifyType,
        notify_msg AS notifyMsg,
        '' AS url,  -- 拼接 url 和 id
        '' as projectId,
        '' as incomeId,
        '' as phaseStatus,
        dispose_user AS disposeUser,
        view_flag AS viewFlag,
        bn.status as STATUS,
        user.nick_name AS createBy,
        bn.create_time AS createTime,
        remind_text AS remindText,
        '' AS remindId,
        '' AS licenseId,
        correlation_id AS correlationId,
        'bl' AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as yurrId,
        '' as processId,
        '' as   businessId,
        '' as processName
        from bl_notify bn
        left join sys_user user on user.user_name = bn.create_by
        <where>
            and view_flag = '0'
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and bn.status = #{topNotify.status}</if>
        </where>
        union all
        select
        id,
        '1'  AS cdlbStatus,
        '' as applyId,
        notify_module as notifyModule,
        '' AS buttonType,
        notify_type AS notifyType,
        notify_msg AS notifyMsg,
        CONCAT(url, id) AS url,  -- 拼接 url 和 id
        '' as projectId,
        '' as incomeId,
        '' as phaseStatus,
        dispose_user AS disposeUser,
        view_flag AS viewFlag,
        kn.status as STATUS,
        user.nick_name AS createBy,
        kn.create_time AS createTime,
        remind_text AS remindText,
        '' AS remindId,
        '' AS licenseId,
        correlation_id AS correlationId,
        CASE
        WHEN kn.hy_notify_type = 2 THEN '7'
        WHEN kn.hy_notify_type = 4 THEN '7'
        WHEN kn.hy_notify_type = 1 THEN '8'
        WHEN kn.hy_notify_type = 3 THEN '7'
        ELSE ''
        END AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as yurrId,
        '' as processId,
        '' as   businessId,
        '' as processName
        from hy_meeting_notify kn
        left join sys_user user on user.user_name = kn.create_by
        <where>
            and  NOW() >= kn.create_time
            <if test="topNotify.disposeUser != null "> and dispose_user = #{topNotify.disposeUser}</if>
            <if test="topNotify.viewFlag != null and topNotify.viewFlag != ''"> and view_flag = #{topNotify.viewFlag}</if>
            <if test="topNotify.status != null and topNotify.status != ''"> and kn.status = #{topNotify.status}</if>
        </where>
        UNION ALL
        select
        -2 AS id,
        '-2' AS cdlbStatus,
        '' AS applyId,
        def.name AS notifyModule,
        '-2' AS buttonType,
        '1' AS notifyType,
        flow.remind_title AS notifyMsg,
        '' AS url,
        '' AS projectId,
        '' AS incomeId,
        '-2' AS phaseStatus,
        '' AS disposeUser,
        '0' AS viewFlag,
        '0' AS status,
        us.nick_name AS createBy,
        rem.create_time AS createTime,
        flow.remind_text AS remindText,
        rem.id AS remindId,
        '' AS licenseId,
        '' AS correlationId,
        '' AS oaNotifyType,
        '' AS oaNotifyStep,
        '' AS oaApplyId,
        '' as yurrId,
        '' as processId,
        '' as   businessId,
        '' as processName
        from noa_need_remind rem
        left join noa_workflow_remind flow on flow.id = rem.remind_id
        left join oa_process_template tem on tem.flow_id = rem.flow_id AND tem.flow_full_id = rem.business_id
        left join proc_form_def def on def.id = tem.form_id
        left join sys_user us on us.user_name = rem.create_by
        <if test="date != null and date.size > 0">
            where rem.id in (
            <foreach item="date" collection="date" index="index" separator=",">
                #{date.remindId}
            </foreach>)
        </if>
        <if test="date == null or date.size == 0">
            where rem.id = ''
        </if>
            ) ts
        ORDER BY ts.createTime desc
    </select>

    <insert id="insertTopNotify" parameterType="org.ruoyi.core.cwproject.domain.TopNotify" useGeneratedKeys="true" keyProperty="id">
        insert into top_notify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="notifyModule != null and notifyModule != ''">notify_module,</if>
            <if test="notifyType != null and notifyType != ''">notify_type,</if>
            <if test="notifyMsg != null and notifyMsg != ''">notify_msg,</if>
            <if test="url != null and url != ''">url,</if>
            <if test="projectId != null">project_id,</if>
            <if test="incomeId != null">income_id,</if>
            <if test="phaseStatus != null">phase_status,</if>
            <if test="disposeUser != null">dispose_user,</if>
            <if test="viewFlag != null and viewFlag != ''">view_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_Time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_Time,</if>
            <if test="buttonType != null">button_type,</if>
            <if test="cdlbStatus != null">cdlb_status,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="oaNotifyType != null">oa_notify_type,</if>
            <if test="oaNotifyStep != null">oa_notify_step,</if>
            <if test="oaApplyId != null">oa_apply_id,</if>
            <if test="yurrId != null">yurr_id,</if>
            <if test="businessId != null and businessId != ''">business_id,</if>
            <if test="processId != null">process_id,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="notifyModule != null and notifyModule != ''">#{notifyModule},</if>
            <if test="notifyType != null and notifyType != ''">#{notifyType},</if>
            <if test="notifyMsg != null and notifyMsg != ''">#{notifyMsg},</if>
            <if test="url != null and url != ''">#{url},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="incomeId != null">#{incomeId},</if>
            <if test="phaseStatus != null">#{phaseStatus},</if>
            <if test="disposeUser != null">#{disposeUser},</if>
            <if test="viewFlag != null and viewFlag != ''">#{viewFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="buttonType != null">#{buttonType},</if>
            <if test="cdlbStatus != null">#{cdlbStatus},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="oaNotifyType != null">#{oaNotifyType},</if>
            <if test="oaNotifyStep != null">#{oaNotifyStep},</if>
            <if test="oaApplyId != null">#{oaApplyId},</if>
            <if test="yurrId != null">#{yurrId},</if>
            <if test="businessId != null and businessId != ''">#{businessId},</if>
            <if test="processId != null">#{processId},</if>

        </trim>
    </insert>

    <update id="updateTopNotify" parameterType="org.ruoyi.core.cwproject.domain.TopNotify">
        update top_notify
        <trim prefix="SET" suffixOverrides=",">
            <if test="notifyModule != null and notifyModule != ''">notify_module = #{notifyModule},</if>
            <if test="notifyType != null and notifyType != ''">notify_type = #{notifyType},</if>
            <if test="notifyMsg != null and notifyMsg != ''">notify_msg = #{notifyMsg},</if>
            <if test="url != null and url != ''">url = #{url},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="incomeId != null">income_id = #{incomeId},</if>
            <if test="phaseStatus != null">phase_status = #{phaseStatus},</if>
            <if test="disposeUser != null">dispose_user = #{disposeUser},</if>
            <if test="viewFlag != null and viewFlag != ''">view_flag = #{viewFlag},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_Time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>
            <if test="buttonType != null">button_type = #{buttonType},</if>
            <if test="cdlbStatus != null">cdlb_status = #{cdlbStatus},</if>
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="oaNotifyType != null">oa_notify_type = #{oaNotifyType},</if>
            <if test="oaNotifyStep != null">oa_notify_step = #{oaNotifyStep},</if>
            <if test="oaApplyId != null">oa_apply_id = #{oaApplyId},</if>
            <if test="yurrId != null">yurr_id = #{yurrId},</if>
            <if test="businessId != null and businessId != ''">business_id = #{businessId},</if>
            <if test="processId != null">process_id = #{processId},</if>

        </trim>
        where id = #{id}
    </update>
    <update id="updateTopNotifyIOA" parameterType="org.ruoyi.core.cwproject.domain.TopNotify">
        update top_notify
        <trim prefix="SET" suffixOverrides=",">
            <if test="notifyModule != null and notifyModule != ''">notify_module = #{notifyModule},</if>
            <if test="notifyType != null and notifyType != ''">notify_type = #{notifyType},</if>
            <if test="notifyMsg != null and notifyMsg != ''">notify_msg = #{notifyMsg},</if>


            <if test="incomeId != null">income_id = #{incomeId},</if>

            <if test="disposeUser != null">dispose_user = #{disposeUser},</if>
            <if test="viewFlag != null and viewFlag != ''">view_flag = #{viewFlag},</if>
            <if test="status != null">status = #{status},</if>

            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_Time = #{updateTime},</if>
            <if test="buttonType != null">button_type = #{buttonType},</if>

        </trim>
        where apply_id = #{applyId} and  cdlb_status =#{cdlbStatus}
    </update>

    <delete id="deleteTopNotifyById" parameterType="Long">
        delete from top_notify where id = #{id}
    </delete>

    <delete id="deleteTopNotifyByIds" parameterType="String">
        delete from top_notify where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateByproductId">
        update top_notify set (notify_type = '0',view_flag = '1') where project_id = #{productId} and notify_module = #{notifyModel} and view_flag = '1'
    </update>


    <update id="updateByInmAndProAndStat">
        update top_notify set notify_type = '0',view_flag = '1',update_Time = #{updateDate},update_by = #{updateName}  where project_id = #{projectId} and income_id = #{inComeId} and phase_status = #{phaseStatus}
    </update>


    <update id="updateStatus">
        update top_notify set status = #{status}  where project_id = #{id}
    </update>

    <delete id="deleteTopNotifyByByIncomeIdAndPhaseStatus">
        DELETE FROM top_notify WHERE income_id=#{phaseId,jdbcType=BIGINT} AND phase_status=#{phaseStatus,jdbcType=VARCHAR}
    </delete>

    <update id="updateTopNotifyByProjectIdAndIncomeIdAndPhaseStatus">
        UPDATE top_notify SET status = '1' WHERE project_id=#{projectId,jdbcType=BIGINT} AND income_id=#{incomeId,jdbcType=BIGINT} AND phase_status=#{phaseStatus,jdbcType=VARCHAR};
    </update>

    <update id="updateTopNotifyTypeAndViewFlagByIds">
        UPDATE top_notify SET notify_type=#{notifyType,jdbcType=VARCHAR},view_flag=#{viewFlag,jdbcType=VARCHAR},status=#{status,jdbcType=VARCHAR}
        WHERE id
        IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <delete id="deleteTopNotifyByOaNotifyTypeAndOaApplyId">
        DELETE FROM top_notify WHERE oa_notify_type=#{oaNotifyType,jdbcType=VARCHAR} AND oa_apply_id=#{oaApplyId,jdbcType=BIGINT}
    </delete>

    <select id="selectTopNotifyInfoByProcessId" resultType="org.ruoyi.core.cwproject.domain.TopNotify">
        <include refid="selectTopNotifyVo"/>
        <where>
            <if test="notifyModule != null and notifyModule != ''"> and notify_module = #{notifyModule}</if>
            <if test="notifyType != null and notifyType != ''"> and notify_type = #{notifyType}</if>
            <if test="notifyMsg != null and notifyMsg != ''"> and notify_msg = #{notifyMsg}</if>
            <if test="url != null and url != ''"> and url = #{url}</if>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="incomeId != null "> and income_id = #{incomeId}</if>
            <if test="phaseStatus != null and phaseStatus != ''"> and phase_status = #{phaseStatus}</if>
            <if test="disposeUser != null "> and dispose_user = #{disposeUser}</if>
            <if test="viewFlag != null and viewFlag != ''"> and view_flag = #{viewFlag}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="createTime != null "> and create_Time = #{createTime}</if>
            <if test="updateTime != null "> and update_Time = #{updateTime}</if>
            <if test="cdlbStatus != null "> and cdlb_status = #{cdlbStatus}</if>
            <if test="applyId != null "> and apply_id = #{applyId}</if>
            <if test="oaNotifyType != null "> and oa_notify_type = #{oaNotifyType}</if>
            <if test="oaNotifyStep != null "> and oa_notify_step = #{oaNotifyStep}</if>
            <if test="oaApplyId != null "> and oa_apply_id = #{oaApplyId}</if>
            <if test="processId != null "> and process_id = #{processId}</if>
        </where>
    </select>
    <update id="updateCuiShenByIds">
        UPDATE top_notify SET notify_type=#{notifyType,jdbcType=VARCHAR},view_flag=#{viewFlag,jdbcType=VARCHAR}
        WHERE process_id
        IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

</mapper>
