<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.cdlb.mapper.CdlbInfoDynamicMapper">

    <resultMap type="CdlbInfoDynamic" id="CdlbInfoDynamicResult">
        <result property="id"    column="id"    />
        <result property="infoId"    column="info_id"    />
        <result property="applyFlag"    column="apply_flag"    />
        <result property="dynamicTitle"    column="dynamic_title"    />
        <result property="operId"    column="oper_id"    />
        <result property="operName"    column="oper_name"    />
        <result property="dynamicTimeTitle"    column="dynamic_time_title"    />
        <result property="dynamicTime"    column="dynamic_time"    />
        <result property="dynamicRemarkTitle"    column="dynamic_remark_title"    />
        <result property="remark"    column="remark"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="status"    column="status"    />
        <result property="garageState"    column="garage_state"    />
    </resultMap>

    <sql id="selectCdlbInfoDynamicVo">
        select id, info_id, apply_flag, dynamic_title, oper_id,garage_state, oper_name, dynamic_time_title, dynamic_time, dynamic_remark_title, remark, error_msg, status from cdlb_info_dynamic
    </sql>

    <select id="selectCdlbInfoDynamicList" parameterType="CdlbInfoDynamic" resultMap="CdlbInfoDynamicResult">
        <include refid="selectCdlbInfoDynamicVo"/>
        <where>
            <if test="infoId != null "> and info_id = #{infoId}</if>
            <if test="applyFlag != null  and applyFlag != ''"> and apply_flag = #{applyFlag}</if>
            <if test="dynamicTitle != null  and dynamicTitle != ''"> and dynamic_title = #{dynamicTitle}</if>
            <if test="operId != null "> and oper_id = #{operId}</if>
            <if test="operName != null  and operName != ''"> and oper_name like concat('%', #{operName}, '%')</if>
            <if test="dynamicTimeTitle != null  and dynamicTimeTitle != ''"> and dynamic_time_title = #{dynamicTimeTitle}</if>
            <if test="dynamicTime != null "> and dynamic_time = #{dynamicTime}</if>
            <if test="dynamicRemarkTitle != null  and dynamicRemarkTitle != ''"> and dynamic_remark_title = #{dynamicRemarkTitle}</if>
            <if test="errorMsg != null  and errorMsg != ''"> and error_msg = #{errorMsg}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="garageState != null "> and garage_state = #{garageState}</if>
        </where>
    </select>

    <select id="selectCdlbInfoDynamicById" parameterType="Long" resultMap="CdlbInfoDynamicResult">
        <include refid="selectCdlbInfoDynamicVo"/>
        where id = #{id}
    </select>

    <insert id="insertCdlbInfoDynamic" parameterType="CdlbInfoDynamic" useGeneratedKeys="true" keyProperty="id">
        insert into cdlb_info_dynamic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="infoId != null">info_id,</if>
            <if test="applyFlag != null and applyFlag != ''">apply_flag,</if>
            <if test="dynamicTitle != null">dynamic_title,</if>
            <if test="operId != null">oper_id,</if>
            <if test="operName != null">oper_name,</if>
            <if test="dynamicTimeTitle != null">dynamic_time_title,</if>
            <if test="dynamicTime != null">dynamic_time,</if>
            <if test="dynamicRemarkTitle != null">dynamic_remark_title,</if>
            <if test="remark != null">remark,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="status != null">status,</if>
            <if test="garageState != null">garage_state,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="infoId != null">#{infoId},</if>
            <if test="applyFlag != null and applyFlag != ''">#{applyFlag},</if>
            <if test="dynamicTitle != null">#{dynamicTitle},</if>
            <if test="operId != null">#{operId},</if>
            <if test="operName != null">#{operName},</if>
            <if test="dynamicTimeTitle != null">#{dynamicTimeTitle},</if>
            <if test="dynamicTime != null">#{dynamicTime},</if>
            <if test="dynamicRemarkTitle != null">#{dynamicRemarkTitle},</if>
            <if test="remark != null">#{remark},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="status != null">#{status},</if>
            <if test="garageState != null">#{garageState},</if>
         </trim>
    </insert>

    <update id="updateCdlbInfoDynamic" parameterType="CdlbInfoDynamic">
        update cdlb_info_dynamic
        <trim prefix="SET" suffixOverrides=",">
            <if test="infoId != null">info_id = #{infoId},</if>
            <if test="applyFlag != null and applyFlag != ''">apply_flag = #{applyFlag},</if>
            <if test="dynamicTitle != null">dynamic_title = #{dynamicTitle},</if>
            <if test="operId != null">oper_id = #{operId},</if>
            <if test="operName != null">oper_name = #{operName},</if>
            <if test="dynamicTimeTitle != null">dynamic_time_title = #{dynamicTimeTitle},</if>
            <if test="dynamicTime != null">dynamic_time = #{dynamicTime},</if>
            <if test="dynamicRemarkTitle != null">dynamic_remark_title = #{dynamicRemarkTitle},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="status != null">status = #{status},</if>
            <if test="garageState != null">garage_state = #{garageState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCdlbInfoDynamicById" parameterType="Long">
        delete from cdlb_info_dynamic where id = #{id}
    </delete>

    <delete id="deleteCdlbInfoDynamicByIds" parameterType="String">
        delete from cdlb_info_dynamic where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>