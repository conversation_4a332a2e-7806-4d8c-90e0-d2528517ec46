<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.OaVoucherRulesUserMapper">



    <select id="selectOaVoucherRulesUserListByOaVoucherRulesMainId" resultType="org.ruoyi.core.oasystem.domain.OaVoucherRulesUser">
        SELECT ovru.id AS id, ovru.oa_voucher_rules_main_id AS oaVoucherRulesMainId, ovru.user_id AS userId, ovru.user_flag AS userFlag, ovru.status AS status, ovru.create_by AS createBy, ovru.create_time AS createTime, ovru.update_by AS updateBy, ovru.update_time AS updateTime,su.nick_name AS userNickName FROM oa_voucher_rules_user ovru LEFT JOIN sys_user su ON ovru.user_id=su.user_id WHERE ovru.oa_voucher_rules_main_id=#{oaVoucherRulesMainId,jdbcType=BIGINT} AND ovru.status='0'
    </select>

    <insert id="insertOaVoucherRulesUser" parameterType="OaVoucherRulesUser" useGeneratedKeys="true" keyProperty="id">
        insert into oa_voucher_rules_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oaVoucherRulesMainId != null">oa_voucher_rules_main_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userFlag != null and userFlag != ''">user_flag,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oaVoucherRulesMainId != null">#{oaVoucherRulesMainId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="userFlag != null and userFlag != ''">#{userFlag,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <delete id="deleteOaVoucherRulesUserByOaVoucherRulesMainId">
        DELETE FROM oa_voucher_rules_user WHERE oa_voucher_rules_main_id=#{oaVoucherRulesMainId,jdbcType=BIGINT}
    </delete>

    <select id="selectOaVoucherRulesUserByOaVoucherRulesMainIdAndUserId" resultType="org.ruoyi.core.oasystem.domain.OaVoucherRulesUser">
         SELECT ovru.id AS id, ovru.oa_voucher_rules_main_id AS oaVoucherRulesMainId, ovru.user_id AS userId, ovru.user_flag AS userFlag, ovru.status AS status, ovru.create_by AS createBy, ovru.create_time AS createTime, ovru.update_by AS updateBy, ovru.update_time AS updateTime,su.nick_name AS userNickName FROM oa_voucher_rules_user ovru LEFT JOIN sys_user su ON ovru.user_id=su.user_id WHERE ovru.oa_voucher_rules_main_id=#{oaVoucherRulesMainId,jdbcType=BIGINT} AND ovru.user_id=#{userId,jdbcType=BIGINT} AND ovru.status='0'
    </select>

</mapper>