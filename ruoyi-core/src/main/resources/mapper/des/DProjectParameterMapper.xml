<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.mapper.DProjectParameterMapper">

    <resultMap type="DProjectParameter" id="DProjectParameterResult">
        <result property="id"    column="id"    />
        <result property="platformNo"    column="platform_no"    />
        <result property="custNo"    column="cust_no"    />
        <result property="partnerNo"    column="partner_no"    />
        <result property="fundNo"    column="fund_no"    />
        <result property="itNo"    column="it_no"    />
        <result property="productNo"    column="product_no"    />
        <result property="isMapping"    column="is_mapping"    />
        <result property="isProjectFinish"    column="is_project_finish"    />
        <result property="projectFinishDate"    column="project_finish_date"    />
        <result property="isProjectCompany"    column="is_project_company"    />
        <result property="isProjectTd"    column="is_project_td"    />
        <result property="isProjectPlan"    column="is_project_plan"    />
        <result property="isProjectPlanUpdate"    column="is_project_plan_update"    />
        <result property="isProjectPlanReset"    column="is_project_plan_reset"    />
        <result property="isProjectRepay1"    column="is_project_repay1"    />
        <result property="isProjectRepay4"    column="is_project_repay4"    />
        <result property="isProjectRepay5"    column="is_project_repay5"    />
        <result property="isProjectRepay7"    column="is_project_repay7"    />
        <result property="isProjectTotalRepay7"    column="is_project_total_repay7"    />
        <result property="isProjectTotalRepay"    column="is_project_total_repay"    />
        <result property="isProjectRepay8"    column="is_project_repay8"    />
        <result property="isProjectRepay7Finish"    column="is_project_repay7_finish"    />
        <result property="isProjectRepay8Normal"    column="is_project_repay8_normal"    />
        <result property="isResultFpd10"    column="is_result_fpd10"    />
        <result property="isResultVintage"    column="is_result_vintage"    />
        <result property="isResultBalanceDistribution"    column="is_result_balance_distribution"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="productName"    column="product_name"    />
        <result property="systemNo"    column="system_no"    />
        <result property="projectId"    column="project_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="description"    column="description"    />
    </resultMap>

    <sql id="selectDProjectParameterVo">
        select id, platform_no, cust_no, partner_no, fund_no, it_no, product_no, is_mapping, is_project_finish, project_finish_date, is_project_company, is_project_td, is_project_plan, is_project_plan_update, is_project_plan_reset, is_project_repay1, is_project_repay4, is_project_repay5, is_project_repay7, is_project_total_repay7, is_project_total_repay, is_project_repay8, is_project_repay7_finish, is_project_repay8_normal, is_result_fpd10, is_result_vintage, is_result_balance_distribution, remark, status,check_status ,create_time, update_time, create_by, update_by, product_name, system_no, project_id, description from d_project_parameter
    </sql>

    <select id="selectDProjectParameterList" parameterType="DProjectParameter" resultMap="DProjectParameterResult">
        select dpp.id, platform_no, cust_no, partner_no, fund_no, it_no, product_no, is_mapping, is_project_finish, project_finish_date, is_project_company,
               is_project_td, is_project_plan, is_project_plan_update, is_project_plan_reset, is_project_repay1, is_project_repay4, is_project_repay5,
               is_project_repay7, is_project_total_repay7, is_project_total_repay, is_project_repay8, is_project_repay7_finish, is_project_repay8_normal,
               is_result_fpd10, is_result_vintage, is_result_balance_distribution, sdd.remark, dpp.status,dpp.check_status, dpp.create_time, dpp.update_time,  sdd.create_by,  sdd.update_by,
               product_name, system_no, project_id, project_name,description
        from d_project_parameter dpp
        left join oa_project_deploy opd on opd.id = dpp.project_id
        left join sys_dict_data sdd on dpp.product_no = sdd.dict_Value and sdd.dict_type = 'product_no'
        <where>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="custNo != null  and custNo != ''"> and cust_no = #{custNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
            <if test="itNo != null  and itNo != ''"> and it_no = #{itNo}</if>
            <if test="productNo != null  and productNo != ''"> and product_no like concat('%',#{productNo},'%') </if>
            <if test="isMapping != null  and isMapping != ''"> and is_mapping = #{isMapping}</if>
            <if test="isProjectFinish != null  and isProjectFinish != ''"> and is_project_finish = #{isProjectFinish}</if>
            <if test="projectFinishDate != null "> and project_finish_date = #{projectFinishDate}</if>
            <if test="isProjectCompany != null  and isProjectCompany != ''"> and is_project_company = #{isProjectCompany}</if>
            <if test="isProjectTd != null  and isProjectTd != ''"> and is_project_td = #{isProjectTd}</if>
            <if test="isProjectPlan != null  and isProjectPlan != ''"> and is_project_plan = #{isProjectPlan}</if>
            <if test="isProjectPlanUpdate != null  and isProjectPlanUpdate != ''"> and is_project_plan_update = #{isProjectPlanUpdate}</if>
            <if test="isProjectPlanReset != null  and isProjectPlanReset != ''"> and is_project_plan_reset = #{isProjectPlanReset}</if>
            <if test="isProjectRepay1 != null  and isProjectRepay1 != ''"> and is_project_repay1 = #{isProjectRepay1}</if>
            <if test="isProjectRepay4 != null  and isProjectRepay4 != ''"> and is_project_repay4 = #{isProjectRepay4}</if>
            <if test="isProjectRepay5 != null  and isProjectRepay5 != ''"> and is_project_repay5 = #{isProjectRepay5}</if>
            <if test="isProjectRepay7 != null  and isProjectRepay7 != ''"> and is_project_repay7 = #{isProjectRepay7}</if>
            <if test="isProjectTotalRepay7 != null  and isProjectTotalRepay7 != ''"> and is_project_total_repay7 = #{isProjectTotalRepay7}</if>
            <if test="isProjectTotalRepay != null  and isProjectTotalRepay != ''"> and is_project_total_repay = #{isProjectTotalRepay}</if>
            <if test="isProjectRepay8 != null  and isProjectRepay8 != ''"> and is_project_repay8 = #{isProjectRepay8}</if>
            <if test="isProjectRepay7Finish != null  and isProjectRepay7Finish != ''"> and is_project_repay7_finish = #{isProjectRepay7Finish}</if>
            <if test="isProjectRepay8Normal != null  and isProjectRepay8Normal != ''"> and is_project_repay8_normal = #{isProjectRepay8Normal}</if>
            <if test="isResultFpd10 != null  and isResultFpd10 != ''"> and is_result_fpd10 = #{isResultFpd10}</if>
            <if test="isResultVintage != null  and isResultVintage != ''"> and is_result_vintage = #{isResultVintage}</if>
            <if test="isResultBalanceDistribution != null  and isResultBalanceDistribution != ''"> and is_result_balance_distribution = #{isResultBalanceDistribution}</if>
            <if test="status != null  and status != ''"> and dpp.status = #{status}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="productName != null and productName != ''"> and (product_name like concat('%', #{productName}, '%') or sdd.dict_label like concat('%',#{productName},'%'))</if>
            <if test="systemNo != null and systemNo != '' "> and system_no = #{systemNo}</if>
            <if test="projectId != null  and projectId != ''"> and project_id = #{projectId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
        order by dpp.id DESC
    </select>

    <select id="selectDProjectParameterListPinyin" parameterType="DProjectParameter" resultMap="DProjectParameterResult">
        select dpp.id, platform_no, cust_no, partner_no, fund_no, it_no, product_no, is_mapping, is_project_finish, project_finish_date, is_project_company,
        is_project_td, is_project_plan, is_project_plan_update, is_project_plan_reset, is_project_repay1, is_project_repay4, is_project_repay5,
        is_project_repay7, is_project_total_repay7, is_project_total_repay, is_project_repay8, is_project_repay7_finish, is_project_repay8_normal,
        is_result_fpd10, is_result_vintage, is_result_balance_distribution, sdd.remark, sdd.status,dpp.check_status, dpp.create_time, dpp.update_time,  sdd.create_by,  sdd.update_by,
        product_name, system_no, project_id, project_name,description
        from d_project_parameter dpp
        left join oa_project_deploy opd on opd.id = dpp.project_id
        left join sys_dict_data sdd on dpp.product_no = sdd.dict_Value and sdd.dict_type = 'product_no'
        <where>
            <if test="platformNo != null  and platformNo != ''"> and platform_no = #{platformNo}</if>
            <if test="custNo != null  and custNo != ''"> and cust_no = #{custNo}</if>
            <if test="partnerNo != null  and partnerNo != ''"> and partner_no = #{partnerNo}</if>
            <if test="fundNo != null  and fundNo != ''"> and fund_no = #{fundNo}</if>
            <if test="itNo != null  and itNo != ''"> and it_no = #{itNo}</if>
            <if test="productNo != null  and productNo != ''"> and product_no like concat('%',#{productNo},'%') </if>
            <if test="isMapping != null  and isMapping != ''"> and is_mapping = #{isMapping}</if>
            <if test="isProjectFinish != null  and isProjectFinish != ''"> and is_project_finish = #{isProjectFinish}</if>
            <if test="projectFinishDate != null "> and project_finish_date = #{projectFinishDate}</if>
            <if test="isProjectCompany != null  and isProjectCompany != ''"> and is_project_company = #{isProjectCompany}</if>
            <if test="isProjectTd != null  and isProjectTd != ''"> and is_project_td = #{isProjectTd}</if>
            <if test="isProjectPlan != null  and isProjectPlan != ''"> and is_project_plan = #{isProjectPlan}</if>
            <if test="isProjectPlanUpdate != null  and isProjectPlanUpdate != ''"> and is_project_plan_update = #{isProjectPlanUpdate}</if>
            <if test="isProjectPlanReset != null  and isProjectPlanReset != ''"> and is_project_plan_reset = #{isProjectPlanReset}</if>
            <if test="isProjectRepay1 != null  and isProjectRepay1 != ''"> and is_project_repay1 = #{isProjectRepay1}</if>
            <if test="isProjectRepay4 != null  and isProjectRepay4 != ''"> and is_project_repay4 = #{isProjectRepay4}</if>
            <if test="isProjectRepay5 != null  and isProjectRepay5 != ''"> and is_project_repay5 = #{isProjectRepay5}</if>
            <if test="isProjectRepay7 != null  and isProjectRepay7 != ''"> and is_project_repay7 = #{isProjectRepay7}</if>
            <if test="isProjectTotalRepay7 != null  and isProjectTotalRepay7 != ''"> and is_project_total_repay7 = #{isProjectTotalRepay7}</if>
            <if test="isProjectTotalRepay != null  and isProjectTotalRepay != ''"> and is_project_total_repay = #{isProjectTotalRepay}</if>
            <if test="isProjectRepay8 != null  and isProjectRepay8 != ''"> and is_project_repay8 = #{isProjectRepay8}</if>
            <if test="isProjectRepay7Finish != null  and isProjectRepay7Finish != ''"> and is_project_repay7_finish = #{isProjectRepay7Finish}</if>
            <if test="isProjectRepay8Normal != null  and isProjectRepay8Normal != ''"> and is_project_repay8_normal = #{isProjectRepay8Normal}</if>
            <if test="isResultFpd10 != null  and isResultFpd10 != ''"> and is_result_fpd10 = #{isResultFpd10}</if>
            <if test="isResultVintage != null  and isResultVintage != ''"> and is_result_vintage = #{isResultVintage}</if>
            <if test="isResultBalanceDistribution != null  and isResultBalanceDistribution != ''"> and is_result_balance_distribution = #{isResultBalanceDistribution}</if>
            <if test="status != null  and status != ''"> and dpp.status = #{status}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="productName != null and productName != ''"> and (product_name like concat('%', #{productName}, '%') or sdd.dict_label like concat('%',#{productName},'%'))</if>
            <if test="systemNo != null and systemNo != '' "> and system_no = #{systemNo}</if>
            <if test="projectId != null  and projectId != ''"> and project_id = #{projectId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
        order by dpp.id DESC
    </select>

    <select id="selectDProjectParameterById" parameterType="Integer" resultMap="DProjectParameterResult">
       select dpp.id, platform_no, cust_no, partner_no, fund_no, it_no, product_no, is_mapping, is_project_finish, project_finish_date, is_project_company,
              is_project_td, is_project_plan, is_project_plan_update, is_project_plan_reset, is_project_repay1, is_project_repay4, is_project_repay5,
              is_project_repay7, is_project_total_repay7, is_project_total_repay, is_project_repay8, is_project_repay7_finish, is_project_repay8_normal,
              is_result_fpd10, is_result_vintage, is_result_balance_distribution, remark, status,dpp.check_status, dpp.create_time, dpp.update_time, create_by, update_by,
              product_name, system_no, project_id, project_name,description
       from d_project_parameter dpp
       left join oa_project_deploy opd on opd.id = dpp.project_id
        where dpp.id = #{id}
    </select>

    <insert id="insertDProjectParameter" parameterType="DProjectParameter" useGeneratedKeys="true" keyProperty="id">
        insert into d_project_parameter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platformNo != null and platformNo != ''">platform_no,</if>
            <if test="custNo != null">cust_no,</if>
            <if test="partnerNo != null">partner_no,</if>
            <if test="fundNo != null">fund_no,</if>
            <if test="itNo != null">it_no,</if>
            <if test="productNo != null">product_no,</if>
            <if test="isMapping != null">is_mapping,</if>
            <if test="isProjectFinish != null">is_project_finish,</if>
            <if test="projectFinishDate != null">project_finish_date,</if>
            <if test="isProjectCompany != null">is_project_company,</if>
            <if test="isProjectTd != null">is_project_td,</if>
            <if test="isProjectPlan != null">is_project_plan,</if>
            <if test="isProjectPlanUpdate != null">is_project_plan_update,</if>
            <if test="isProjectPlanReset != null">is_project_plan_reset,</if>
            <if test="isProjectRepay1 != null">is_project_repay1,</if>
            <if test="isProjectRepay4 != null">is_project_repay4,</if>
            <if test="isProjectRepay5 != null">is_project_repay5,</if>
            <if test="isProjectRepay7 != null">is_project_repay7,</if>
            <if test="isProjectTotalRepay7 != null">is_project_total_repay7,</if>
            <if test="isProjectTotalRepay != null">is_project_total_repay,</if>
            <if test="isProjectRepay8 != null">is_project_repay8,</if>
            <if test="isProjectRepay7Finish != null">is_project_repay7_finish,</if>
            <if test="isProjectRepay8Normal != null">is_project_repay8_normal,</if>
            <if test="isResultFpd10 != null">is_result_fpd10,</if>
            <if test="isResultVintage != null">is_result_vintage,</if>
            <if test="isResultBalanceDistribution != null">is_result_balance_distribution,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="checkStatus != null and checkStatus != ''">check_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="productName != null">product_name,</if>
            <if test="systemNo != null">system_no,</if>
            <if test="projectId != null">project_id,</if>
            <if test="description != null">description,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platformNo != null and platformNo != ''">#{platformNo},</if>
            <if test="custNo != null">#{custNo},</if>
            <if test="partnerNo != null">#{partnerNo},</if>
            <if test="fundNo != null">#{fundNo},</if>
            <if test="itNo != null">#{itNo},</if>
            <if test="productNo != null">#{productNo},</if>
            <if test="isMapping != null">#{isMapping},</if>
            <if test="isProjectFinish != null">#{isProjectFinish},</if>
            <if test="projectFinishDate != null">#{projectFinishDate},</if>
            <if test="isProjectCompany != null">#{isProjectCompany},</if>
            <if test="isProjectTd != null">#{isProjectTd},</if>
            <if test="isProjectPlan != null">#{isProjectPlan},</if>
            <if test="isProjectPlanUpdate != null">#{isProjectPlanUpdate},</if>
            <if test="isProjectPlanReset != null">#{isProjectPlanReset},</if>
            <if test="isProjectRepay1 != null">#{isProjectRepay1},</if>
            <if test="isProjectRepay4 != null">#{isProjectRepay4},</if>
            <if test="isProjectRepay5 != null">#{isProjectRepay5},</if>
            <if test="isProjectRepay7 != null">#{isProjectRepay7},</if>
            <if test="isProjectTotalRepay7 != null">#{isProjectTotalRepay7},</if>
            <if test="isProjectTotalRepay != null">#{isProjectTotalRepay},</if>
            <if test="isProjectRepay8 != null">#{isProjectRepay8},</if>
            <if test="isProjectRepay7Finish != null">#{isProjectRepay7Finish},</if>
            <if test="isProjectRepay8Normal != null">#{isProjectRepay8Normal},</if>
            <if test="isResultFpd10 != null">#{isResultFpd10},</if>
            <if test="isResultVintage != null">#{isResultVintage},</if>
            <if test="isResultBalanceDistribution != null">#{isResultBalanceDistribution},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="checkStatus != null and checkStatus != ''">#{checkStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="productName != null">#{productName},</if>
            <if test="systemNo != null">#{systemNo},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="description != null">#{description},</if>
         </trim>
    </insert>

    <update id="updateDProjectParameter" parameterType="DProjectParameter">
        update d_project_parameter
        <trim prefix="SET" suffixOverrides=",">
            <if test="platformNo != null and platformNo != ''">platform_no = #{platformNo},</if>
            <if test="custNo != null">cust_no = #{custNo},</if>
            <if test="partnerNo != null">partner_no = #{partnerNo},</if>
            <if test="fundNo != null">fund_no = #{fundNo},</if>
            <if test="itNo != null">it_no = #{itNo},</if>
            <if test="productNo != null">product_no = #{productNo},</if>
            <if test="isMapping != null">is_mapping = #{isMapping},</if>
            <if test="isProjectFinish != null">is_project_finish = #{isProjectFinish},</if>
            <if test="projectFinishDate != null">project_finish_date = #{projectFinishDate},</if>
            <if test="isProjectCompany != null">is_project_company = #{isProjectCompany},</if>
            <if test="isProjectTd != null">is_project_td = #{isProjectTd},</if>
            <if test="isProjectPlan != null">is_project_plan = #{isProjectPlan},</if>
            <if test="isProjectPlanUpdate != null">is_project_plan_update = #{isProjectPlanUpdate},</if>
            <if test="isProjectPlanReset != null">is_project_plan_reset = #{isProjectPlanReset},</if>
            <if test="isProjectRepay1 != null">is_project_repay1 = #{isProjectRepay1},</if>
            <if test="isProjectRepay4 != null">is_project_repay4 = #{isProjectRepay4},</if>
            <if test="isProjectRepay5 != null">is_project_repay5 = #{isProjectRepay5},</if>
            <if test="isProjectRepay7 != null">is_project_repay7 = #{isProjectRepay7},</if>
            <if test="isProjectTotalRepay7 != null">is_project_total_repay7 = #{isProjectTotalRepay7},</if>
            <if test="isProjectTotalRepay != null">is_project_total_repay = #{isProjectTotalRepay},</if>
            <if test="isProjectRepay8 != null">is_project_repay8 = #{isProjectRepay8},</if>
            <if test="isProjectRepay7Finish != null">is_project_repay7_finish = #{isProjectRepay7Finish},</if>
            <if test="isProjectRepay8Normal != null">is_project_repay8_normal = #{isProjectRepay8Normal},</if>
            <if test="isResultFpd10 != null">is_result_fpd10 = #{isResultFpd10},</if>
            <if test="isResultVintage != null">is_result_vintage = #{isResultVintage},</if>
            <if test="isResultBalanceDistribution != null">is_result_balance_distribution = #{isResultBalanceDistribution},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="checkStatus != null and checkStatus != ''">check_status = #{checkStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="systemNo != null">system_no = #{systemNo},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="description != null">description = #{description},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDProjectParameterById" parameterType="Integer">
        delete from d_project_parameter where id = #{id}
    </delete>

    <delete id="deleteDProjectParameterByIds" parameterType="String">
        delete from d_project_parameter where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectProjectList" resultMap="DProjectParameterResult">
        <include refid="selectDProjectParameterVo"></include>
        where is_mapping = 'Y' and system_no is not null and project_id is not null
    </select>
    <select id="selectProCodeByProjectId" resultType="java.lang.String">
        select product_no from  d_project_parameter
        <where>
            1=1
            <if test="projectIds != null  ">
                and project_id in
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryproductListByProjectId" resultType="map">
        select product_name label,product_no value from d_project_parameter where 1 = 1
        and product_no is not null and product_name is not null
        <if test="projectIds != null and projectIds.size() != 0 ">
            and project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
    </select>
    <select id="querySysDataListByProjectId" resultType="map">
        SELECT sdd.dict_label label ,dpp.system_no value FROM d_project_parameter dpp LEFT JOIN sys_dict_data sdd ON dpp.system_no = sdd.dict_code
        where 1=1 and dpp.system_no is not null
        <if test="projectIds != null and projectIds.size() != 0  ">
            and dpp.project_id in
            <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>

        GROUP BY dpp.system_no
    </select>
</mapper>
