<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.kaoqin.mapper.MonthLogSlaveMapper">

    <resultMap type="MonthLogSlave" id="MonthLogSlaveResult">
        <result property="id"    column="id"    />
        <result property="mainId"    column="main_id"    />
        <result property="jobRole"    column="job_role"    />
        <result property="workContent"    column="work_content"    />
        <result property="complete"    column="complete"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMonthLogSlaveVo">
        select id, main_id, job_role, work_content, complete, is_delete,remark, create_by, create_time, update_by, update_time from kq_month_log_slave
    </sql>

    <select id="selectMonthLogSlaveList" parameterType="MonthLogSlave" resultMap="MonthLogSlaveResult">
        <include refid="selectMonthLogSlaveVo"/>
        <where>
            and is_delete = 1
            <if test="mainId != null "> and main_id = #{mainId}</if>
            <if test="jobRole != null  and jobRole != ''"> and job_role = #{jobRole}</if>
            <if test="workContent != null  and workContent != ''"> and work_content = #{workContent}</if>
            <if test="complete != null "> and complete = #{complete}</if>
            <if test="mainIds != null and mainIds.size() > 0">
                and main_id in
                <foreach collection="mainIds" item="mainId" separator="," open="(" close=")">
                    #{mainId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectMonthLogSlaveById" parameterType="Long" resultMap="MonthLogSlaveResult">
        <include refid="selectMonthLogSlaveVo"/>
        where id = #{id}
    </select>

    <insert id="insertMonthLogSlave" parameterType="MonthLogSlave" useGeneratedKeys="true" keyProperty="id">
        insert into kq_month_log_slave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mainId != null">main_id,</if>
            <if test="jobRole != null">job_role,</if>
            <if test="workContent != null">work_content,</if>
            <if test="complete != null">complete,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mainId != null">#{mainId},</if>
            <if test="jobRole != null">#{jobRole},</if>
            <if test="workContent != null">#{workContent},</if>
            <if test="complete != null">#{complete},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMonthLogSlave" parameterType="MonthLogSlave">
        update kq_month_log_slave
        <trim prefix="SET" suffixOverrides=",">
            <if test="mainId != null">main_id = #{mainId},</if>
            <if test="jobRole != null">job_role = #{jobRole},</if>
            <if test="workContent != null">work_content = #{workContent},</if>
            <if test="complete != null">complete = #{complete},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMonthLogSlaveById" parameterType="Long">
        delete from kq_month_log_slave where id = #{id}
    </delete>

    <delete id="deleteMonthLogSlaveByIds" parameterType="String">
        delete from kq_month_log_slave where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="replaceDayLogBatch" parameterType="java.util.List">
        REPLACE INTO kq_month_log_slave
            (id, main_id,job_role,work_content,complete,is_delete,create_by,create_time,update_by,update_time)
        VALUES
        <foreach item="item" collection="list" separator=",">
            (#{item.id},
             #{item.mainId},
             #{item.jobRole},
             #{item.workContent},
             #{item.complete},
             #{item.isDelete},
             #{item.createBy},
             #{item.createTime},
             #{item.updateBy},
             #{item.updateTime}
            )
        </foreach>
    </update>
</mapper>
