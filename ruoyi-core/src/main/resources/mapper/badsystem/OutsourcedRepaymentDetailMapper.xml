<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.badsystem.mapper.OutsourcedRepaymentDetailMapper">

    <resultMap type="OutsourcedRepaymentDetailVo" id="OutsourcedRepaymentDetailResult">
        <result property="id"    column="id"    />
        <result property="loanBill"    column="loan_bill"    />
        <result property="outsourcedProjectNumber"    column="outsourced_project_number"    />
        <result property="borrowerName"    column="borrower_name"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="repaymentAmount"    column="repayment_amount"    />
        <result property="repaymentTime"    column="repayment_time"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="trusteeInstitutionId"    column="trustee_institution_id"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="serviceAmount"    column="service_amount"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="trusteeInstitutionName"    column="mechanism_name"    />
    </resultMap>

    <sql id="selectOutsourcedRepaymentDetailVo">
        select id, loan_bill, outsourced_project_number, borrower_name, phone_number, repayment_amount, repayment_time, serial_number, trustee_institution_id, check_status, service_amount, remark, create_by, create_time, update_by, update_time from bl_outsourced_repayment_detail
    </sql>

    <select id="selectOutsourcedRepaymentDetailList" parameterType="OutsourcedRepaymentDetail" resultMap="OutsourcedRepaymentDetailResult">
        select bord.id, bord.loan_bill, bord.outsourced_project_number, bord.borrower_name, bord.phone_number, bord.repayment_amount
             , bord.repayment_time, bord.serial_number, bord.trustee_institution_id, bord.check_status, bord.service_amount
             , bord.remark, bord.create_by, bord.create_time, bord.update_by, bord.update_time
             , bm.mechanism_name
        from bl_outsourced_repayment_detail bord
        left join bl_mechanism bm on bord.trustee_institution_id = bm.id
        <where>
            <if test="loanBill != null  and loanBill != ''"> and bord.loan_bill = #{loanBill}</if>
            <if test="outsourcedProjectNumber != null  and outsourcedProjectNumber != ''"> and bord.outsourced_project_number = #{outsourcedProjectNumber}</if>
            <if test="borrowerName != null  and borrowerName != ''"> and bord.borrower_name like concat('%', #{borrowerName}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and bord.phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="repaymentAmount != null "> and bord.repayment_amount = #{repaymentAmount}</if>
            <if test="repaymentTime != null "> and bord.repayment_time = #{repaymentTime}</if>
            <if test="serialNumber != null  and serialNumber != ''"> and bord.serial_number = #{serialNumber}</if>
            <if test="trusteeInstitutionId != null "> and bord.trustee_institution_id = #{trusteeInstitutionId}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and bord.check_status = #{checkStatus}</if>
            <if test="serviceAmount != null "> and bord.service_amount = #{serviceAmount}</if>
            <if test="outsourcedProjectNumberList != null and outsourcedProjectNumberList.size() > 0">
                and bord.outsourced_project_number in
                <foreach collection="outsourcedProjectNumberList" item="number" separator="," open="(" close=")">
                    #{number}
                </foreach>
            </if>
        </where>
        order by bord.id desc
    </select>

    <select id="selectOutsourcedRepaymentDetailById" parameterType="Long" resultMap="OutsourcedRepaymentDetailResult">
        <include refid="selectOutsourcedRepaymentDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertOutsourcedRepaymentDetail" parameterType="OutsourcedRepaymentDetail" useGeneratedKeys="true" keyProperty="id">
        insert into bl_outsourced_repayment_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="loanBill != null">loan_bill,</if>
            <if test="outsourcedProjectNumber != null">outsourced_project_number,</if>
            <if test="borrowerName != null">borrower_name,</if>
            <if test="phoneNumber != null">phone_number,</if>
            <if test="repaymentAmount != null">repayment_amount,</if>
            <if test="repaymentTime != null">repayment_time,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="trusteeInstitutionId != null">trustee_institution_id,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="serviceAmount != null">service_amount,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="loanBill != null">#{loanBill},</if>
            <if test="outsourcedProjectNumber != null">#{outsourcedProjectNumber},</if>
            <if test="borrowerName != null">#{borrowerName},</if>
            <if test="phoneNumber != null">#{phoneNumber},</if>
            <if test="repaymentAmount != null">#{repaymentAmount},</if>
            <if test="repaymentTime != null">#{repaymentTime},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="trusteeInstitutionId != null">#{trusteeInstitutionId},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="serviceAmount != null">#{serviceAmount},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOutsourcedRepaymentDetail" parameterType="OutsourcedRepaymentDetail">
        update bl_outsourced_repayment_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="loanBill != null">loan_bill = #{loanBill},</if>
            <if test="outsourcedProjectNumber != null">outsourced_project_number = #{outsourcedProjectNumber},</if>
            <if test="borrowerName != null">borrower_name = #{borrowerName},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="repaymentAmount != null">repayment_amount = #{repaymentAmount},</if>
            <if test="repaymentTime != null">repayment_time = #{repaymentTime},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="trusteeInstitutionId != null">trustee_institution_id = #{trusteeInstitutionId},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="serviceAmount != null">service_amount = #{serviceAmount},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOutsourcedRepaymentDetailById" parameterType="Long">
        delete from bl_outsourced_repayment_detail where id = #{id}
    </delete>

    <delete id="deleteOutsourcedRepaymentDetailByIds" parameterType="String">
        delete from bl_outsourced_repayment_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchOutsourcedRepaymentDetail" parameterType="java.util.List">
        INSERT INTO bl_outsourced_repayment_detail
        (
          loan_bill, outsourced_project_number, borrower_name, phone_number, repayment_amount
        , repayment_time, serial_number, trustee_institution_id, check_status, service_amount
        , remark, create_by
        )
        VALUES
        <foreach item="item" collection="list" separator=",">
            (
            #{item.loanBill},
            #{item.outsourcedProjectNumber},
            #{item.borrowerName},
            #{item.phoneNumber},
            #{item.repaymentAmount},
            #{item.repaymentTime},
            #{item.serialNumber},
            #{item.trusteeInstitutionId},
            #{item.checkStatus},
            #{item.serviceAmount},
            #{item.remark},
            #{item.createBy}
            )
        </foreach>
    </insert>

    <select id="selectOutsourcedRepaymentDetailListByLoanBills" parameterType="java.util.List"  resultMap="OutsourcedRepaymentDetailResult">
        select bord.id, bord.loan_bill, bord.outsourced_project_number, bord.borrower_name, bord.phone_number, bord.repayment_amount
        , bord.repayment_time, bord.serial_number, bord.trustee_institution_id, bord.check_status, bord.service_amount
        , bord.remark, bord.create_by, bord.create_time, bord.update_by, bord.update_time
        from bl_outsourced_repayment_detail bord
        where
            bord.loan_bill in
            <foreach collection="collection" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

    </select>
</mapper>
