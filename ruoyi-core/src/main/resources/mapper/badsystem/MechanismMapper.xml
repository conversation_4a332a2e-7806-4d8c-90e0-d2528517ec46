<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.badsystem.mapper.MechanismMapper">

    <resultMap type="MechanismVo" id="MechanismResult">
        <result property="id"    column="id"    />
        <result property="mechanismName"    column="mechanism_name"    />
        <result property="mechanismShortName"    column="mechanism_short_name"    />
        <result property="mechanismCode"    column="mechanism_code"    />
        <result property="joinTime"    column="join_time"    />
        <result property="signEndTime"    column="sign_end_time"    />
        <result property="mechanismType"    column="mechanism_type"  typeHandler="org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler"  />
        <result property="mechanismDisposalMode"    column="mechanism_disposal_mode"  typeHandler="org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler" />
        <result property="businessManager"    column="business_manager"   typeHandler="org.ruoyi.core.meeting.domain.StringToLongListTypeHandler"  />
        <result property="settlementDate"    column="settlement_date"    />
        <result property="unifiedCreditCode"    column="unified_credit_code"    />
        <result property="enterpriseName"    column="enterprise_name"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="incorporationDate"    column="incorporation_date"    />
        <result property="personnelScale"    column="personnel_scale"    />
        <result property="collaborationStatus"    column="collaboration_status"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="processId"    column="process_id"    />
        <collection property="mechanismSettlementFormulaList" column = "id" select="selectMechanismSettlementFormulaList"/>
        <collection property="mechanismFinanceAccountList" column = "id" select="selectMechanismFinanceAccountList"/>
        <collection property="mechanismContactsList" column = "id" select="selectMechanismContactsList"/>
    </resultMap>

    <resultMap type="MechanismSettlementFormula" id="MechanismSettlementFormulaResult">
        <result property="id"    column="id"    />
        <result property="mechanismId"    column="mechanism_id"    />
        <result property="settlementCycle"  column="settlement_cycle"  typeHandler="org.ruoyi.core.badsystem.domain.tool.StringToStringListTypeHandler"  />
        <result property="settlementRatio"    column="settlement_ratio"    />
        <result property="enableStatus"    column="enable_status"    />
    </resultMap>

    <sql id="selectMechanismVo">
        select id, mechanism_name, mechanism_short_name, mechanism_code, join_time, sign_end_time, mechanism_type, mechanism_disposal_mode, business_manager, settlement_date, unified_credit_code, enterprise_name, legal_person, incorporation_date, personnel_scale, collaboration_status, status, process_id,create_by, create_time, update_by, update_time from bl_mechanism
    </sql>

    <select id="selectMechanismList" parameterType="MechanismVo" resultMap="MechanismResult">
        select id, mechanism_name, mechanism_short_name, mechanism_code, join_time, sign_end_time, mechanism_type
             , mechanism_disposal_mode, business_manager, settlement_date, unified_credit_code, enterprise_name
             , legal_person, incorporation_date, personnel_scale
             , collaboration_status, status,process_id, create_by, create_time, update_by, update_time
        from bl_mechanism bm
        <where>
            <if test="mechanismName != null  and mechanismName != ''"> and mechanism_name like concat('%', #{mechanismName}, '%')</if>
            <if test="mechanismShortName != null  and mechanismShortName != ''"> and mechanism_short_name like concat('%', #{mechanismShortName}, '%')</if>
            <if test="mechanismCode != null  and mechanismCode != ''"> and mechanism_code = #{mechanismCode}</if>
            <if test="joinTime != null "> and join_time = #{joinTime}</if>
            <if test="signEndTime != null "> and sign_end_time = #{signEndTime}</if>
            <if test="mechanismType != null and mechanismType.size() > 0">
                and bm.id in (
                select id from bl_mechanism t
                where
                <foreach collection="mechanismType" item="type" separator="or">
                    FIND_IN_SET(#{type}, t.mechanism_type) > 0
                </foreach>
                )
            </if>
            <if test="mechanismDisposalMode != null and mechanismDisposalMode.size() > 0">
                and bm.id in (
                select id from bl_mechanism t
                where
                <foreach collection="mechanismDisposalMode" item="mode" separator="or">
                    FIND_IN_SET(#{mode}, t.mechanism_disposal_mode) > 0
                </foreach>
                )
            </if>
            <if test="businessManager != null  and businessManager != ''"> and business_manager = #{businessManager}</if>
            <if test="settlementDate != null "> and settlement_date = #{settlementDate}</if>
            <if test="unifiedCreditCode != null  and unifiedCreditCode != ''"> and unified_credit_code = #{unifiedCreditCode}</if>
            <if test="enterpriseName != null  and enterpriseName != ''"> and enterprise_name like concat('%', #{enterpriseName}, '%')</if>
            <if test="legalPerson != null  and legalPerson != ''"> and legal_person = #{legalPerson}</if>
            <if test="incorporationDate != null "> and incorporation_date = #{incorporationDate}</if>
            <if test="personnelScale != null "> and personnel_scale = #{personnelScale}</if>
            <if test="collaborationStatus != null  and collaborationStatus != ''"> and collaboration_status = #{collaborationStatus}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="mechanismNameList != null and mechanismNameList.size() > 0">
                bm.mechanism_name in
                <foreach collection="mechanismNameList" item="name" separator="," open="(" close=")">
                    #{name}
                </foreach>
            </if>
        </where>
        order by bm.id desc
    </select>

    <select id="selectMechanismById" parameterType="Long" resultMap="MechanismResult">
        select id, mechanism_name, mechanism_short_name, mechanism_code, join_time, sign_end_time, mechanism_type
             , mechanism_disposal_mode, business_manager, settlement_date, unified_credit_code, enterprise_name
             , legal_person, incorporation_date, personnel_scale
             , collaboration_status, status,process_id, create_by, create_time, update_by, update_time
        from bl_mechanism bm
        where id = #{id}
    </select>

    <insert id="insertMechanism" parameterType="Mechanism" useGeneratedKeys="true" keyProperty="id">
        insert into bl_mechanism
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mechanismName != null">mechanism_name,</if>
            <if test="mechanismShortName != null">mechanism_short_name,</if>
            <if test="mechanismCode != null">mechanism_code,</if>
            <if test="joinTime != null">join_time,</if>
            <if test="signEndTime != null">sign_end_time,</if>
            <if test="mechanismType != null">mechanism_type,</if>
            <if test="mechanismDisposalMode != null">mechanism_disposal_mode,</if>
            <if test="businessManager != null">business_manager,</if>
            <if test="settlementDate != null">settlement_date,</if>
            <if test="unifiedCreditCode != null">unified_credit_code,</if>
            <if test="enterpriseName != null">enterprise_name,</if>
            <if test="legalPerson != null">legal_person,</if>
            <if test="incorporationDate != null">incorporation_date,</if>
            <if test="personnelScale != null">personnel_scale,</if>
            <if test="collaborationStatus != null">collaboration_status,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mechanismName != null">#{mechanismName},</if>
            <if test="mechanismShortName != null">#{mechanismShortName},</if>
            <if test="mechanismCode != null">#{mechanismCode},</if>
            <if test="joinTime != null">#{joinTime},</if>
            <if test="signEndTime != null">#{signEndTime},</if>
            <if test="mechanismType != null">#{mechanismType ,typeHandler=org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler},</if>
            <if test="mechanismDisposalMode != null">#{mechanismDisposalMode ,typeHandler=org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler},</if>
            <if test="businessManager != null">#{businessManager,typeHandler=org.ruoyi.core.meeting.domain.StringToLongListTypeHandler},</if>
            <if test="settlementDate != null">#{settlementDate},</if>
            <if test="unifiedCreditCode != null">#{unifiedCreditCode},</if>
            <if test="enterpriseName != null">#{enterpriseName},</if>
            <if test="legalPerson != null">#{legalPerson},</if>
            <if test="incorporationDate != null">#{incorporationDate},</if>
            <if test="personnelScale != null">#{personnelScale},</if>
            <if test="collaborationStatus != null">#{collaborationStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMechanism" parameterType="Mechanism">
        update bl_mechanism
        <trim prefix="SET" suffixOverrides=",">
            <if test="mechanismName != null">mechanism_name = #{mechanismName},</if>
            <if test="mechanismShortName != null">mechanism_short_name = #{mechanismShortName},</if>
            <if test="mechanismCode != null">mechanism_code = #{mechanismCode},</if>
            <if test="joinTime != null">join_time = #{joinTime},</if>
            <if test="signEndTime != null">sign_end_time = #{signEndTime},</if>
            <if test="mechanismType != null">mechanism_type = #{mechanismType ,typeHandler=org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler},</if>
            <if test="mechanismDisposalMode != null">mechanism_disposal_mode = #{mechanismDisposalMode ,typeHandler=org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler},</if>
            <if test="businessManager != null">business_manager = #{businessManager,typeHandler=org.ruoyi.core.meeting.domain.StringToLongListTypeHandler},</if>
            <if test="settlementDate != null">settlement_date = #{settlementDate},</if>
            <if test="unifiedCreditCode != null">unified_credit_code = #{unifiedCreditCode},</if>
            <if test="enterpriseName != null">enterprise_name = #{enterpriseName},</if>
            <if test="legalPerson != null">legal_person = #{legalPerson},</if>
            <if test="incorporationDate != null">incorporation_date = #{incorporationDate},</if>
            <if test="personnelScale != null">personnel_scale = #{personnelScale},</if>
            <if test="collaborationStatus != null">collaboration_status = #{collaborationStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="processId != null">process_id = #{processId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMechanismById" parameterType="Long">
        delete from bl_mechanism where id = #{id}
    </delete>

    <delete id="deleteMechanismByIds" parameterType="String">
        delete from bl_mechanism where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMechanismSettlementFormulaList"  resultMap="MechanismSettlementFormulaResult">
        select
            id, mechanism_id, settlement_cycle, settlement_ratio, enable_status
        from bl_mechanism_settlement_formula
        where mechanism_id = #{id}
    </select>

    <select id="selectMechanismContactsList"  resultType="MechanismContacts">
        select
            id, mechanism_id, name, sex, position, wechat, phone_number, email, remark
        from bl_mechanism_contacts
        where mechanism_id = #{id}
    </select>


    <select id="selectMechanismFinanceAccountList" resultType="MechanismFinanceAccount">
        select
            id, mechanism_id, account_name, duty_paragraph, bank, bank_account
        from bl_mechanism_finance_account
        where mechanism_id = #{id}
    </select>

    <select id="selectMechanismByProcessId" parameterType="String" resultMap="MechanismResult">
        select id, mechanism_name, mechanism_short_name, mechanism_code, join_time, sign_end_time, mechanism_type
             , mechanism_disposal_mode, business_manager, settlement_date, unified_credit_code, enterprise_name
             , legal_person, incorporation_date, personnel_scale
             , collaboration_status, status,process_id, create_by, create_time, update_by, update_time
        from bl_mechanism bm
        where process_id = #{processId}
    </select>

    <select id="getCountByCreateTime" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM bl_mechanism
        <where>
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>

    <insert id="batchMechanism" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into bl_mechanism
        (mechanism_name, mechanism_short_name, mechanism_code, join_time, sign_end_time,
        mechanism_type, mechanism_disposal_mode, business_manager,
        create_by, create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.mechanismName},
            #{item.mechanismShortName},
            #{item.mechanismCode},
            #{item.joinTime},
            #{item.signEndTime},
            #{item.mechanismType,typeHandler=org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler},
            #{item.mechanismDisposalMode,typeHandler=org.ruoyi.core.badsystem.domain.tool.StringToIntegerListTypeHandler},
            #{item.businessManager,typeHandler=org.ruoyi.core.meeting.domain.StringToLongListTypeHandler},
            #{item.createBy},
            #{item.createTime}
            )
        </foreach>
    </insert>
</mapper>
