<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.offSupply.mapper.OffReceiveMainMapper">
    <!--办公用品领用主表-->
    <resultMap type="OffReceiveMain" id="OffReceiveMainResult">
        <result property="id"    column="id"    />
        <result property="applicationCode"    column="application_code"    />
        <result property="userId"    column="user_id"    />
        <result property="companyId"    column="company_id"    />
        <result property="processId"    column="process_id"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="cause"    column="cause"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="itemName"    column="item_name"    />
        <result property="approvalStatusLabel"    column="approvalStatusLabel"    />
        <result property="userNickName"    column="nick_name"    />
    </resultMap>

    <!--<resultMap id="OffReceiveMainOffReceiveDetailResult" type="OffReceiveMain" extends="OffReceiveMainResult">
        <collection property="offReceiveDetailList" notNullColumn="id" javaType="java.util.List" resultMap="OffReceiveDetailResult" />
    </resultMap>-->

    <!--办公用品领用明细表-->
    <resultMap type="OffReceiveDetail" id="OffReceiveDetailResult">
        <result property="id"    column="id"    />
        <result property="receiveId"    column="receive_id"    />
        <result property="supplyId"    column="supply_id"    />
        <result property="beforeNum"    column="before_num"    />
        <result property="applyNum"    column="apply_num"    />
        <result property="residueNum"    column="residue_num"    />
        <result property="processId"    column="process_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectOffReceiveMainVo">
        select id, application_code, user_id, process_id, company_id, approval_status, del_flag, cause, remark, create_by, create_time, submit_time, approval_time from off_receive_main
    </sql>

    <select id="selectOffReceiveMainList" parameterType="OffReceiveMain" resultMap="OffReceiveMainResult">
        select ss.* from (
        SELECT
        orm.id,
        orm.application_code,
        orm.user_id,
        orm.process_id,
        orm.company_id,
        orm.approval_status,
        GROUP_CONCAT( osm.item_name separator '、' ) AS item_name ,
        case
        when orm.approval_status = '0' then '未提交'
        when orm.approval_status = '1' then '审核中'
        when orm.approval_status = '2' then '审核不通过'
        when orm.approval_status = '3' then '审核通过'
        end as approvalStatusLabel,
        orm.del_flag,
        orm.cause,
        orm.remark,
        orm.create_by,
        orm.create_time,
        orm.submit_time,
        orm.approval_time,
        su.nick_name
        FROM off_receive_main orm
        LEFT JOIN off_receive_detail ord ON orm.id = ord.receive_id
        LEFT JOIN off_supply_main osm ON ord.supply_id = osm.id
        left join sys_user su on orm.user_id = su.user_id
        WHERE
        1=1
        <!-- 添加审核不通过和未提交状态的数据查看权限控制 -->
        <if test="loginUserName != null and loginUserName != ''">
            and (orm.approval_status in ('0','2') AND orm.create_by = #{loginUserName})
        </if>
        <if test="userSubordinateList != null and userSubordinateList.size() > 0">
            or orm.user_id in
            <foreach collection="userSubordinateList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="authCompanyIds != null and authCompanyIds.size() > 0">
            or orm.company_id in
            <foreach collection="authCompanyIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY orm.id
        )ss
        <where>
            <if test="itemName != null and itemName != ''">
                AND ss.item_name LIKE CONCAT('%', #{itemName}, '%')
            </if>
            <if test="approvalStatus != null and approvalStatus != ''">
                AND ss.approval_status = #{approvalStatus}
            </if>
            <if test="createBeginTime != null ">and date_format(ss.create_time,'%Y-%m-%d') &gt;=
                DATE_FORMAT(#{createBeginTime},'%Y-%m-%d')
            </if>
            <if test="createEndTime != null ">and date_format(ss.create_time,'%Y-%m-%d') &lt;=
                DATE_FORMAT(#{createEndTime},'%Y-%m-%d')
            </if>
            and ss.del_flag = '0'
        </where>
        order by ss.create_time desc, ss.approval_status asc
    </select>
    
    <select id="selectOffReceiveMainById" parameterType="Long" resultType="org.ruoyi.core.offSupply.domain.OffReceiveMain">
        select orm.id, orm.application_code as applicationCode, orm.approval_status as approvalStatus, orm.user_id as userId, su.nick_name as userNickName, orm.cause, orm.remark,
               sc.company_short_name as companyShortName
        from off_receive_main orm
        left join sys_user su on orm.user_id = su.user_id
        left join sys_company sc on orm.company_id = sc.id
        where orm.id = #{id}
    </select>

    <insert id="insertOffReceiveMain" parameterType="OffReceiveMain" useGeneratedKeys="true" keyProperty="id">
        insert into off_receive_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationCode != null and applicationCode != ''">application_code,</if>
            <if test="userId != null">user_id,</if>
            <if test="processId != null and processId != ''">process_id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="cause != null and cause != ''">cause,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="approvalTime != null">approval_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationCode != null and applicationCode != ''">#{applicationCode},</if>
            <if test="userId != null">#{userId},</if>
            <if test="processId != null and processId != ''">#{processId},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="approvalStatus != null and approvalStatus != ''">#{approvalStatus},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="cause != null and cause != ''">#{cause},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
         </trim>
    </insert>

    <update id="updateOffReceiveMain" parameterType="OffReceiveMain">
        update off_receive_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationCode != null and applicationCode != ''">application_code = #{applicationCode},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="processId != null and processId != ''">process_id = #{processId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="cause != null and cause != ''">cause = #{cause},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOffReceiveMainById" parameterType="Long">
        update off_receive_main set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteOffReceiveMainByIds" parameterType="String">
        delete from off_receive_main where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteOffReceiveDetailByReceiveIds" parameterType="String">
        delete from off_receive_detail where receive_id in 
        <foreach item="receiveId" collection="array" open="(" separator="," close=")">
            #{receiveId}
        </foreach>
    </delete>

    <delete id="deleteOffReceiveDetailByReceiveId" parameterType="Long">
        delete from off_receive_detail where receive_id = #{receiveId}
    </delete>

    <delete id="deleteOffReceivePurchaseInfo">
        delete from off_receive_purchase_detail where process_id = #{processId}
    </delete>

    <insert id="batchOffReceiveDetail">
        insert into off_receive_detail(receive_id, before_num, supply_id, apply_num, residue_num, process_id, create_by, create_time) values
		<foreach item="item" index="index" collection="offReceiveDetailList" separator=",">
            (#{item.receiveId}, #{item.beforeNum}, #{item.supplyId}, #{item.applyNum}, #{item.residueNum}, #{item.processId}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <insert id="insertOffReceivePurchaseInfo">
        insert into off_receive_purchase_detail(process_id, json_data, create_by, create_time)
        values (#{processId}, #{jsonData}, #{createBy}, #{createTime})
    </insert>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM off_receive_main
        WHERE DATE(create_time) = CURDATE();
    </select>

    <update id="updateReceiveStatusByFlowId">
        update off_receive_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationCode != null and applicationCode != ''">application_code = #{applicationCode},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="processId != null and processId != ''">process_id = #{processId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="approvalStatus != null and approvalStatus != ''">approval_status = #{approvalStatus},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="cause != null and cause != ''">cause = #{cause},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
        </trim>
        where process_id = #{processId}
    </update>

    <select id="selectSupplyReceiveReportList" resultType="org.ruoyi.core.offSupply.domain.vo.OffReceiveReport">
        select tt.* from (
        SELECT
        ord.id as detailId,
        sc.id AS companyId,
        sc.company_short_name AS companyShortName,
        osm.id as itemId,
        osm.item_name AS itemName,
        osm.sys_code AS itemSysCode,
        osm.amount AS amount,
        osm.measure_unit AS measureUnit,
        su.nick_name AS recipientUser,
        DATE_FORMAT(orm.approval_time, '%Y年%c月%d日') AS approvalTime,
        orm.approval_time AS approvalTimeDate,
        orm.approval_status AS approvalStatus,
        ord.apply_num AS applyNum,
        ord.residue_num AS residueNum,
        orm.cause AS cause
        FROM off_receive_detail ord
        LEFT JOIN off_receive_main orm ON ord.receive_id = orm.id
        LEFT JOIN off_supply_main osm ON ord.supply_id = osm.id
        LEFT JOIN off_category_main ocm ON osm.category_id = ocm.id
        LEFT JOIN sys_company sc ON orm.company_id = sc.id
        LEFT JOIN sys_user su ON orm.user_id = su.user_id
        <trim prefix="WHERE" prefixOverrides="AND">
            <if test="authCompanyIds != null and authCompanyIds.size() > 0">
                and orm.company_id IN
                <foreach item="authCompanyId" collection="authCompanyIds" open="(" separator="," close=")">
                    #{authCompanyId}
                </foreach>
            </if>
            <if test="loginUserName != null and loginUserName != ''">
                and orm.approval_status in ('1','3') AND orm.create_by = #{loginUserName}
            </if>
            <if test="userSubordinateList != null and userSubordinateList.size() > 0">
                or orm.user_id in
                <foreach collection="userSubordinateList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND orm.approval_status IN ('1', '3')
            AND orm.del_flag = '0'
            AND ocm.del_flag = '0'
            AND osm.del_flag = '0'
        </trim>
        ) as tt
        <where>
            <if test="itemName != null and itemName != ''">
                and tt.itemName LIKE CONCAT('%', #{itemName}, '%')
            </if>
            <if test="receiveUserName != null and receiveUserName != ''">
                and tt.recipientUser LIKE CONCAT('%', #{receiveUserName}, '%')
            </if>
            <if test="companyId != null">
                and tt.companyId = #{companyId}
            </if>
            <if test="receiveBeginTime != null">
                and tt.approvalTimeDate &gt;= #{receiveBeginTime}
            </if>
            <if test="receiveEndTime != null">
                and tt.approvalTimeDate &lt;= #{receiveEndTime}
            </if>
        </where>
    </select>

    <select id="selectSysCompanyList" resultType="org.ruoyi.core.offSupply.domain.vo.SysComVo">
        select id, company_short_name AS 'companyShortName' from sys_company
        <where>
            <if test="authCompanyIds != null and authCompanyIds.size() > 0">
                and id in
                <foreach item="authCompanyId" collection="authCompanyIds" open="(" separator="," close=")">
                    #{authCompanyId}
                </foreach>
            </if>
            <if test="companyId != null">
                and id = #{companyId}
            </if>
            and is_inside = '1' and status = '0' and is_delete = '0'
        </where>
    </select>

    <select id="selectAllReceiveList" resultType="org.ruoyi.core.offSupply.domain.vo.OffReceiveReport">
        SELECT
        osm.sys_code AS itemSysCode,
        SUM(ord.apply_num) AS approvalStatusCount
        FROM
        off_supply_main osm
        LEFT JOIN
        off_receive_detail ord ON osm.id = ord.supply_id
        LEFT JOIN
        off_receive_main orm ON ord.receive_id = orm.id
        <where>
            <if test="itemIds != null and itemIds.size() > 0">
                and ord.id IN
                <foreach collection="itemIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and orm.approval_status = '1' and osm.del_flag = '0' and orm.del_flag = '0'
        </where>
        GROUP BY osm.sys_code
    </select>

    <select id="getNewAuthForModuleAndRole" resultType="com.ruoyi.system.domain.AuthMain">
        select * from auth_main
        <where>
            <if test="userId != null">
               and third_id = #{userId}
            </if>
            <if test="moduleType != null and moduleType != ''">
                and module_type = #{moduleType}
            </if>
            <if test="roleType != null and roleType != '' ">
                and role_type = #{roleType}
            </if>
            and status = '0' and third_type = '1' and permission_time >= now()
        </where>
    </select>

    <select id="selectAuthDetailByMainId" resultType="com.ruoyi.system.domain.AuthDetail">
        select * from auth_detail where auth_main_id = #{mainId} and status = '0' and third_table_name = 'off_supply_main' and third_table_alias_name = 'osmr'
    </select>

    <select id="selectAuthDetailByMainIdList" resultType="com.ruoyi.system.domain.AuthDetail">
        select * from auth_detail
        <where>
            <if test="newAuthForModuleAndRole != null and newAuthForModuleAndRole.size() > 0">
                and auth_main_id in
                <foreach collection="newAuthForModuleAndRole" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and status = '0' and third_table_name = 'sys_company' and third_table_alias_name = 'unit'
            </if>
        </where>
    </select>

    <select id="selectOaLaunchAuth" resultType="com.ruoyi.system.domain.AuthMain">
        SELECT am.id, am.third_id AS 'thirdId', am.role_type AS 'roleType'
        FROM auth_main am
        WHERE am.third_type = '1'
          AND am.STATUS = '0'
          AND am.third_id = #{userId}
          AND am.module_type = #{moduleType}
          AND am.role_type = #{roleType}
          AND am.permission_time >= now();
    </select>

    <select id="selectOffReceiveDetailByReceiveId" resultType="org.ruoyi.core.offSupply.domain.OffReceiveDetail">
        select ord.before_num as beforeNum, ord.supply_id as supplyId, ord.apply_num as applyNum, ord.residue_num as residueNum, ord.process_id as processId,
               osm.item_name as supplyName, ocm.category_name as categoryName, osm.measure_unit as measureUnit, osm.item_type as itemType
        from off_receive_main orm
        LEFT JOIN off_receive_detail ord on orm.id = ord.receive_id
        LEFT JOIN off_supply_main osm ON ord.supply_id = osm.id
        LEFT JOIN off_category_main ocm ON osm.category_id = ocm.id
        where orm.id = #{id}
    </select>

    <select id="selectOffReceiveMainByProcessId" resultType="org.ruoyi.core.offSupply.domain.OffReceivePurchaseDetail">
        select * from off_receive_purchase_detail where process_id = #{processId}
    </select>

</mapper>