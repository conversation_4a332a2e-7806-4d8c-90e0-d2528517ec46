package com.ruoyi.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author: 左东冉
 * @Create: 2024-03-08 14:44
 * @Description: 新闻操作常量
 **/
@Component
public class NewsOptionConfig {

    // 公共
    @Value("${native.imagePath}")
    public String nativeImagePath;

    // 青海昌泰
    @Value("${qhct.localServer}")
    public boolean qhctLocalServerFlag;
    @Value("${qhct.imageSuffix}")
    public String qhctImageSuffix;
    @Value("${qhct.imagePath}")
    public String qhctImagePath;
    @Value("${qhct.fileTargetPath}")
    public String qhctFileTargetPath;
    @Value("${qhct.fileBackupsPath}")
    public String qhctFileBackupsPath;
    @Value("${qhct.tranInfo}")
    public String qhctTranInfo; // 传输图片信息

    // 云南鼎丰
    @Value("${yndf.localServer}")
    public boolean yndfLocalServerFlag;
    @Value("${yndf.imageSuffix}")
    public String yndfImageSuffix;
    @Value("${yndf.imagePath}")
    public String yndfImagePath;
    @Value("${yndf.fileTargetPath}")
    public String yndfFileTargetPath;
    @Value("${yndf.fileBackupsPath}")
    public String yndfFileBackupsPath;

    @Value("${yndf.tranInfo}")
    public String yndfTranInfo; // 传输图片信息


    // 海南正堂
    @Value("${hnzt.localServer}")
    public boolean hnztLocalServerFlag;
    @Value("${hnzt.imageSuffix}")
    public String hnztImageSuffix;
    @Value("${hnzt.imagePath}")
    public String hnztImagePath;
    @Value("${hnzt.fileTargetPath}")
    public String hnztFileTargetPath;
    @Value("${hnzt.fileBackupsPath}")
    public String hnztFileBackupsPath;
    @Value("${hnzt.tranInfo}")
    public String hnztTranInfo; // 传输图片信息


    // 湖南樽昊
    @Value("${hnzh.localServer}")
    public boolean hnzhLocalServerFlag;
    @Value("${hnzh.imageSuffix}")
    public String hnzhImageSuffix;
    @Value("${hnzh.imagePath}")
    public String hnzhImagePath;
    @Value("${hnzh.fileTargetPath}")
    public String hnzhFileTargetPath;
    @Value("${hnzh.fileBackupsPath}")
    public String hnzhFileBackupsPath;
    @Value("${hnzh.tranInfo}")
    public String hnzhTranInfo; // 传输图片信息


    // 湖北富辰
    @Value("${hbfc.localServer}")
    public boolean hbfcLocalServerFlag;
    @Value("${hbfc.imageSuffix}")
    public String hbfcImageSuffix;
    @Value("${hbfc.imagePath}")
    public String hbfcImagePath;
    @Value("${hbfc.fileTargetPath}")
    public String hbfcFileTargetPath;
    @Value("${hbfc.fileBackupsPath}")
    public String hbfcFileBackupsPath;
    @Value("${hbfc.tranInfo}")
    public String hbfcTranInfo;


    // 福建大有
    @Value("${fjdy.localServer}")
    public boolean fjdyLocalServerFlag;
    @Value("${fjdy.imageSuffix}")
    public String fjdyImageSuffix;
    @Value("${fjdy.imagePath}")
    public String fjdyImagePath;
    @Value("${fjdy.fileTargetPath}")
    public String fjdyFileTargetPath;
    @Value("${fjdy.fileBackupsPath}")
    public String fjdyFileBackupsPath;
    @Value("${fjdy.tranInfo}")
    public String fjdyTranInfo;

    // 聚汇融盛

    @Value("${jhrs.localServer}")
    public boolean jhrsLocalServerFlag;
    @Value("${jhrs.imageSuffix}")
    public String jhrsImageSuffix;
    @Value("${jhrs.imagePath}")
    public String jhrsImagePath;
    @Value("${jhrs.fileTargetPath}")
    public String jhrsFileTargetPath;
    @Value("${jhrs.fileBackupsPath}")
    public String jhrsFileBackupsPath;
    @Value("${jhrs.tranInfo}")
    public String jhrsTranInfo; // 传输图片信息

}
