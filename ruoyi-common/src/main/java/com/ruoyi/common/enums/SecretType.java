package com.ruoyi.common.enums;


/**
 * 秘钥类型
 * <AUTHOR>
 *
 */
public enum SecretType {

	/*
	 * AES加密是建立在DES加密不能满足破解难度而产生的。由比利时两位非常著名的密码学家Joan <PERSON>en和Vincent R<PERSON>jmen设计，选取了分组长度为128比特，密钥长度为128比特、192比特和256比特的三个版本。
        分组密码有五种工作体制：

    1.电码本模式（Electronic Codebook Book (ECB)）；
    2.密码分组链接模式（Cipher Block Chaining (CBC)）；
    3.计算器模式（Counter (CTR)）；
    4.密码反馈模式（Cipher FeedBack (CFB)）；
    5.输出反馈模式（Output FeedBack (OFB)）。
    
    AES支持支持几种填充：NoPadding，PKCS5Padding，ISO10126Padding，PaddingMode.Zeros，PaddingMode.PKCS7;
	 */
	//算法/工作方式/填充方式
	TYPE1("1", "AES","AES/ECB/PKCS5Padding"), 


	
	
	
	TYPE2("2", "RSA","MD5withRSA");
//	TYPE3("2", "ECC","",1), 
//	TYPE4("2", "SM2","",1), 
//	TYPE5("1", "SM4","",1);

    private final String code;//1对称加密 2非对称加密
    private final String algorithm;//秘钥算法
    private final String workAlgorithm;//工作模式

    SecretType(String code, String algorithm, String workAlgorithm)
    {
        this.code = code;
        this.algorithm = algorithm;
        this.workAlgorithm = workAlgorithm;
    }

    public String getCode()
    {
        return code;
    }

    public String getAlgorithm()
    {
        return algorithm;
    }
    public String getWorkAlgorithm()
    {
        return workAlgorithm;
    }
}
