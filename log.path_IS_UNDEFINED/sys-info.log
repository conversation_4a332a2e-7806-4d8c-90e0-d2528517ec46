11:20:02.029 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
11:20:02.030 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 42766 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
11:20:02.034 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
11:20:04.428 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8888"]
11:20:04.429 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:20:04.429 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
11:20:04.481 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:20:07.054 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
11:20:07.787 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
11:20:09.453 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
11:20:09.453 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
11:20:09.951 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
11:20:09.954 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
11:20:09.956 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
11:20:09.967 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@5d0c47ba, ORIGINAL=[Ljava.lang.String;@7d38152d, PIC_CLICK=[Ljava.lang.String;@75e40acc]
11:20:09.967 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
11:20:10.402 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
11:20:10.402 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$b391bc5d (priority:10000)
11:20:10.402 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$b391bc5d (priority:10000)
11:20:10.572 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$b391bc5d (priority:10000)
11:20:10.602 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
11:20:10.603 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
11:20:10.604 [Thread-27] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
11:20:10.604 [Thread-28] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
11:20:10.604 [Thread-29] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
11:20:10.789 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:20:10.795 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:20:10.795 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:20:10.798 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749525610789'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

11:20:10.798 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
11:20:10.799 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:20:10.799 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1b74fb81
11:20:12.732 [restartedMain] INFO  o.r.c.s.i.DSecretKeyServiceImpl - [cacheSecretData,59] - 初始化秘钥到 Redis 成功
11:20:15.243 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8888"]
11:20:15.677 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 13.908 seconds (JVM running for 14.744)
11:20:15.906 [RMI TCP Connection(1)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:20:16.679 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749525610789 started.
11:20:18.071 [restartedMain] INFO  c.r.RuoYiApplication - [main,29] - 数据平台 RuoYiApplication 服务启动成功
11:20:25.225 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749525610789 paused.
11:20:25.254 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749525610789 shutting down.
11:20:25.254 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749525610789 paused.
11:20:25.257 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749525610789 shutdown complete.
11:20:25.258 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
11:20:25.277 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
11:20:25.278 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
11:20:25.278 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
11:20:25.278 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
11:20:25.307 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
11:20:25.317 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
13:45:51.815 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 47865 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
13:45:51.817 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
13:45:51.817 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
13:45:54.079 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
13:45:54.079 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:45:54.079 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
13:45:54.144 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:45:56.614 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
13:45:57.229 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
13:45:58.743 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
13:45:58.743 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
13:45:59.179 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
13:45:59.182 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
13:45:59.184 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
13:45:59.194 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@74923075, ORIGINAL=[Ljava.lang.String;@d596902, PIC_CLICK=[Ljava.lang.String;@7a6ef383]
13:45:59.195 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
13:45:59.703 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
13:45:59.703 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$8136e679 (priority:10000)
13:45:59.703 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$8136e679 (priority:10000)
13:45:59.877 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$8136e679 (priority:10000)
13:45:59.906 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
13:45:59.909 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:45:59.909 [Thread-27] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
13:45:59.909 [Thread-28] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
13:45:59.909 [Thread-29] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
13:46:00.122 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:46:00.133 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:46:00.133 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:46:00.138 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749534360123'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:46:00.138 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
13:46:00.138 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:46:00.139 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7b853e32
13:46:01.502 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534360123 shutting down.
13:46:01.502 [restartedMain] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534360123 paused.
13:46:01.503 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534360123 shutdown complete.
13:46:01.503 [restartedMain] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
13:46:01.508 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:46:01.508 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
13:46:01.508 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
13:46:01.508 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
13:46:01.514 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
13:46:01.518 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
13:46:01.637 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
13:49:44.699 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 48050 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
13:49:44.700 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
13:49:44.701 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
13:49:46.840 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
13:49:46.841 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:49:46.841 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
13:49:46.890 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:49:49.259 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
13:49:49.784 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
13:49:51.020 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
13:49:51.020 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
13:49:51.456 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
13:49:51.459 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
13:49:51.461 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
13:49:51.472 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@178f1e0b, ORIGINAL=[Ljava.lang.String;@7f857d9b, PIC_CLICK=[Ljava.lang.String;@10b15b33]
13:49:51.472 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
13:49:51.931 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
13:49:51.931 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$9435e0dc (priority:10000)
13:49:51.932 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$9435e0dc (priority:10000)
13:49:52.105 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$9435e0dc (priority:10000)
13:49:52.129 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
13:49:52.131 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:49:52.131 [Thread-25] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
13:49:52.132 [Thread-26] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
13:49:52.132 [Thread-27] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
13:49:52.316 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:49:52.323 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:49:52.323 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:49:52.327 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749534592316'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:49:52.327 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
13:49:52.327 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:49:52.328 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@11847d03
13:49:53.792 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534592316 shutting down.
13:49:53.792 [restartedMain] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534592316 paused.
13:49:53.793 [restartedMain] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534592316 shutdown complete.
13:49:53.793 [restartedMain] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
13:49:53.798 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:49:53.798 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
13:49:53.798 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
13:49:53.798 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
13:49:53.804 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
13:49:53.808 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
13:49:53.929 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
13:50:33.393 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 48089 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
13:50:33.394 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
13:50:33.396 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
13:50:35.769 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8080"]
13:50:35.769 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:50:35.770 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
13:50:35.820 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:50:38.218 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
13:50:38.739 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
13:50:40.037 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
13:50:40.038 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
13:50:40.490 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
13:50:40.494 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
13:50:40.496 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
13:50:40.507 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@25112257, ORIGINAL=[Ljava.lang.String;@661b5af4, PIC_CLICK=[Ljava.lang.String;@1c04f2e2]
13:50:40.507 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
13:50:40.969 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
13:50:40.969 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$b887777b (priority:10000)
13:50:40.969 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$b887777b (priority:10000)
13:50:41.152 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$b887777b (priority:10000)
13:50:41.175 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
13:50:41.179 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:50:41.180 [Thread-26] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
13:50:41.180 [Thread-27] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
13:50:41.180 [Thread-28] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
13:50:41.412 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:50:41.421 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:50:41.421 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:50:41.425 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749534641413'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:50:41.425 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
13:50:41.426 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:50:41.427 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@38998d77
13:50:43.226 [restartedMain] INFO  o.r.c.s.i.DSecretKeyServiceImpl - [cacheSecretData,59] - 初始化秘钥到 Redis 成功
13:50:45.589 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8080"]
13:50:45.988 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 12.841 seconds (JVM running for 13.843)
13:50:46.501 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:50:46.990 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534641413 started.
13:50:48.320 [restartedMain] INFO  c.r.RuoYiApplication - [main,29] - 数据平台 RuoYiApplication 服务启动成功
13:52:24.795 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534641413 paused.
13:52:24.821 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534641413 shutting down.
13:52:24.821 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534641413 paused.
13:52:24.822 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534641413 shutdown complete.
13:52:24.822 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
13:52:24.840 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:52:24.841 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
13:52:24.841 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
13:52:24.841 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
13:52:24.873 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
13:52:24.881 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
13:52:27.090 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 48132 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
13:52:27.091 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
13:52:27.092 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
13:52:29.368 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8888"]
13:52:29.369 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:52:29.369 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
13:52:29.417 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:52:31.784 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
13:52:32.315 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
13:52:33.561 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
13:52:33.561 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
13:52:33.994 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
13:52:33.996 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
13:52:33.998 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
13:52:34.008 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@2095b4f4, ORIGINAL=[Ljava.lang.String;@375648c7, PIC_CLICK=[Ljava.lang.String;@168add1c]
13:52:34.008 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
13:52:34.440 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
13:52:34.440 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$ddb0b794 (priority:10000)
13:52:34.440 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$ddb0b794 (priority:10000)
13:52:34.633 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$ddb0b794 (priority:10000)
13:52:34.654 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
13:52:34.658 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:52:34.658 [Thread-25] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
13:52:34.659 [Thread-26] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
13:52:34.659 [Thread-27] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
13:52:34.837 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:52:34.845 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:52:34.846 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:52:34.849 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749534754838'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:52:34.849 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
13:52:34.849 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:52:34.850 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5918f264
13:52:36.584 [restartedMain] INFO  o.r.c.s.i.DSecretKeyServiceImpl - [cacheSecretData,59] - 初始化秘钥到 Redis 成功
13:52:38.916 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8888"]
13:52:39.312 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 12.467 seconds (JVM running for 13.365)
13:52:39.462 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:52:40.310 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534754838 started.
13:52:41.731 [restartedMain] INFO  c.r.RuoYiApplication - [main,29] - 数据平台 RuoYiApplication 服务启动成功
13:56:13.688 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534754838 paused.
13:56:13.715 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534754838 shutting down.
13:56:13.715 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534754838 paused.
13:56:13.716 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534754838 shutdown complete.
13:56:13.716 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
13:56:13.738 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:56:13.738 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
13:56:13.738 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
13:56:13.739 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
13:56:13.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
13:56:13.802 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
13:56:17.951 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 48287 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
13:56:17.952 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
13:56:17.953 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
13:56:20.447 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8888"]
13:56:20.448 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:56:20.448 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
13:56:20.502 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:56:23.499 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
13:56:24.297 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
13:56:25.859 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
13:56:25.859 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
13:56:26.330 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
13:56:26.333 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
13:56:26.335 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
13:56:26.345 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@430fc4dc, ORIGINAL=[Ljava.lang.String;@64a69404, PIC_CLICK=[Ljava.lang.String;@7e76208e]
13:56:26.345 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
13:56:26.852 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
13:56:26.852 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$9ab1b8f4 (priority:10000)
13:56:26.852 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$9ab1b8f4 (priority:10000)
13:56:27.021 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$9ab1b8f4 (priority:10000)
13:56:27.044 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
13:56:27.044 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:56:27.045 [Thread-29] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
13:56:27.045 [Thread-30] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
13:56:27.048 [Thread-31] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
13:56:27.228 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:56:27.235 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:56:27.235 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:56:27.239 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749534987229'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:56:27.239 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
13:56:27.239 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:56:27.240 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5f75abf3
13:56:30.259 [restartedMain] INFO  o.r.c.s.i.DSecretKeyServiceImpl - [cacheSecretData,59] - 初始化秘钥到 Redis 成功
13:56:33.039 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8888"]
13:56:33.452 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 15.798 seconds (JVM running for 16.996)
13:56:33.992 [RMI TCP Connection(5)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:56:34.452 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534987229 started.
13:56:36.161 [restartedMain] INFO  c.r.RuoYiApplication - [main,29] - 数据平台 RuoYiApplication 服务启动成功
13:57:24.330 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534987229 paused.
13:57:24.355 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534987229 shutting down.
13:57:24.355 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534987229 paused.
13:57:24.356 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749534987229 shutdown complete.
13:57:24.356 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
13:57:24.369 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:57:24.369 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
13:57:24.369 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
13:57:24.369 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
13:57:24.408 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
13:57:24.426 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
13:57:27.821 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 48331 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
13:57:27.823 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
13:57:27.823 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
13:57:30.054 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8888"]
13:57:30.054 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:57:30.054 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
13:57:30.102 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:57:32.471 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
13:57:33.091 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
13:57:34.362 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
13:57:34.363 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
13:57:34.800 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
13:57:34.803 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
13:57:34.805 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
13:57:34.815 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@6d19f103, ORIGINAL=[Ljava.lang.String;@567af259, PIC_CLICK=[Ljava.lang.String;@3156415f]
13:57:34.815 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
13:57:35.298 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
13:57:35.299 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$cc50e912 (priority:10000)
13:57:35.299 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$cc50e912 (priority:10000)
13:57:35.520 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$cc50e912 (priority:10000)
13:57:35.545 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
13:57:35.548 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
13:57:35.548 [Thread-26] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
13:57:35.548 [Thread-27] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
13:57:35.549 [Thread-28] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
13:57:35.730 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:57:35.736 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:57:35.737 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:57:35.740 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749535055730'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:57:35.740 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
13:57:35.740 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:57:35.741 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@3dd7eecd
13:57:37.495 [restartedMain] INFO  o.r.c.s.i.DSecretKeyServiceImpl - [cacheSecretData,59] - 初始化秘钥到 Redis 成功
13:57:39.861 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8888"]
13:57:40.264 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 12.683 seconds (JVM running for 13.515)
13:57:40.747 [RMI TCP Connection(5)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:57:41.263 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535055730 started.
13:57:42.567 [restartedMain] INFO  c.r.RuoYiApplication - [main,29] - 数据平台 RuoYiApplication 服务启动成功
14:00:10.509 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535055730 paused.
14:00:10.526 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535055730 shutting down.
14:00:10.527 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535055730 paused.
14:00:10.527 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535055730 shutdown complete.
14:00:10.527 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
14:00:10.536 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:00:10.536 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
14:00:10.536 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
14:00:10.536 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
14:00:10.571 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
14:00:10.581 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
14:00:12.842 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_442 on macdeMacBook-Pro.local with PID 48378 (/Users/<USER>/zn_OA/RuoYi-Vue/ruoyi-admin/target/classes started by mac in /Users/<USER>/zn_OA)
14:00:12.844 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,676] - The following profiles are active: zw,interTest
14:00:12.844 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.0.Final
14:00:15.086 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8888"]
14:00:15.086 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:00:15.086 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.56]
14:00:15.136 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:00:17.506 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1} inited
14:00:18.035 [restartedMain] INFO  c.r.s.s.i.SysUserServiceImpl - [loadingDictCache,237] - =========》初始化字典映射到Reids完成《==========
14:00:19.282 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [initSysDictDataRef,74] - SysDictDataRefServiceImpl init 初始化级联缓存成功
14:00:19.283 [restartedMain] INFO  c.r.s.s.i.SysDictDataRefServiceImpl - [operationSysDictDataRef,104] - SysDictRefTask insertSysDictDataRef 录入并且缓存数据成功
14:00:19.713 [restartedMain] INFO  c.a.c.c.AjCaptchaServiceAutoConfiguration - [captchaService,33] - 自定义配置项：
AjCaptchaProperties{type=BLOCKPUZZLE, jigsaw='', picClick='', waterMark='ruoyi.vip', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='2', cacheNumber='1000', timingClear='180', cacheType=redis, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=360, reqGetMinuteLimit=30, reqCheckMinuteLimit=60, reqVerifyMinuteLimit=60}
14:00:19.716 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,52] - supported-captchaCache-service:[redis, local]
14:00:19.718 [restartedMain] INFO  c.a.c.s.i.CaptchaServiceFactory - [<clinit>,58] - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
14:00:19.727 [restartedMain] INFO  c.a.c.u.ImageUtils - [cacheImage,48] - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@3b2f4157, ORIGINAL=[Ljava.lang.String;@3287559, PIC_CLICK=[Ljava.lang.String;@61a446f0]
14:00:19.728 [restartedMain] INFO  c.a.c.s.i.BlockPuzzleCaptchaServiceImpl - [init,76] - --->>>初始化验证码底图<<<---blockPuzzle
14:00:20.172 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1552] - Found 1 Process Engine Configurators in total:
14:00:20.172 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [initConfigurators,1554] - class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$1ca863df (priority:10000)
14:00:20.173 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsBeforeInit,1564] - Executing beforeInit() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$1ca863df (priority:10000)
14:00:20.338 [restartedMain] INFO  o.a.e.i.c.ProcessEngineConfigurationImpl - [configuratorsAfterInit,1571] - Executing configure() of class org.activiti.spring.process.conf.ProcessExtensionsConfiguratorAutoConfiguration$$EnhancerBySpringCGLIB$$1ca863df (priority:10000)
14:00:20.361 [restartedMain] INFO  o.a.e.i.ProcessEngineImpl - [<init>,74] - ProcessEngine default created
14:00:20.361 [restartedMain] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [start,169] - Starting up the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:00:20.362 [Thread-26] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,49] - {} starting to acquire async jobs due
14:00:20.362 [Thread-25] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,45] - {} starting to acquire async jobs due
14:00:20.362 [Thread-27] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,51] - {} starting to reset expired jobs
14:00:20.544 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:00:20.550 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:00:20.551 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:00:20.553 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'RuoyiScheduler' with instanceId 'macdeMacBook-Pro.local1749535220545'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:00:20.554 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'RuoyiScheduler' initialized from an externally provided properties instance.
14:00:20.554 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:00:20.554 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@2afe4479
14:00:22.281 [restartedMain] INFO  o.r.c.s.i.DSecretKeyServiceImpl - [cacheSecretData,59] - 初始化秘钥到 Redis 成功
14:00:24.616 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8888"]
14:00:25.038 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 12.448 seconds (JVM running for 13.435)
14:00:25.224 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:00:26.037 [Quartz Scheduler [RuoyiScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535220545 started.
14:00:27.505 [restartedMain] INFO  c.r.RuoYiApplication - [main,29] - 数据平台 RuoYiApplication 服务启动成功
14:03:55.041 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535220545 paused.
14:03:55.093 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535220545 shutting down.
14:03:55.093 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535220545 paused.
14:03:55.095 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler RuoyiScheduler_$_macdeMacBook-Pro.local1749535220545 shutdown complete.
14:03:55.095 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,32] - ====关闭后台任务任务线程池====
14:03:55.109 [SpringApplicationShutdownHook] INFO  o.a.e.i.a.DefaultAsyncJobExecutor - [shutdown,208] - Shutting down the default async job executor [org.activiti.spring.SpringAsyncExecutor].
14:03:55.110 [activiti-acquire-timer-jobs] INFO  o.a.e.i.a.AcquireTimerJobsRunnable - [run,115] - {} stopped async job due acquisition
14:03:55.110 [activiti-acquire-async-jobs] INFO  o.a.e.i.a.AcquireAsyncJobsDueRunnable - [run,115] - {} stopped async job due acquisition
14:03:55.110 [activiti-reset-expired-jobs] INFO  o.a.e.i.a.ResetExpiredJobsRunnable - [run,99] - {} stopped resetting expired jobs
14:03:55.137 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2071] - {dataSource-1} closing ...
14:03:55.142 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2144] - {dataSource-1} closed
