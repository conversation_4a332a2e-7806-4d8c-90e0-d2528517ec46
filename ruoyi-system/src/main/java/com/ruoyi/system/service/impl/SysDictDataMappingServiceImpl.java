package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.domain.SysDictDataMapping;
import com.ruoyi.system.mapper.SysDictDataMapper;
import com.ruoyi.system.mapper.SysDictDataMappingMapper;
import com.ruoyi.system.service.ISysDictDataMappingService;
import com.ruoyi.system.util.DictMappingUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.validation.Validator;
import java.util.List;
import java.util.Map;

/**
 * 字典数据映射Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-03-28
 */
@Service
public class SysDictDataMappingServiceImpl implements ISysDictDataMappingService 
{
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);
    @Autowired
    private SysDictDataMappingMapper sysDictDataMappingMapper;
    @Autowired
    protected Validator validator;

    @Autowired
    private SysDictDataServiceImpl sysDictDataService;

    @Autowired
    private SysDictDataMapper sysDictDataMapper;
    /**
     * 查询字典数据映射
     * @param dictddmId 字典数据映射主键
     * @return 字典数据映射
     */
    @Override
    public SysDictDataMapping selectSysDictDataMappingByDictddmId(Long dictddmId)
    {
        return sysDictDataMappingMapper.selectSysDictDataMappingByDictddmId(dictddmId);
    }

    /**
     * 查询字典数据映射列表
     * 
     * @param sysDictDataMapping 字典数据映射
     * @return 字典数据映射
     */
    @Override
    public List<SysDictDataMapping> selectSysDictDataMappingList(SysDictDataMapping sysDictDataMapping)
    {
        return sysDictDataMappingMapper.selectSysDictDataMappingList(sysDictDataMapping);
    }

    @Override
    public List<SysDictData> selectByDictType(SysDictDataMapping sysDictDataMapping) {
        return sysDictDataMapper.selectDictDataByType(sysDictDataMapping.getDictType());
    }

    /**
     * 新增字典数据映射
     * 
     * @param sysDictDataMapping 字典数据映射
     * @return 结果
     */
    @Override
    public int insertSysDictDataMapping(SysDictDataMapping sysDictDataMapping)
    {

        sysDictDataMapping.setCreateTime(DateUtils.getNowDate());
        SysDictData sysDictData =  sysDictDataService.getByDictvalue(sysDictDataMapping.getDictValue(),sysDictDataMapping.getDictType());

        sysDictDataMapping.setDictdataId(sysDictData.getDictCode());
        int i = sysDictDataMappingMapper.insertSysDictDataMapping(sysDictDataMapping);
        if (i > 0) {
            DictMappingUtils.setDictMappingCache(sysDictDataMapping.getDictType(),sysDictDataMapping.getDictValue(),sysDictDataMapping.getPlatformNo(),sysDictDataMapping.getDictValueMapping());
        }
        return i;
    }

    /**
     * 修改字典数据映射
     * 修改完成后 更新缓存
     * @param sysDictDataMapping 字典数据映射
     * @return 结果
     */
    @Override
    public int updateSysDictDataMapping(SysDictDataMapping sysDictDataMapping,String operName)
    {
        sysDictDataMapping.setUpdateBr(operName);
        sysDictDataMapping.setUpdateTime(DateUtils.getNowDate());
        int i = sysDictDataMappingMapper.updateSysDictDataMapping(sysDictDataMapping);
        if( i > 0){
            DictMappingUtils.setDictMappingCache(sysDictDataMapping.getDictType(),sysDictDataMapping.getDictValue(),sysDictDataMapping.getPlatformNo(),sysDictDataMapping.getDictValueMapping());
        }
        return i;
    }

    /**
     * 批量删除字典数据映射
     * 
     * @param dictddmIds 需要删除的字典数据映射主键
     * @return 结果
     */
    @Override
    public int deleteSysDictDataMappingByDictddmIds(Long[] dictddmIds)
    {
        int i = sysDictDataMappingMapper.deleteSysDictDataMappingByDictddmIds(dictddmIds);
        if (i>0) {
            this.resetDictCache();
        }
        return i;
    }

    /**
     * 删除字典数据映射信息
     * 
     * @param dictddmId 字典数据映射主键
     * @return 结果
     */
    @Override
    public int deleteSysDictDataMappingByDictddmId(Long dictddmId)
    {
        int i = sysDictDataMappingMapper.deleteSysDictDataMappingByDictddmId(dictddmId);
        if(i>0){
            SysDictDataMapping sysDictDataMapping = sysDictDataMappingMapper.selectSysDictDataMappingByDictddmId(dictddmId);
            //删除映射
            DictMappingUtils.removeDictCache(sysDictDataMapping.getDictType(),sysDictDataMapping.getDictValueMapping(),sysDictDataMapping.getPlatformNo());
        }
        return i;

    }

    /**
     * 字典映射批量导入
     * @param mappingsList
     * @param isUpdateSupport
     * @param operName
     * @return
     */
    @Override
    public String importSysDictDataMapping(List<SysDictDataMapping> mappingsList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(mappingsList) || mappingsList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int mlIndex = 0;
        int updateNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysDictDataMapping sysDictDataMapping : mappingsList)
        {
            mlIndex++;
            try
            {
                // 验证是否存在这条数据
                SysDictDataMapping s = sysDictDataMappingMapper.selectMappingByAllCondition(sysDictDataMapping);
                if (StringUtils.isNull(s))
                {
                    BeanValidators.validateWithException(validator, sysDictDataMapping);
                    SysDictData sysDictData =  sysDictDataService.getByDictvalue(sysDictDataMapping.getDictValue(),sysDictDataMapping.getDictType());
                    sysDictDataMapping.setDictdataId(sysDictData.getDictCode());
                    sysDictDataMapping.setCreateBr(operName);
                    this.insertSysDictDataMapping(sysDictDataMapping);
                    successNum++;
                }
                else if (isUpdateSupport)
                {
                    if(sysDictDataMapping.getStatus().length()== 0 || StringUtils.isEmpty(sysDictDataMapping.getStatus())){
                        sysDictDataMapping.setStatus("0");
                    }
                    BeanValidators.validateWithException(validator, sysDictDataMapping);
                    sysDictDataMapping.setDictddmId(s.getDictddmId());
                    this.updateSysDictDataMapping(sysDictDataMapping,operName);
                    updateNum++;
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>第" +  mlIndex + "条数据已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>第"+  mlIndex +  " 条数据导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！应导入"+mappingsList.size()+"条，共导入 " + successNum + " 条数据，有 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！应导入"+mappingsList.size()+"条，共导入 " + successNum + " 条，更新"+updateNum+"条");
        }
        return successMsg.toString();
    }

    /**
     * 项目启动时，初始化字典到缓存
     */
    @PostConstruct
    public void init()
    {
        loadingDictCache();
    }

    /**
     * 加载字典缓存数据
     */
    @Override
    public void loadingDictCache()
    {
        SysDictDataMapping sysDictDataMapping = new SysDictDataMapping();
        sysDictDataMapping.setStatus("0");
        List<SysDictDataMapping> sysDictDataMappings = sysDictDataMappingMapper.selectSysDictDataMappingList(sysDictDataMapping);
        for (SysDictDataMapping dictDataMapping : sysDictDataMappings) {
            DictMappingUtils.setDictMappingCache(dictDataMapping.getDictType(),dictDataMapping.getDictValue(),dictDataMapping.getPlatformNo(),dictDataMapping.getDictValueMapping());
        }
        log.info("=========》初始化字典映射到Reids完成《==========");
    }
    /**
     * 清空字典缓存数据
     */
    @Override
    public void clearDictCache()
    {
        DictMappingUtils.clearDictCache();
    }

    /**
     * 重置字典缓存数据
     */
    @Override
    public void resetDictCache()
    {
        clearDictCache();
        loadingDictCache();
    }

    @Override
    public List<Map<String,Object>> getSelectData(String selectDiceType) {
        List<Map<String,Object>> selectData = sysDictDataMappingMapper.getSelectData(selectDiceType);
        return selectData;
    }

}
