package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.domain.SysBadDebtParams;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ISysBadDebtParamsService.java
 * @Description TODO
 * @createTime 2023年01月17日 10:07:00
 */
public interface ISysBadDebtParamsService {
    public SysBadDebtParams selectSysBadDebtParamsById(Long id);
    /**
     * 查询【请填写功能名称】列表
     *
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<SysBadDebtParams> selectSysBadDebtParamsList(SysBadDebtParams sysBadDebtParams);

    /**
     * 新增【请填写功能名称】
     *
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 结果
     */
    public int insertSysBadDebtParams(SysBadDebtParams sysBadDebtParams, LoginUser loginUser);

    /**
     * 修改【请填写功能名称】
     *
     * @param sysBadDebtParams 【请填写功能名称】
     * @return 结果
     */
    public int updateSysBadDebtParams(SysBadDebtParams sysBadDebtParams, LoginUser loginUser);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysBadDebtParamsByIds(Long[] ids);
    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSysBadDebtParamsById(Long id);

    /**
     * 校验新增的数据是否存在
     *
     * @param sysBadDebtParams 利润测算坏账参数
     * @return 结果
     */
    Boolean checkSysBadDebtParams(SysBadDebtParams sysBadDebtParams);
}
