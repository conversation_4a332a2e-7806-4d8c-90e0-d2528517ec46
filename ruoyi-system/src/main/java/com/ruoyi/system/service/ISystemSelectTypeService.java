package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.domain.SystemSelectType;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ISystemSelectTypeService 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SystemSelectType selectSystemSelectTypeById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param systemSelectType 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SystemSelectType> selectSystemSelectTypeList(SystemSelectType systemSelectType);

    /**
     * 新增【请填写功能名称】
     * 
     * @param systemSelectType 【请填写功能名称】
     * @return 结果
     */
    public Map<String, Object> insertSystemSelectType(SystemSelectType systemSelectType, LoginUser loginUser);

    /**
     * 修改【请填写功能名称】
     * 
     * @param systemSelectType 【请填写功能名称】
     * @return 结果
     */
    public int updateSystemSelectType(SystemSelectType systemSelectType, LoginUser loginUser);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSystemSelectTypeByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSystemSelectTypeById(Long id);
}
