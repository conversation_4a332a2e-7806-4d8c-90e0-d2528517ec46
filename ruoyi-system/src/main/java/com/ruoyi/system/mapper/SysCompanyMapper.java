package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.SysOperLog;
import com.ruoyi.system.domain.vo.CompanyBusinessTypeVo;
import com.ruoyi.system.domain.vo.CompanyLogVo;
import com.ruoyi.system.domain.vo.CompanyTypeVo;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 全量公司信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
public interface SysCompanyMapper
{
    /**
     * 查询全量公司信息
     *
     * @param id 全量公司信息主键
     * @return 全量公司信息
     */
    public SysCompanyVo selectSysCompanyById(Long id);

    /**
     * 查询全量公司信息列表
     *
     * @param sysCompany 全量公司信息
     * @return 全量公司信息集合
     */
    public List<SysCompanyVo> selectSysCompanyList(SysCompanyVo sysCompany);

    /**
     * 新增全量公司信息
     *
     * @param sysCompany 全量公司信息
     * @return 结果
     */
    public int insertSysCompany(SysCompanyVo sysCompany);

    /**
     * 修改全量公司信息
     *
     * @param sysCompany 全量公司信息
     * @return 结果
     */
    public int updateSysCompany(SysCompanyVo sysCompany);

    /**
     * 删除全量公司信息
     *
     * @param id 全量公司信息主键
     * @return 结果
     */
    public int deleteSysCompanyById(Long id);

    /**
     * 批量删除全量公司信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysCompanyByIds(Long[] ids);

    public List<SysCompany> selectSysCompanyListByCompanyName(SysCompany sysCompany);

    public List<SysCompany> selectSysCompanyListByCompanyShortName(SysCompany sysCompany);

    public List<SysCompany> selectSysCompanyListByCheck(SysCompany sysCompany);

    public List<CompanyTypeVo> getCompanyTypeList(SysDictData sysDictData);

    public List<CompanyBusinessTypeVo> getCompanyBusinessTypeList(SysDictData sysDictData);

    public List<CompanyLogVo> getCompanyLogVoList(SysOperLog sysOperLog);

    List<SysCompanyVo> selectSysCompanyListForAuthority(@Param("sysCompany") SysCompanyVo sysCompany, @Param("companyIdList") List<Long> companyIdList);

    Map<String, Object> selectOaProcessClassNum(@Param("id") Long id);

    Map<String, Object> getProjectNumByComId(@Param("id") Long id);

    List<SysCompanyVo> selectCompanyListByCompanyShortNames(Set<String> companyNames);

    List<SysCompanyVo> selectCompanyDataByTypeId(@Param("companyTypeId") List<String> companyTypeId);

    List<SysCompany> selectSysCompanyListByCompanyNameList(@Param("companyNameList") List<String> companyNameList);

    List<SysCompanyVo> selectSysCompanyListForAuthorityNew(@Param("sysCompany") SysCompanyVo sysCompany, @Param("companyTypeCode") List<Long> companyTypeCode, @Param("companyIdList") List<Long> companyIdList);

    List<SysCompany> selectSysCompanyListByCompanyIdList(@Param("companyIdList") List<Long> companyIdList);
}
