package com.ruoyi.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 授权相关 - DTO
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/6/19 15:57
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AgencyDTO {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 授权的第三方类型：0未定义1用户2岗位3代理给其他人授权4代理给其他人工作 */
    private String thirdType;

    /** 授权的第三方主键 */
    private Long thirdId;

    /** 授权的第三方主键昵称 */
    private String thirdNickName;

    //授权的用户状态
    private String thirdUserStatus;

    /** 模块，详见枚举AuthModuleEnum */
    private String moduleType;

    /** 角色类型，详见枚举AuthRoleEnum */
    private String roleType;

    /** 权限范围，详见枚举AuthPermissionEnum */
    private String permissionScope;

    /** 授权类型：0未定义1长期2有效期 */
    private String permissionType;

    /** 授权有效期，长期有效时设置为9999-12-31 23:59:59 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date permissionTime;

    /** 状态，0正常 1停用（失效） */
    private String status;

    /** 创建者id */
    private Long createId;

    /** 创建者userName */
    private String createBy;

    /** 创建者昵称 */
    private String createNickName;

    //创建者状态
    private String createUserStatus;

    /** 创建者主岗部门id */
    private Long createDeptId;

    /** 创建者主岗公司id */
    private Long createUnitId;

    /** 创建时间 */
    private Date createTime;

    /** 创建者id */
    private Long updateId;

    /** 更新者userName */
    private String updateBy;

    /** 创建者主岗部门id */
    private Long updateDeptId;

    /** 创建者主岗公司id */
    private Long updateUnitId;

    /** 更新时间 */
    private Date updateTime;

}
