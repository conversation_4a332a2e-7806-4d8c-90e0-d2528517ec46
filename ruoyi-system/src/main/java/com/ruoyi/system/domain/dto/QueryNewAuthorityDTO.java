package com.ruoyi.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 数据库查询出的结果集 - DTO
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/4/16 16:10
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class QueryNewAuthorityDTO {
    private static final long serialVersionUID = 1L;

    //主键，根据入参queryType不同，响应代表的含义不同。可能为项目id或者公司id
    private Long id;

    //同上，代表的是项目名称或者公司名称
    private String responseName;

    //简称，公司的话有简称，项目没有。看查询入参决定
    private String responseAbbreviationName;

    //部分功能返回 例如，财务项目管理返回项目类型
    private String responseType;

    //返回的授权用户Id
    private Long authorizedUserId;

    //授权用户是否是本人标识0-否 1-是
    private String authorizedUserIsCurrentUserFlag;

    //有权限的功能模块
    private String authorizedModuleFeature;

    //授权类型    用于页面判断是否是1-永久或者2-有效期
    private String permissionType;

    //授权有效期，长期有效时设置为9999-12-31 23:59:59
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date permissionTime;
}
