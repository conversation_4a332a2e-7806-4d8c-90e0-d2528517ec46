<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.financial.mapper.FinancialU8CBankaccountMapper">
    <resultMap type="FinancialU8CBankaccount" id="FinancialU8CBankaccountResult">
        <result property="accountId"    column="account_id"    />
        <result property="traderId"    column="trader_id"    />
        <result property="accountNumber"    column="account_number"    />
        <result property="pkBankaccbas"    column="pk_bankaccbas"    />
        <result property="u8cAccountName"    column="u8c_account_name"    />
        <result property="u8cAccount"    column="u8c_account"    />
        <result property="u8cAccountCode"    column="u8c_account_code"    />
        <result property="pkBankaccbasBefor"    column="pk_bankaccbas_befor"    />
        <result property="u8cAccountBefor"    column="u8c_account_befor"    />
        <result property="u8cAccountCodeBefor"    column="u8c_account_code_befor"    />
        <result property="u8cAccountNameBefor"    column="u8c_account_name_befor"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="state"    column="state"    />
        <result property="version"    column="version"    />
        <result property="updateReason"    column="update_reason"    />
        <result property="relevanceType"    column="relevance_type"    />
        <result property="bankOfDeposit"    column="bankOfDeposit"    />
    </resultMap>

    <sql id="selectFinancialU8CBankaccountVo">
        select account_id, trader_id, account_number, pk_bankaccbas, u8c_account_name, u8c_account, u8c_account_code, pk_bankaccbas_befor, u8c_account_befor, u8c_account_name_befor, u8c_account_code_befor, create_time, create_by, state, version, update_reason, relevance_type from financial_u8c_bankaccount
    </sql>

    <select id="selectFinancialU8CBankaccountList" parameterType="FinancialU8CBankaccount" resultMap="FinancialU8CBankaccountResult">
        <include refid="selectFinancialU8CBankaccountVo"/>
        <where>
            <if test="u8cAccountName != null  and u8cAccountName != ''"> and u8c_account_name like concat('%', #{u8cAccountName}, '%')</if>
            <if test="u8cAccount != null  and u8cAccount != ''"> and u8c_account = #{u8cAccount}</if>
            <if test="u8cAccountBefor != null  and u8cAccountBefor != ''"> and u8c_account_befor = #{u8cAccountBefor}</if>
            <if test="u8cAccountNameBefor != null  and u8cAccountNameBefor != ''"> and u8c_account_name_befor = #{u8cAccountNameBefor}</if>
            <if test="relevanceType != null  and relevanceType != ''"> and relevance_type = #{relevanceType}</if>
        </where>
    </select>

    <select id="selectFinancialU8CBankaccountByAccountId" parameterType="Long" resultMap="FinancialU8CBankaccountResult">
        <include refid="selectFinancialU8CBankaccountVo"/>
        where account_id = #{accountId}
    </select>

    <select id="selectAccountList" resultType="com.ruoyi.financial.domain.FinancialU8CBankaccount" parameterType="FinancialU8CBankaccount">
        SELECT
        tra.id traderId,
        bank.account_id accountId,
        bank.pk_bankaccbas pkBankaccbas,
        tra.bank_of_deposit bankOfDeposit,
        tra.account_number accountNumber,
        bank.u8c_account_name u8cAccountName,
        bank.u8c_account u8cAccount,
        bank.u8c_account_code u8cAccountCode,
        bank.relevance_type relevanceType,
        bank.create_time createTime,
        book.pk_glorgbook pkGlorgbook
        FROM
        oa_trader tra
        LEFT JOIN financial_u8c_bankaccount bank on bank.trader_id = tra.id and bank.state = '0'
        LEFT JOIN financial_u8c_account_glorgbook book on book.account_id = tra.account_id and book.state = '0'
        <where>
            tra.account_id = #{accountId}
            <if test="accountNumber != null  and accountNumber != ''"> and tra.account_number like concat('%', #{accountNumber}, '%')</if>
            <if test="bankOfDeposit != null  and bankOfDeposit != ''"> and tra.bank_of_deposit like concat('%', #{bankOfDeposit}, '%')</if>
            <if test="u8cAccount != null  and u8cAccount != ''"> and bank.u8c_account like concat('%', #{u8cAccount}, '%')</if>
            <if test="u8cAccountName != null  and u8cAccountName != ''"> and bank.u8c_account_name like concat('%', #{u8cAccountName}, '%')</if>
            <if test="relevanceType != null  and relevanceType != ''"> and bank.relevance_type = #{relevanceType}</if>
            <if test="traderId != null  and traderId != ''"> and bank.trader_id = #{traderId}</if>
        </where>
    </select>

    <select id="selectListToLastVersion" parameterType="FinancialU8CBankaccount" resultType="com.ruoyi.financial.domain.FinancialU8CBankaccount">
        SELECT
            account_id accountId,
            trader_id traderId,
            pk_bankaccbas pkBankaccbasBefor,
            u8c_account u8cAccountBefor,
            u8c_account_code u8cAccountCodeBefor,
            u8c_account_name u8cAccountNameBefor,
            max( version ) version
        FROM
            financial_u8c_bankaccount
        WHERE
            account_id = #{accountId}
          AND trader_id = #{traderId}
          AND state = '0'
    </select>

    <insert id="insertFinancialU8CBankaccount" parameterType="FinancialU8CBankaccount">
        insert into financial_u8c_bankaccount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">account_id,</if>
            <if test="traderId != null">trader_id,</if>
            <if test="accountNumber != null and accountNumber != ''">account_number,</if>
            <if test="pkBankaccbas != null and pkBankaccbas != ''">pk_bankaccbas,</if>
            <if test="u8cAccountName != null and u8cAccountName != ''">u8c_account_name,</if>
            <if test="u8cAccount != null and u8cAccount != ''">u8c_account,</if>
            <if test="u8cAccountCode != null and u8cAccountCode != ''">u8c_account_code,</if>
            <if test="pkBankaccbasBefor != null">pk_bankaccbas_befor,</if>
            <if test="u8cAccountCodeBefor != null">u8c_account_code_befor,</if>
            <if test="u8cAccountBefor != null">u8c_account_befor,</if>
            <if test="u8cAccountNameBefor != null">u8c_account_name_befor,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="version != null">version,</if>
            <if test="updateReason != null">update_reason,</if>
            <if test="relevanceType != null and relevanceType != ''">relevance_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">#{accountId},</if>
            <if test="traderId != null">#{traderId},</if>
            <if test="accountNumber != null and accountNumber != ''">#{accountNumber},</if>
            <if test="pkBankaccbas != null and pkBankaccbas != ''">#{pkBankaccbas},</if>
            <if test="u8cAccountName != null and u8cAccountName != ''">#{u8cAccountName},</if>
            <if test="u8cAccount != null and u8cAccount != ''">#{u8cAccount},</if>
            <if test="u8cAccountCode != null and u8cAccountCode != ''">#{u8cAccountCode},</if>
            <if test="pkBankaccbasBefor != null">#{pkBankaccbasBefor},</if>
            <if test="u8cAccountCodeBefor != null">#{u8cAccountCodeBefor},</if>
            <if test="u8cAccountBefor != null">#{u8cAccountBefor},</if>
            <if test="u8cAccountNameBefor != null">#{u8cAccountNameBefor},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="version != null">#{version},</if>
            <if test="updateReason != null">#{updateReason},</if>
            <if test="relevanceType != null and relevanceType != ''">#{relevanceType},</if>
        </trim>
    </insert>

    <update id="updateFinancialU8CBankaccount" parameterType="FinancialU8CBankaccount">
        update financial_u8c_bankaccount
        <trim prefix="SET" suffixOverrides=",">
            <if test="traderId != null">trader_id = #{traderId},</if>
            <if test="accountNumber != null and accountNumber != ''">account_number = #{accountNumber},</if>
            <if test="pkBankaccbas != null and pkBankaccbas != ''">pk_bankaccbas = #{pkBankaccbas},</if>
            <if test="u8cAccountName != null and u8cAccountName != ''">u8c_account_name = #{u8cAccountName},</if>
            <if test="u8cAccount != null and u8cAccount != ''">u8c_account = #{u8cAccount},</if>
            <if test="pkBankaccbasBefor != null">pk_bankaccbas_befor = #{pkBankaccbasBefor},</if>
            <if test="u8cAccountBefor != null">u8c_account_befor = #{u8cAccountBefor},</if>
            <if test="u8cAccountNameBefor != null">u8c_account_name_befor = #{u8cAccountNameBefor},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="version != null">version = #{version},</if>
            <if test="updateReason != null">update_reason = #{updateReason},</if>
            <if test="relevanceType != null and relevanceType != ''">relevance_type = #{relevanceType},</if>
        </trim>
        where account_id = #{accountId}
    </update>

    <delete id="deleteFinancialU8CBankaccountByAccountId" parameterType="Long">
        delete from financial_u8c_bankaccount where account_id = #{accountId}
    </delete>

    <delete id="deleteFinancialU8CBankaccountByAccountIds" parameterType="String">
        delete from financial_u8c_bankaccount where account_id in
        <foreach item="accountId" collection="array" open="(" separator="," close=")">
            #{accountId}
        </foreach>
    </delete>

    <update id="setState">
        update financial_u8c_bankaccount set state = #{state} where account_id = #{accountId} and trader_id = #{traderId} and version <![CDATA[ < ]]> #{version}
    </update>

    <select id="selectUpdate" parameterType="FinancialU8CBankaccount" resultType="com.ruoyi.financial.domain.FinancialU8CBankaccount">
        SELECT
            bank.account_number accountNumber,
            tra.bank_of_deposit bankOfDeposit,
            bank.u8c_account_befor u8cAccountBefor,
            bank.u8c_account_name_befor u8cAccountNameBefor,
            bank.u8c_account_code_befor u8cAccountCodeBefor,
            bank.u8c_account_name u8cAccountName,
            bank.u8c_account_code u8cAccountCode,
            bank.u8c_account u8cAccount,
            bank.update_reason updateReason,
            bank.create_time updateTime,
            us.nick_name updateBy
        FROM
            financial_u8c_bankaccount bank
                LEFT JOIN oa_trader tra ON tra.id = bank.trader_id
                LEFT JOIN sys_user us on us.user_name = bank.create_by
        where bank.account_id = #{accountId} and bank.trader_id = #{traderId} order by bank.create_time desc
    </select>

</mapper>
