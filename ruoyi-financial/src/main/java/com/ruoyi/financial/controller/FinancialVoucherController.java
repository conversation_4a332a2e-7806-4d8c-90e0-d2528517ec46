package com.ruoyi.financial.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.financial.controller.base.BaseCrudController;
import com.ruoyi.financial.domain.FinancialVoucher;
import com.ruoyi.financial.domain.FinancialVoucherDetails;
import com.ruoyi.financial.excel.VoucherExcel;
import com.ruoyi.financial.service.IFinancialUserService;
import com.ruoyi.financial.service.IFinancialVoucherService;
import com.ruoyi.financial.utils.VoucherExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.controller</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@Slf4j
@RestController
@RequestMapping("/voucher")
public class FinancialVoucherController extends BaseCrudController<IFinancialVoucherService, FinancialVoucher> {

    String[] defaultSummary = new String[]{"提现", "利息收入", "利息收入", "支付银行手续费", "报销销售人员的业务招待费", "购入固定资产", "支付货款"};

    @Autowired
    private IFinancialUserService userService;

    @Autowired
    private VoucherExcelUtils excelUtils;

    /**
     * 列表数据
     *
     * @return
     */
    @GetMapping
    public JsonResult list(@RequestParam Map<String, String> params) {
        return JsonResult.successful(this.service.listVoucher(params));
    }

    @GetMapping("code")
    public JsonResult loadCode(Integer accountSetsId, String word, Date currentAccountDate) {
        int code = this.service.loadCode(accountSetsId, word, currentAccountDate);
        return JsonResult.successful(code);
    }

    /**
     * 期末结转，结转金额
     *
     * @param years 期间年
     * @param month 期间月
     * @param code  科目编码
     * @return
     */
    @PostMapping("carryForwardMoney")
    public JsonResult carryForwardMoney(Integer accountSetsId, Integer years, Integer month, String[] code) {
        Map<String, FinancialVoucherDetails> detailsMap = this.service.carryForwardMoney(accountSetsId, years, month, code);
        return JsonResult.successful(detailsMap);
    }

    /**
     * 断号整理
     *
     * @param startTime  开始时间
     * @param endTime 结束时间
     * @return
     */
    @GetMapping("finishingOffNo")
    public JsonResult finishingOffNo(Integer accountSetsId, String startTime, String endTime) {
        this.service.finishingOffNo(accountSetsId, startTime, endTime);
        return JsonResult.successful();
    }

    /**
     * 批量删除
     *
     * @param checked 凭证ID
     * @return
     */
    @PostMapping("batchDelete")
    public JsonResult batchDelete(Integer accountSetsId, Integer[] checked, String startTime, String endTime) {
        this.service.batchDelete(accountSetsId, checked, startTime, endTime);
        return JsonResult.successful();
    }

    /**
     * 批量审核
     *
     * @param checked 凭证ID
     * @return
     */
    @PostMapping("audit")
    public JsonResult audit(Integer accountSetsId, Integer[] checked, String year, String month) {
        this.service.audit(accountSetsId, checked, getUserVo(accountSetsId), year, month);
        return JsonResult.successful();
    }

    /**
     * 批量审核
     *
     * @param checked 凭证ID
     * @return
     */
    @PostMapping("batchAudit")
    public JsonResult batchAudit(Integer accountSetsId, Integer[] checked, String startTime, String endTime) {
        this.service.batchAudit(accountSetsId, checked, getUserVo(accountSetsId), startTime, endTime);
        return JsonResult.successful();
    }

    /**
     * 批量反审核
     *
     * @param checked 凭证ID
     * @return
     */
    @PostMapping("cancelAudit")
    public JsonResult cancelAudit(Integer accountSetsId, Integer[] checked, String year, String month) {
        this.service.cancelAudit(accountSetsId, checked, getUserVo(accountSetsId), year, month);
        return JsonResult.successful();
    }

    /**
     * 上一条
     *
     * @param currentId 凭证ID
     * @return
     */
    @GetMapping("beforeId")
    public JsonResult beforeId(Integer currentId, Integer accountSetsId, String voucherDate) {
        Integer id = this.service.getBeforeId(accountSetsId, currentId, voucherDate);
        return JsonResult.successful(id);
    }

    /**
     * 下一条
     *
     * @param currentId 凭证ID
     * @return
     */
    @GetMapping("nextId")
    public JsonResult nextId(Integer currentId, Integer accountSetsId, String voucherDate) {
        Integer id = this.service.getNextId(accountSetsId, currentId, voucherDate);
        return JsonResult.successful(id);
    }

    @Override
    public JsonResult save(@RequestBody FinancialVoucher entity) {
        JsonResult result = super.save(entity);
        result.setData(entity);
        return result;
    }

    @GetMapping("summary")
    public JsonResult summary(Integer accountSetsId) {
        List<String> summary = this.service.getTopSummary(accountSetsId);
        summary = summary.stream().map(String::trim).collect(Collectors.toList());
        for (String s : defaultSummary) {
            if (!summary.contains(s)) {
                summary.add(s);
            }
        }
        return JsonResult.successful(summary);
    }

    @PostMapping("/import")
    public JsonResult importVoucher(@RequestParam("file") MultipartFile multipartFile, Integer accountSetsId) {
        try {
            List<FinancialVoucher> voucherList = excelUtils.readExcel(multipartFile.getOriginalFilename(), multipartFile.getInputStream(), accountSetsId);
            Date date = this.service.importVoucher(voucherList, getAccountSets(accountSetsId));
            return JsonResult.successful(date);
        } catch (ServiceException e) {
            e.printStackTrace();
            return JsonResult.failure(e.getMessage());
        } catch (Exception e) {
            log.error("导入失败", e);
            throw new ServiceException("导入失败~", e);
        }
    }

    /**
     * 作废凭证
     *
     * @param id
     * @return
     */
    @PostMapping("/valid/{id:\\d+}")
    public JsonResult valid(@PathVariable Long id, String accountSetsId) {
        try {
            QueryWrapper qw = Wrappers.query();
            qw.eq("id", id);
            if(null != accountSetsId){
                qw.eq("account_sets_id", accountSetsId);
            }
            service.validVoucher(qw);
            return JsonResult.successful();
        } catch (ServiceException se) {
            log.error("删除失败！", se);
            return JsonResult.failure(se.getMessage());
        } catch (Exception e) {
            log.error("删除失败！", e);
            return JsonResult.failure("删除失败！");
        }
    }

    /**
     * <AUTHOR>
     * @Description 凭证号重新排序
     * @Date 2024/3/27 16:46
     * @Param [accountSetsId, voucherYearMonth]
     * @return void
     **/
    @PostMapping("/reorderCode")
    public void reorderCode(Integer accountSetsId, String voucherYearMonth){
        this.service.reorderCode(accountSetsId, voucherYearMonth);
    }

    /**
     * 账簿总账导出
     */
    @Log(title = "凭证列表导出", businessType = BusinessType.EXPORT)
    @PostMapping("exportVoucher")
    public void exportVoucher(HttpServletResponse response, @RequestParam Map<String, String> params) {
        List<VoucherExcel> voucherExcels = this.service.exportVoucher(params);
        ExcelUtil<VoucherExcel> excelUtil = new ExcelUtil<>(VoucherExcel.class);
        excelUtil.exportExcel(response, voucherExcels, "凭证信息");
    }
}
