package com.ruoyi.financial.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.financial.controller.base.BaseCrudController;
import com.ruoyi.financial.domain.FinancialAccountSets;
import com.ruoyi.financial.domain.FinancialUserAccountSets;
import com.ruoyi.financial.domain.FinancialVoucher;
import com.ruoyi.financial.service.IFinancialAccountSetsService;
import com.ruoyi.financial.service.IFinancialUserAccountSetsService;
import com.ruoyi.financial.service.IFinancialUserService;
import com.ruoyi.financial.service.IFinancialVoucherService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.controller</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年07月30日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
@RestController
@RequestMapping("/account-sets")
@AllArgsConstructor
@Slf4j
public class FinancialAccountSetsController extends BaseCrudController<IFinancialAccountSetsService, FinancialAccountSets>{

    private IFinancialUserAccountSetsService userAccountSetsService;

    private IFinancialVoucherService voucherService;

    private IFinancialUserService userService;

    private IFinancialAccountSetsService financialAccountSetsService;

    @Override
    public JsonResult list(@RequestParam Map<String, String> params) {
        // 1.从中间表查出当前用户的所有账套
        LambdaQueryWrapper<FinancialUserAccountSets> uasQw = Wrappers.lambdaQuery();
        uasQw.eq(FinancialUserAccountSets::getUserId, getUserId());
        List<FinancialUserAccountSets> userAccountSetsList = userAccountSetsService.list(uasQw);

        if (userAccountSetsList.size() > 0) {
            // 2.获取账套信息
            QueryWrapper<FinancialAccountSets> asQw = Wrappers.query();
            IntStream intStream = userAccountSetsList.stream().mapToInt(FinancialUserAccountSets::getAccountSetsId);
            asQw.in("id", intStream.boxed().collect(Collectors.toList()));
            asQw.allEq(params);
            //this.getPageList(params, userAccountSetsService);

            Map<Integer, List<FinancialUserAccountSets>> userAccountSetsMap = userAccountSetsList.stream().collect(Collectors.groupingBy(FinancialUserAccountSets::getAccountSetsId));
            List<FinancialAccountSets> accountSetsList = this.service.list(asQw);
            accountSetsList.stream().forEach(accountSets->{
                List<FinancialUserAccountSets> userAccountSets = userAccountSetsMap.get(accountSets.getId());
                Map<String, List<FinancialUserAccountSets>> roleTypeMap = userAccountSets.stream().collect(Collectors.groupingBy(FinancialUserAccountSets::getRoleType));
                if(roleTypeMap.containsKey("accounting")){
                    List<Long> accountings = roleTypeMap.get("accounting").stream().map(FinancialUserAccountSets::getUserId).collect(Collectors.toList());
                    accountSets.setAccountings(accountings);
                }
                if(roleTypeMap.containsKey("accountingManager")){
                    List<Long> accountingManagers = roleTypeMap.get("accountingManager").stream().map(FinancialUserAccountSets::getUserId).collect(Collectors.toList());
                    accountSets.setAccountingManagers(accountingManagers);
                }
            });

            return JsonResult.successful(accountSetsList);
        }
        return JsonResult.successful();
    }

    @Override
    public JsonResult save(@RequestBody FinancialAccountSets entity) {
        try {
            entity.setCurrentAccountDate(entity.getEnableDate());
            entity.setCreatorId(getUserId());
            service.save(entity);
            return JsonResult.successful(entity);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("账套创建失败！", e);
            return JsonResult.failure(e.getMessage());
        }
    }

    @Override
    public JsonResult update(@RequestBody FinancialAccountSets entity) {
        entity.setCreatorId(getUserId());
        JsonResult result = super.update(entity);
        return result;
    }

    @GetMapping("updateUserRole")
    public JsonResult updateUserRole(Integer accountSetsId, Integer id, String role) {
        this.service.updateUserRole(accountSetsId, id, role);
        return JsonResult.successful();
    }

    @GetMapping("removeUser/{uid}")
    public JsonResult removeUser(@PathVariable Integer uid, Integer accountSetsId) {
        this.service.removeUser(accountSetsId, uid);
        return JsonResult.successful();
    }

    /**
     * 更新科目编码设置
     *
     * @param encoding
     * @param newEncoding
     * @return
     */
    @PostMapping("updateEncode")
    public JsonResult updateEncode(@RequestParam Integer accountSetsId, @RequestParam String encoding, @RequestParam String newEncoding) {
        if (!encoding.equals(newEncoding)) {
            this.service.updateEncode(accountSetsId, encoding, newEncoding);
        }
        return JsonResult.successful();
    }

    @PostMapping("handOver")
    public JsonResult handOver(@RequestParam String code, @RequestParam Long userId, Integer accountSetsId) {
        this.service.handOver(accountSetsId, getUserId(), userId);
        return JsonResult.successful();
    }

    /**
     * 检查账套是否已有凭证
     *
     * @param asid 账套 ID
     * @return
     */
    @GetMapping("checkUse")
    public JsonResult checkUse(@RequestParam Integer asid) {
        LambdaQueryWrapper<FinancialVoucher> qw = Wrappers.lambdaQuery();
        qw.eq(FinancialVoucher::getAccountSetsId, asid);
        return JsonResult.successful(this.voucherService.count(qw) > 0);
    }

    /**
     * 屏蔽默认的删除操作
     *
     * @param id
     * @return
     */
    @Override
    public JsonResult delete(Long id, String accountSetsId) {
        return JsonResult.failure();
    }

    /**
     * 带验证码删除
     *
     * @param id
     * @param smsCode
     * @return
     */
    @DeleteMapping("/{id:\\d+}/{smsCode}")
    public JsonResult deleteAccountSets(@PathVariable Long id, @PathVariable String smsCode) {
        JsonResult rs = super.delete(id, null);
        return rs;
    }

    @GetMapping("/getAccountSetsList/{companyId}")
    public List<Map<String,Object>> getAccountList(@PathVariable Long companyId){
       return financialAccountSetsService.queryAccountListByComId(companyId);
    }

    @GetMapping("/getAllAccountSetsList")
    public List<Map<String,Object>> getAllAccountList(){
        return financialAccountSetsService.getAllList();
    }

    @GetMapping("/queryRoleTypeUser")
    public JsonResult queryRoleTypeUser(@RequestParam List<Long> companyIds){
        return JsonResult.successful(financialAccountSetsService.queryRoleTypeUser(companyIds));
    }
}
