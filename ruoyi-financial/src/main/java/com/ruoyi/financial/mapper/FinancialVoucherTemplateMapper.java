package com.ruoyi.financial.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ruoyi.financial.domain.FinancialVoucherTemplate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FinancialVoucherTemplateMapper extends BaseMapper<FinancialVoucherTemplate> {
    int batchInsert(@Param("list") List<FinancialVoucherTemplate> list);

    List<FinancialVoucherTemplate> selectVoucherTemplate(@Param(Constants.WRAPPER) Wrapper wrapper);
}