package com.ruoyi.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.financial.domain.FinancialReportTemplate;
import com.ruoyi.financial.po.ReportTemplatePo;
import com.ruoyi.financial.vo.ReportDataVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.service</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月05日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface IFinancialReportTemplateService extends IService<FinancialReportTemplate> {


    int batchInsert(List<FinancialReportTemplate> list);

    Map<Integer, ReportDataVo> view(Integer accountSetsId, Long id, Date startTime, Date endTime);

    /**
     * <AUTHOR>
     * @Description 导出报表数据
     * @Date 2023/6/28 16:00
     * @Param [response, accountSetsId, reportId, startTime, endTime]
     * @return void
     **/
    void exportDetail(HttpServletResponse response, Integer accountSetsId, Long reportId, Date startTime, Date endTime);

    /**
     * <AUTHOR>
     * @Description 查询模板列表项信息
     * @Date 2024/10/24 15:00
     * @Param [templateKey, title]
     * @return java.util.List<com.ruoyi.financial.po.ReportTemplatePo>
     **/
    public List<ReportTemplatePo> selectReportItemInfo(String templateKey, String title);
}
