import { floatMultiply, addTotal, formatNumberWithCommas } from "@/utils";
export default {
  data() {
    return {
      tableShow: {
        fullName: false,
        tenThousand: false,
        detail: false,
      },
      columns: [],
      columnsInitY: [
        {
          label: "公司/部门/用户",
          prop: "shortName",
          fixed: "left",
          minWidth: "200px",
        },
        { label: "年度", prop: "year", minWidth: "100px" },
        { label: "一季度(元)", prop: "q1TotalIndexY", minWidth: "150px" },
        { label: "二季度(元)", prop: "q2TotalIndexY", minWidth: "150px" },
        { label: "三季度(元)", prop: "q3TotalIndexY", minWidth: "150px" },
        { label: "四季度(元)", prop: "q4TotalIndexY", minWidth: "150px" },
        { label: "合计(元)", prop: "totalY", minWidth: "150px" },
        { label: "状态", key: "status", minWidth: "100px" },
        { label: "操作", key: "operate", minWidth: "220px" },
      ],
      columnsInit: [
        {
          label: "公司/部门/用户",
          prop: "shortName",
          fixed: "left",
          minWidth: "200px",
        },
        { label: "年度", prop: "year", minWidth: "100px" },
        { label: "一季度(万元)", prop: "q1TotalIndex", minWidth: "150px" },
        { label: "二季度(万元)", prop: "q2TotalIndex", minWidth: "150px" },
        { label: "三季度(万元)", prop: "q3TotalIndex", minWidth: "150px" },
        { label: "四季度(万元)", prop: "q4TotalIndex", minWidth: "150px" },
        { label: "合计(万元)", prop: "total", minWidth: "150px" },
        { label: "状态", key: "status", minWidth: "100px" },
        { label: "操作", key: "operate", minWidth: "220px" },
      ],
      columnsDetailY: [
        {
          label: "公司/部门/用户",
          prop: "shortName",
          fixed: "left",
          minWidth: "200px",
        },
        { label: "年度", prop: "year", minWidth: "100px" },
        {
          label: "        一季度\n总项目指标(元)",
          prop: "q1TotalIndexY",
          minWidth: "120px",
        },
        {
          label: "        一季度\n分配项目指标(元)",
          prop: "q1DistributionIndexY",
          minWidth: "130px",
        },
        {
          label: "        一季度\n自拓项目指标(元)",
          prop: "q1ExtensionIndexY",
          minWidth: "130px",
        },
        {
          label: "        一季度\n自拓银行指标(家)",
          prop: "q1ExtensionBank",
          minWidth: "130px",
        },
        {
          label: "        二季度\n总项目指标(元)",
          prop: "q2TotalIndexY",
          minWidth: "120px",
        },
        {
          label: "        二季度\n分配项目指标(元)",
          prop: "q2DistributionIndexY",
          minWidth: "130px",
        },
        {
          label: "        二季度\n自拓项目指标(元)",
          prop: "q2ExtensionIndexY",
          minWidth: "130px",
        },
        {
          label: "        二季度\n自拓银行指标(家)",
          prop: "q2ExtensionBank",
          minWidth: "130px",
        },
        {
          label: "        三季度\n总项目指标(元)",
          prop: "q3TotalIndexY",
          minWidth: "120px",
        },
        {
          label: "        三季度\n分配项目指标(元)",
          prop: "q3DistributionIndexY",
          minWidth: "130px",
        },
        {
          label: "        三季度\n自拓项目指标(元)",
          prop: "q3ExtensionIndexY",
          minWidth: "130px",
        },
        {
          label: "        三季度\n自拓银行指标(家)",
          prop: "q3ExtensionBank",
          minWidth: "130px",
        },
        {
          label: "        四季度\n总项目指标(元)",
          prop: "q4TotalIndexY",
          minWidth: "120px",
        },
        {
          label: "        四季度\n分配项目指标(元)",
          prop: "q4DistributionIndexY",
          minWidth: "130px",
        },
        {
          label: "        四季度\n自拓项目指标(元)",
          prop: "q4ExtensionIndexY",
          minWidth: "130px",
        },
        {
          label: "        四季度\n自拓银行指标(家)",
          prop: "q4ExtensionBank",
          minWidth: "130px",
        },
        { label: "合计(元)", prop: "totalY", minWidth: "150px" },
        { label: "状态", key: "status", minWidth: "100px" },
        { label: "操作", key: "operate", minWidth: "250px" },
      ],
      columnsDetail: [
        {
          label: "公司/部门/用户",
          prop: "shortName",
          fixed: "left",
          minWidth: "200px",
        },
        { label: "年度", prop: "year", minWidth: "100px" },
        {
          label: "        一季度\n总项目指标(万元)",
          prop: "q1TotalIndex",
          minWidth: "130px",
        },
        {
          label: "        一季度\n分配项目指标(万元)",
          prop: "q1DistributionIndex",
          minWidth: "140px",
        },
        {
          label: "        一季度\n自拓项目指标(万元)",
          prop: "q1ExtensionIndex",
          minWidth: "140px",
        },
        {
          label: "        一季度\n自拓银行指标(家)",
          prop: "q1ExtensionBank",
          minWidth: "130px",
        },
        {
          label: "        二季度\n总项目指标(万元)",
          prop: "q2TotalIndex",
          minWidth: "140px",
        },
        {
          label: "        二季度\n分配项目指标(万元)",
          prop: "q2DistributionIndex",
          minWidth: "140px",
        },
        {
          label: "        二季度\n自拓项目指标(万元)",
          prop: "q2ExtensionIndex",
          minWidth: "140px",
        },
        {
          label: "        二季度\n自拓银行指标(家)",
          prop: "q2ExtensionBank",
          minWidth: "130px",
        },
        {
          label: "        三季度\n总项目指标(万元)",
          prop: "q3TotalIndex",
          minWidth: "130px",
        },
        {
          label: "        三季度\n分配项目指标(万元)",
          prop: "q3DistributionIndex",
          minWidth: "140px",
        },
        {
          label: "        三季度\n自拓项目指标(万元)",
          prop: "q3ExtensionIndex",
          minWidth: "140px",
        },
        {
          label: "        三季度\n自拓银行指标(家)",
          prop: "q3ExtensionBank",
          minWidth: "140px",
        },
        {
          label: "        四季度\n总项目指标(万元)",
          prop: "q4TotalIndex",
          minWidth: "130px",
        },
        {
          label: "        四季度\n分配项目指标(万元)",
          prop: "q4DistributionIndex",
          minWidth: "140px",
        },
        {
          label: "        四季度\n自拓项目指标(万元)",
          prop: "q4ExtensionIndex",
          minWidth: "140px",
        },
        {
          label: "        四季度\n自拓银行指标(家)",
          prop: "q4ExtensionBank",
          minWidth: "130px",
        },
        { label: "合计(万元)", prop: "total", minWidth: "150px" },
        { label: "状态", key: "status", minWidth: "100px" },
        { label: "操作", key: "operate", minWidth: "250px" },
      ],
    };
  },
  methods: {
    changeDetail() {
      if (this.tableShow.detail && this.tableShow.tenThousand) {
        this.columns = this.columnsDetail;
      } else if (this.tableShow.detail && !this.tableShow.tenThousand) {
        this.columns = this.columnsDetailY;
      } else if (!this.tableShow.detail && this.tableShow.tenThousand) {
        this.columns = this.columnsInit;
      } else if (!this.tableShow.detail && !this.tableShow.tenThousand) {
        this.columns = this.columnsInitY;
      }
      this.columns[0].prop = this.tableShow.fullName ? "name" : "shortName";
      this.columns[0].minWidth = this.tableShow.fullName ? "250px" : "180px";
    },
    addConfigList(data) {
      const addData = [
        "q1DistributionIndex",
        "q1ExtensionIndex",
        "q1TotalIndex",
        "q2DistributionIndex",
        "q2ExtensionIndex",
        "q2TotalIndex",
        "q3DistributionIndex",
        "q3ExtensionIndex",
        "q3TotalIndex",
        "q4DistributionIndex",
        "q4ExtensionIndex",
        "q4TotalIndex",
      ];
      if (this.queryParams.deptName || this.queryParams.nickName) {
        data.forEach((item) => {
          addData.forEach((item1) => {
            item[item1 + "Y"] = formatNumberWithCommas(
              floatMultiply(item[item1], 10000)
            );
            item[item1 + "Y1"] = floatMultiply(item[item1], 10000);
          });
          item.total = addTotal(item, [
            "q1TotalIndex",
            "q2TotalIndex",
            "q3TotalIndex",
            "q4TotalIndex",
          ]);
          item.totalY = formatNumberWithCommas(
            addTotal(item, [
              "q1TotalIndexY1",
              "q2TotalIndexY1",
              "q3TotalIndexY1",
              "q4TotalIndexY1",
            ])
          );
          item.isSelect = false; //默认为不选中
          if(item.type==2){
            if(item.name.indexOf('>')==-1){
              item.name=`${item.companyShortName} > ${item.name}`
            }
          }
        });
      } else {
        let add = function (data) {
          data.forEach((item) => {
            addData.forEach((item1) => {
              item[item1 + "Y"] = formatNumberWithCommas(
                floatMultiply(item[item1], 10000)
              );
              item[item1 + "Y1"] = floatMultiply(item[item1], 10000);
            });
            item.total = addTotal(item, [
              "q1TotalIndex",
              "q2TotalIndex",
              "q3TotalIndex",
              "q4TotalIndex",
            ]);
            item.totalY = formatNumberWithCommas(
              addTotal(item, [
                "q1TotalIndexY1",
                "q2TotalIndexY1",
                "q3TotalIndexY1",
                "q4TotalIndexY1",
              ])
            );
            item.parentId = item.parentId || 0;
            item.isSelect = false; //默认为不选中
            item.disabled = ["2", "3"].includes(item.state);
            if (item.annualPlanVoList) {
              add(item.annualPlanVoList);
            }
            if(item.type==2){
              if(item.name.indexOf('>')==-1){
                item.name=`${item.companyShortName} > ${item.name}`
              }
            }
          });
        };
        add(data);
      }
      return data;
    },
  },
};
