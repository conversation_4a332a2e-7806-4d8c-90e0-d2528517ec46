import {
  updateProcessChannelBusiness,
  getByProcessIdChannelBusiness,
} from "@/api/badSystem/channelBusiness";
export default {
  data() {
    return {
      channelBusinessDetailId: "",
    };
  },
  methods: {
    async initChannelBusiness() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const { data } = await getByProcessIdChannelBusiness(id);
      this.channelBusinessDetailId = data.id;
    },
    async passChannelBusiness() {
      if (this.flowData.flowInfo.oaModuleType == "blChannelBusinessReconciliation") {
        const params = {
          id: this.channelBusinessDetailId,
          status: "3",
        };
        await updateProcessChannelBusiness(params);
      }
    },
  },
};
