import OverTime from "@/views/oaWork/updateProcessForm/components/overTime/Form.vue";
import { overtimeProcessId, unpassWorkOvertime } from "@/api/checkWork/overTime";
export default {
  components: {
    OverTime,
  },
  data() {
    return {};
  },
  methods: {
    async initCheckWorkOverTime() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const { data } = await overtimeProcessId(id);
      if(data)this.checkWork.checkWorkOverTime=data;
    },
    async unpassChekcWorkOverTime(){
      if (Object.keys(this.checkWork.checkWorkOverTime).length) {
       await unpassWorkOvertime(this.checkWork.checkWorkOverTime.id);
     }
   },
  },
};
