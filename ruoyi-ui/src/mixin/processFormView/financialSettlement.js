import {
  updateProcessSettlementFinancial,
  getByProcessIdFinancialSettlement,
} from "@/api/badSystem/financialSettlement";
export default {
  data() {
    return {
      financialSettlementDetailId: "",
    };
  },
  methods: {
    async initFinancialSettlement() {
      const id = this.$route.query.businessId || this.$route.query.oid;
      const {data} = await getByProcessIdFinancialSettlement(id);
      this.financialSettlementDetailId = data.id;
    },
    async unpassFinancialSettlement() {
      if (this.followData.oaModuleType == "blFinancialSettlement") {
        const params = {
          id: this.financialSettlementDetailId,
          status: "5",
          processId:''
        };
        await updateProcessSettlementFinancial(params);
      }
    },
  },
};
