<template>
  <div class="p-20 overflow-y-auto" style="height: calc(100vh - 100px)">
    <el-form
      :model="queryForms"
      :rules="rules"
      ref="form"
      label-width="130px"
      :disabled="Boolean(title.indexOf('查看') != -1)"
    >
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryForms.templateName"
          style="width: 240px"
        ></el-input>
      </el-form-item>
      <el-form-item label="模板说明" prop="templateExplain">
        <el-input
          v-model="queryForms.templateExplain"
          type="textarea"
          :autosize="{ minRows: 3 }"
          style="width: 600px"
        ></el-input>
      </el-form-item>
      <el-form-item label="部门配置项填写:" prop="slaveList" class="tables">
      </el-form-item>
      <el-table
        :data="queryForms.slaveList"
        :row-class-name="rowClassName"
        class="tables"
      >
        <el-table-column label="部门名称">
          <template #default="{ row, $index }">
            <el-form-item
              :rules="[
                {
                  validator: validateValue,
                  trigger: 'change',
                },
              ]"
              :prop="`slaveList.${$index}.deptName`"
              class="relative top-3 mb-7"
            >
              <el-input
                @input="changeInput(row, 'deptName')"
                v-model.trim="row.deptName"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          label="绩效工资占比(%)"
          prop="achievementWagesProportion"
          width="300px"
        >
        </el-table-column>
        <el-table-column label="分配占比(%)" width="300px">
          <template #default="{ row }">
            <el-input
              style="width: 150px"
              @input="changeInput(row, 'distributionProportion')"
              v-model="row.distributionProportion"
              :disabled="title.indexOf('查看') != -1"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="自拓占比(%)" width="300px">
          <template #default="{ row }">
            <el-input
              style="width: 150px"
              @input="changeInput(row, 'extensionProportion')"
              v-model="row.extensionProportion"
              :disabled="title.indexOf('查看') != -1"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column width="100px">
          <template #default="{ row }">
            <i
              @click="deletTableItem(row)"
              class="el-icon-circle-close cursor-pointer text-2xl"
              style="color: rgb(2, 167, 240)"
            ></i>
          </template>
        </el-table-column>
      </el-table>
      <div class="flex justify-end mt-2">
        <el-button
          @click="addRow"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          >增加一条</el-button
        >
      </div>
    </el-form>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
      >
        <el-button
          @click="onSave"
          v-show="title.indexOf('查看') == -1"
          type="primary"
          >保存</el-button
        >

        <el-button @click="cancel">取消</el-button>
      </div>
    </InBody>
  </div>
</template>

<script>
import config from "./config";
import { floatAdd, decimal } from "@/utils";
import {
  configTemplate,
  updateConfigTemplate,
  getTemplate,
} from "@/api/perAppraisal/templateList";
import XEUtils from "xe-utils";
export default {
  name: "TemplateListDetail",
  data() {
    return {
      ...config,
      id: this.$route.params.id,
      title: this.$route.query.title,
      queryForms: {
        slaveList: [],
      },
    };
  },

  created() {
    this.init();
  },
  methods: {
    init() {
      this.getconfigList();
    },

    async getconfigList() {
      if (this.title.indexOf("新增") == -1) {
        const { data } = await getTemplate(this.id);
        this.queryForms = data;
      }
    },
    rowClassName({ row, rowIndex }) {
      row.xh = rowIndex + 1;
    },
    validateValue(rule, value, callback) {
      const regex = /^[\u4E00-\u9FA5A-Za-z]+$/;
      const chineseRegex = /^[\u4e00-\u9fa5]{0,20}$/;
      const englishRegex = /^[a-zA-Z]{0,60}$/;
      if (!value) {
        callback(new Error("请输入部门名称"));
      } else if (!regex.test(value)) {
        callback(new Error("部门名称只可输入汉字或英文字母大小写"));
      } else if (!(chineseRegex.test(value) || englishRegex.test(value))) {
        callback(new Error("部门名称最多可输入20个汉字/60个英文字母"));
      } else {
        callback();
      }
    },
    changeInput(value, itemValue) {
      if (itemValue != "deptName") {
        value[itemValue] = decimal(value[itemValue], 2);
        this.getItemTotal(value, itemValue);
      }
    },

    getItemTotal(value, itemValue) {
      value.achievementWagesProportion = floatAdd(
        value.distributionProportion,
        value.extensionProportion
      );
    },
    addRow() {
      this.queryForms.slaveList.push({
        deptName: "",
        achievementWagesProportion: 0,
        distributionProportion: 0,
        extensionProportion: 0,
      });
    },
    deletTableItem(row) {
      if (this.title.indexOf("查看") != -1) return;
      this.queryForms.slaveList.splice(row.xh - 1, 1);
    },

    async onSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.id.indexOf("addTemplate") != -1) {
            await configTemplate(this.queryForms);
          } else {
            await updateConfigTemplate(this.queryForms);
          }
          this.$message.success("保存成功");
          this.cancel();
        }
      });
    },
    cancel() {
      const obj = { path: "/perAppraisalOther/templateList" };
      this.$tab.closeOpenPage(obj);
    },
  },
};
</script>
<style lang="scss" scoped>
.tables {
  ::v-deep .el-form-item__content {
    margin-left: 0px !important;
  }
}
</style>
