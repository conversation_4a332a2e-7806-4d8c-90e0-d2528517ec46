<template>
  <div class="p-5">
    <div class="mb-5">
      配置项目或公司中可支持的业务类型。一个项目或公司可同时支持多个业务类型。项目经理可新增、编辑业务类型
    </div>
        <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="88px"
      style="margin-bottom: 10px"
      @submit.native.prevent
    >
      <el-form-item label="业务类型" prop="dictLabel" >
        <el-input
          v-model.trim="queryParams.dictLabel"
          placeholder="请输入业务类型"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
          
        />
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-button
      class="mb-5"
      type="primary"
      plain
      icon="el-icon-plus"
      size="mini"
      @click="handleAdd"
      v-hasPermi="['businessType:addEdit']"
      > 新增业务类型</el-button
    >
    <MyTable
      :columns="columns"
      :source="configList"
      :showIndex="true"
      :queryParams="queryParams"
    >
      <template #dictLabel="{ record }">
        <div
          class="border border-solid rounded px-2 inline-block h-6 leading-6"
          style="border-color: #cccccc; backgroundColor: #f2f2f2"
        >
          {{ record.dictLabel }}
        </div>
      </template>
      <template #operate="{ record }">
        <el-button type="text" @click="onView(record)">查看详情</el-button>
        <el-button
          type="text"
          v-hasPermi="['businessType:addEdit']"
          @click="onEdit(record)"
          >编辑</el-button
        >
        <el-button type="text" @click="editRecord(record)">更新记录</el-button>
      </template>
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog
      :form="form"
      :title="formTitle"
      v-model="open"
      @on-submit-success="getList"
    />
    <DetailDialogView
      :form="form"
      title="查看业务类型详情"
      v-model="openView"
    />
     <RecordDialog
      :params="paramsRecord"
      v-model="openRecord"
      :columnsRecord="columnsRecord"
      :formColumns="formColumns"
      title="更新记录"
    />
  </div>
</template>

<script>

import { getCompanyBusinessTypeList } from "@/api/businessInformation/companyInformation";
import DetailDialog from "./components/DetailDialog.vue";
import DetailDialogView from "./components/DetailDialogView.vue";
import config from "./components/config";

export default {
  name: "BusinessType",
  components: { DetailDialog, DetailDialogView,  },
  provide: {
    contrast: {dictLabel:'公司类型',dictSort:'顺序号',remark:'备注说明',},//对比表格映射的字段
  },
  data() {
    return {
      ...config,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      configList: [],
      total: 0,
      form: {},
      formTitle: "",
      open: false,
      openView: false,
      openRecord: false,
      paramsRecord: {},
      columnsRecord: Object.freeze([
        { label: "时间", prop: "operTime" },
        { label: "操作人", prop: "nickName" },
        { label: "操作类型", key: "businessType" },
        // { label: "操作", key: "operate" },
      ]),
      formColumns: Object.freeze([
       {
          label: "操作人",
          prop: "operName",
          type: "input",
          placeholder: "请输入操作人",
        },
        {
          label: "时间",
          prop: "operTime",
          type: "datePicker",
          dateType: "daterange",
          placeholder: "请选择时间",
        },
      ]),
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    async getList() {
      const { rows, total } = await getCompanyBusinessTypeList(this.queryParams);
      this.configList = rows;
      this.total = total;
    },
      handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handleAdd() {
      this.formTitle = "新增业务类型";
      this.form = {};
      this.open = true;
    },
    onView(value) {
      this.form = { ...value };
      this.openView = true;
    },
    onEdit(value) {
      this.formTitle = "编辑业务类型";
      this.form = { ...value };
      this.open = true;
    },
    editRecord(value) {
      this.paramsRecord = {
        relationId: value.dictCode,
        functionNode: "COMPANYBUSINESSTYPE",
        title: "COMPANY",
      };
      this.openRecord = true;
    },
     resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
