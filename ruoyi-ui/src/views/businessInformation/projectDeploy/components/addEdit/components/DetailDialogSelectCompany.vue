<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="550px"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div style="max-height: 72vh">
          <div class="flex">
            <el-input
              v-model.trim="companyName"
              clearable
              placeholder="请输入公司名称搜索"
              class="w-1/2 mr-3"
              @keyup.enter.native="handleQuery"
              @clear="handleQuery"
            ></el-input>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </div>
          <el-button
            type="text"
            @click="submit({ companyShortName: currentTableType, id: 0 })"
            class="my-2"
          >
            {{ currentTableType }}
          </el-button>
          <MyTable :columns="columnsCompany" :source="configList">
            <template #companyName="{ record }">
              <div
                @click="submit(record)"
                class="truncate ... cursor-pointer"
                style="color: #46a6ff"
              >
                {{ record.companyName || '' }}
              </div>
            </template>
          </MyTable>
          <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        /> -->
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button @click="innerValue = false" class="ml-3">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import XEUtils from "xe-utils";
import config from "../../config";

export default {
  mixins: [vModelMixin],
  props: {
    tableList: {
      type: Array,
      required: true,
      default: () => [],
    },
    currentTableType: {
      type: [String],
      required: true,
      default: "",
    },
  },
  data() {
    return {
      ...config,
      // queryParams: {
      //   pageNum: 1,
      //   pageSize: 20,
      // },
      // total: 0,
      companyName: "",
      configList: [],
      configListInit: [],
      tableTitle: "",
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleOpen() {
      this.companyName = "";
      this.configList = this.tableList
      console.log(this.configList,'-');
      this.configListInit = this.tableList
    },
    handleQuery() {
      // this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.companyName = "";
      this.configList = XEUtils.clone(this.configListInit, true);
    },
    getList() {
      if (this.companyName) {
        this.configList = this.configListInit.filter(
          (item) => item.companyName.indexOf(this.companyName)!=-1||item.companyShortName.indexOf(this.companyName)!=-1
        );
      } else {
        this.configList = XEUtils.clone(this.configListInit, true);
      }
    },
    submit(value) {
      this.$emit("submitCompany", value);
      this.innerValue = false;
    },
  },
};
</script>
