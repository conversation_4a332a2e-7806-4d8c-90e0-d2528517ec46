<template>
  <div class="app-container">
    <!-- 上传组件的div-->
    <div v-show="showProgress"   style="text-align: center" v-hasPermi="['yybbsc:iData:import']">

      <div>
        <span style="">上传文件：</span>
        <el-upload
          ref="uploadExcel"
          :limit="1"
          accept=".xlsx"
          :headers="upload.headers"
          :action="upload.url"
          :before-remove="beforeRemove"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          :file-list="upFileList"
          :data="uploadData"
          drag>


          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            <span style="font-size: 13px;color: #cccccc">请选择《.xlsx》文件导入</span><br>
            <span
              style="font-size: 13px;color: #cccccc">如上传错误，请删除文件后重新选择后上传！如果重复上传，将覆盖</span><br>
            <span style="font-size: 13px;color: #cccccc">旧数据</span><br>
          </div>

        </el-upload>
      </div>
      <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
      <br/>
      <!-- 关闭按钮-->
      <!--      <div style="text-align: center">-->
      <!--        <el-button size="mini" type="primary" @click="submitFileForm">上传</el-button>-->
      <!--        <el-button size="mini" @click="goBackFirst()">取消</el-button>-->
      <!--      </div>-->
      <div v-if="buttonFlag === false "  style="width: 100%;height:60px; line-height: 45px;">
        <el-button size="mini" type="info" disabled>上传</el-button>
        <el-button size="mini" @click="goBackFirst()">取消</el-button>
      </div>
      <div v-if="buttonFlag === true "  style="width: 100%;height:60px; line-height: 45px;">
        <el-button size="mini" type="primary" @click="handleImport">上传</el-button>
        <el-button size="mini" @click="goBackFirst()">取消</el-button>
      </div>
    </div>
    <div v-show="showProgressSubsequent"  >
      <el-table
        v-loading="showProgressSubsequent"
        element-loading-text="上传中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgb(255, 255, 255)"
        style="width: 100%"/>

    </div>



  </div>
</template>

<script>

import {getToken} from '@/utils/auth'
import cust from "@/views/cwxmgl/cust";

export default {
  props: {
    queryFromFirst: {
      type: Object,
      default: function () {
        let obj = {
          productNo: null,
        };
        return obj;
      },
      required: false
    }
  },


  // name: "Day",
  data() {
    return {
      //向上一个页面返回的结果-告诉第二个页面展示和隐藏
      CRes: false,
      //按钮是否置灰 false-灰色，true-蓝
      buttonFlag: false,
      fileNames:null,
      //文件集合
      upFileList: [],
      //请求接口的参数
      // queryParams: {
      //   productNo: null,
      // },
      showProgress : true,
      showProgressSubsequent : false,
      //弹窗相关
      dialogOpen: false,
      //上传携带的参数
      uploadData: {
        reconDate: null,
        productNo: null,
      },
      // 上传完成之后接口响应的对象
      // respObj: null,
      // respObj: {
      //   //页面展示的
      //   reconDate: null,
      //   loanAmt: null,
      //   actPrintAmt: null,
      //   actIntAmt: null,
      //   jtFrAmt: null,
      //   zbFrAmt: null,
      //   compensatePrintAmt: null,
      //   compensateIntAmt: null,
      //   compensateOintAmt: null,
      //   compensateTotalAmt: null,
      //   compensateRepayPrintAmt: null,
      //   compensateRepayTotalAmt: null,
      //   //页面不展示，也要往库里插的
      //   productNo: null,
      //   intAmt: null,
      //   ointAmt: null,
      //   flAmt: null,
      //   advDefineAmt: null,
      //   deductAmt: null,
      //   reduceAmt: null,
      //   fzAmt: null,
      //   fundBalanceAmt: null,
      //   userBalanceAmt: null,
      //   accumProfitAmt: null,
      // },
      // 用户导入参数
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/iData/importData"
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询每日运营统计列表 */
    getList() {
      this.loading = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0) {
        this.$message.error('上传文件大小不能为 0 MB');
        return false;
      } else if (fileSize < maxSize) {
        this.uploadData = {}; //上传携带的参数名
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      if (response.code === 500) {
        this.$modal.msgError(response.msg);
        this.showProgress= true;
        this.showProgressSubsequent= false;
        this.submitFileFormError();
      }
      if (response.code === 501) {
        this.dialogOpen = true;
        this.$refs.uploadExcel.clearFiles();
        this.showProgress= true;
        this.showProgressSubsequent= false;
        this.submitFileFormError();
      }
      if (response.code === 200) {
        this.$refs.uploadExcel.clearFiles();
        this.submitFileForm(response);
        this.showProgress= true;
        this.showProgressSubsequent= false;
      }
    },
    // 提交上传文件
    handleImport() {
      let file = this.$refs.uploadExcel.uploadFiles[0];
      if (file!=undefined){
        this.showProgress= false;
        this.showProgressSubsequent= true;
        this.$refs.uploadExcel.submit();
      }else {
        this.showProgress= true;
        this.showProgressSubsequent= false;
      }

    },
    beforeRemove(file, upFileList) {
      this.buttonFlag = false;
    },

    //点击文件列表中已上传文件进行下载
    // handlePreview(file) {
    //   this.buttonFlag = true;
    // },
    handleChange(file, fileList) {


      if (file !== null) {
        this.buttonFlag = true;
      }
    },
    submitFileForm(response) {

      this.$refs.uploadExcel.clearFiles();
      this.$modal.msgSuccess("上传成功"+response.data+"条数据！");
      this.CRes = true;
      this.$emit('emitToParent', this.CRes);
        this.buttonFlag = false;

    },
    submitFileFormError() {
      this.$refs.uploadExcel.clearFiles();

      this.CRes = true;
      this.$emit('emitToParent', this.CRes);
      this.buttonFlag = false;
    },
    dialogCloseHandle() {
      this.dialogOpen = false;
      this.buttonFlag = false;
    },
    goBackFirst() {
      this.buttonFlag = false;
      this.$refs.uploadExcel.clearFiles();
      this.CRes = true;
      this.$emit('emitToParent', this.CRes);
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/iData/importTemplate', {
      }, `模板_${new Date().getTime()}.xlsx`)
    },



    buttonFlagIsFalse() {
      this.buttonFlag = false;
    }


  }
};
</script>
<style>
.spancol {
  color: #333333;
  font-weight: bold;
  font-size: 15px;
  display: inline-block;
  margin-left: 0;
  /* padding-top:10px; */
}

</style>
