<template>
  <div class="app-container">
    <div v-show="dayShow">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <span class="grid-content2">本功能用于为财务部门自动生成富邦业务《收入预测报表》</span><br/>
        <span class="grid-content1">以2020年06月为例，需先导入2020-06对账单->再导入2020-06还款表->再导入2020-06代偿明细表</span>
      </el-col>
    </el-row>

      <div style="width: 40%;height:60px; line-height: 45px;" v-hasPermi="['yybbsc:forecast:importStaOfAcc','yybbsc:forecast:importRepay','yybbsc:forecast:importCompensatory','yybbsc:forecast:export']">
        <el-button size="mini" type="primary" @click="importStaOfAcc()" v-hasPermi="['yybbsc:forecast:importStaOfAcc']">导入对账单</el-button>
        <el-button size="mini" type="primary" @click="importRepay()" v-hasPermi="['yybbsc:forecast:importRepay']">导入还款表</el-button>
        <el-button size="mini" type="primary" @click="importCompensatory()" v-hasPermi="['yybbsc:forecast:importCompensatory']">导入代偿明细表</el-button>
        <el-button
        style="margin-left:20px"
        type="warning"
        plain
        icon="el-icon-download"
        size="mini"
        @click="handleExport"
        v-if="this.monthDataList.length !== 0"
        v-hasPermi="['yybbsc:forecast:export']"
      >导出
      </el-button><br>
      </div>

<!--    <div>-->
<!--      <span class="spancol" v-hasPermi="['yybbsc:day:list']">每日运营报表 </span><el-button-->
<!--      style="margin-left:20px"-->
<!--      type="warning"-->
<!--      plain-->
<!--      icon="el-icon-download"-->
<!--      size="mini"-->
<!--      @click="handleExport"-->
<!--      v-hasPermi="['yybbsc:day:export']"-->
<!--    >导出-->
<!--    </el-button><br>-->
<!--    </div>-->

      <div v-hasPermi="['yybbsc:forecast:list']">
        <div v-hasPermi="['yybbsc:forecast:list']">
        <span style="font-size: 14px;">放款月份</span>
        <el-select v-model="monthSelectData.firstData" @change="queryListByLoanMonth" filterable :data="monthDataList" placeholder="请选择" clearable :style="{width: '10%',marginLeft: '20px'}">
          <template slot-scope="scope">
            <el-option v-for="(item, index) in monthDataList" :label="item" :key="index"
                       :value="item" :disabled="item.disabled"></el-option>
          </template>
        </el-select>
      </div>

        <br/>


        <div style="margin-bottom: 8px">
          <span style="font-size: 14px;">按产品统计</span>
        </div>
    <el-table v-loading="loading" :data="stsIncomeForecastLoanVoList" :span-method="overDetailBodyDetailList_v2panMethod" border>
      <el-table-column label="放款月份" align="center" prop="loanMonth" width="100" />
      <el-table-column label="放款金额" align="center" prop="loanAmt" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.loanAmt)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品类型" align="center" prop="productType" width="180px" />
      <el-table-column label="期数" align="center" prop="phase" width="180px" />
      <el-table-column label="还款月份" align="center" prop="repaymentMonth" width="180px" />
      <el-table-column label="还款本金" align="center" prop="repaymentPrintAmount" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.repaymentPrintAmount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="还款利息" align="center" prop="repaymentIntAmount" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.repaymentIntAmount)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="还款罚息" align="center" prop="repaymentOintAmt" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.repaymentOintAmt)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="还款复利" align="center" prop="repaymentFlAmt" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.repaymentFlAmt)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="提前还款违约金" align="center" prop="advDefineAmt" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.advDefineAmt)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动抵扣金额" align="center" prop="deductAmt" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.deductAmt)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="红线减免金额" align="center" prop="reduceAmt" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.reduceAmt)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="月末余额" align="center" prop="monthEndBalanceAmt" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney(scope.row.monthEndBalanceAmt)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="技术服务费" align="center" prop="technicalServiceFee" width="180px" >
        <template slot-scope="scope">
          <span>{{formaterMoney1(scope.row.technicalServiceFee)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="实收息费" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="借条分润" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="中保分润" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="资金成本" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="中保收入" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="净收入" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="代偿总金额" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="净收入-代偿总金额" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="平均余额" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="FA" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="保证金成本" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
      <el-table-column label="净收益" align="center" width="180px" >
        <span>-</span>
      </el-table-column>
    </el-table>

        <br/>
        <div style="margin-bottom: 8px">
          <span style="font-size: 14px;">按还款月统计</span>
        </div>

        <el-table v-loading="loading" :data="stsIncomeForecastRepayMonthVoList" :span-method="objectSpanMethod" border>
          <el-table-column label="放款月份" align="center" prop="loanMonth" width="100" />
          <el-table-column label="放款金额" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="产品类型" align="center" prop="productType" width="180px" />
          <el-table-column label="期数" align="center" prop="phase" width="180px" />
          <el-table-column label="还款月份" align="center" prop="repaymentMonth" width="180px" />
          <el-table-column label="还款本金" align="center" prop="repaymentPrintAmount" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.repaymentPrintAmount)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="还款利息" align="center" prop="repaymentIntAmount" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.repaymentIntAmount)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="还款罚息" align="center" prop="repaymentOintAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.repaymentOintAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="还款复利" align="center" prop="repaymentFlAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.repaymentFlAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="提前还款违约金" align="center" prop="advDefineAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.advDefineAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="活动抵扣金额" align="center" prop="deductAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.deductAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="红线减免金额" align="center" prop="reduceAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.reduceAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="月末余额" align="center" prop="monthEndBalanceAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.monthEndBalanceAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="技术服务费" align="center" prop="technicalServiceFee" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney1(scope.row.technicalServiceFee)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="实收息费" align="center" prop="paidInterestFee" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.paidInterestFee)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="借条分润" align="center" prop="jtFrAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.jtFrAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="中保分润" align="center" prop="zbFrAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.zbFrAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="资金成本" align="center" prop="costOfCapital" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.costOfCapital)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="中保收入" align="center" prop="zbIncome" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.zbIncome)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="净收入" align="center" prop="netIncome" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.netIncome)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿总金额" align="center" prop="compensateTotalAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.compensateTotalAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="净收入-代偿总金额" align="center" prop="netIncomeCompensateTotalAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.netIncomeCompensateTotalAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="平均余额" align="center" prop="avgBalance" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.avgBalance)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="FA" align="center" prop="fa" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.fa)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="保证金成本" align="center" prop="marginCost" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.marginCost)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="净收益" align="center" prop="incomeNet" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.incomeNet)}}</span>
            </template>
          </el-table-column>
        </el-table>

        <br/>

        <div style="margin-bottom: 8px">
          <span style="font-size: 14px;">合计</span>
        </div>
        <el-table v-loading="loading" :data="stsIncomeForecastTotalVoList" border>
          <el-table-column label="放款月份" align="center" prop="loanMonth" width="100" />
          <el-table-column label="放款金额" align="center" prop="loanAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.loanAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="产品类型" align="center" prop="productType" width="180px" />
          <el-table-column label="期数" align="center" prop="phase" width="180px" />
          <el-table-column label="还款月份" align="center" prop="repaymentMonth" width="180px" />
          <el-table-column label="还款本金" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="还款利息" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="还款罚息" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="还款复利" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="提前还款违约金" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="活动抵扣金额" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="红线减免金额" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="月末余额" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="技术服务费" align="center" prop="technicalServiceFee" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney1(scope.row.technicalServiceFee)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="实收息费" align="center" prop="paidInterestFee" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.paidInterestFee)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="借条分润" align="center" prop="jtFrAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.jtFrAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="中保分润" align="center" prop="zbFrAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.zbFrAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="资金成本" align="center" prop="costOfCapital" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.costOfCapital)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="中保收入" align="center" prop="zbIncome" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.zbIncome)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="净收入" align="center" prop="netIncome" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.netIncome)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="代偿总金额" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="净收入-代偿总金额" align="center" prop="netIncomeCompensateTotalAmt" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.netIncomeCompensateTotalAmt)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="平均余额" align="center" width="180px" >
            <span>-</span>
          </el-table-column>
          <el-table-column label="FA" align="center" prop="fa" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.fa)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="保证金成本" align="center" prop="marginCost" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.marginCost)}}</span>
            </template>
          </el-table-column>
          <el-table-column label="净收益" align="center" prop="incomeNet" width="180px" >
            <template slot-scope="scope">
              <span>{{formaterMoney(scope.row.incomeNet)}}</span>
            </template>
          </el-table-column>
        </el-table>

      </div>

    </div>

    <import-sta-of-acc :style="{width: '100%'}" ref="bPage" :queryFromFirst="querySecondObj" @emitToParent='getBRes' v-show="detailShow"></import-sta-of-acc>
    <import-repay :style="{width: '100%'}" ref="cPage" :queryFromFirst="querySecondObj" @emitToParent='getCRes' v-show="checkShow"></import-repay>
    <import-compensatory :style="{width: '100%'}" ref="dPage" :queryFromFirst="querySecondObj" @emitToParent='getDRes' v-show="compensatoryShow"></import-compensatory>
  </div>
</template>

<script>
import importStaOfAcc from "@/views/yybbsc/importStaOfAcc/index"
import importRepay from "@/views/yybbsc/importRepay/index"
import importCompensatory from "@/views/yybbsc/importCompensatory/index"
import { loanMonthFirst, list } from '@/api/yybbsc/forecast'

export default {
  components:{importStaOfAcc, importRepay, importCompensatory},
  name: "Forecast",
  data() {
    return {
      //单元格合并
      merageArr1: [],
      meragePos1: 0,
      merageArr2: [],
      meragePos2: 0,
      //选择框自动选中第一个
      monthSelectData: {
        firstData: ''
      },
      //控制本页面是否显示第二页-importStaOfAcc
      dayShow: true,
      //控制本页面是否显示第三页-importRepay内容
      checkShow: false,
      //控制本页面是否显示第四页-importRepay
      compensatoryShow: false,
      //控制导入页面是否显示
      detailShow: false,
      //第二个和第三个页面-importStaOfAcc、importRepay、importCompensatory传参对象
      querySecondObj: {
        productNo: null,
      },
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      //月份下拉框参数
      monthDataList: [],
      // 渲染的几个参数
      stsIncomeForecastTotalVoList: [],
      stsIncomeForecastLoanVoList: [],
      stsIncomeForecastRepayMonthVoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        loanMonth: null,
      },
    };
  },
  created() {
    // this.queryParams.productNo = '0'
    this.getList();
  },
  methods: {
    /** 下拉框的改变事件 */
    queryListByLoanMonth(loanMonth) {
      this.loading = true;
      if (loanMonth !== undefined) {
        if (loanMonth.length === 7) {
          this.queryParams.loanMonth = loanMonth.substring(0,4) + '-0' + loanMonth.substring(5,6);
        }
        if (loanMonth.length === 8) {
          this.queryParams.loanMonth = loanMonth.substring(0,4) + '-' + loanMonth.substring(5,7);
        }
      }
      list(this.queryParams).then(response => {
        this.stsIncomeForecastLoanVoList = response.stsIncomeForecastLoanVoList;
        this.merage(this.stsIncomeForecastLoanVoList);
        this.stsIncomeForecastRepayMonthVoList = response.stsIncomeForecastRepayMonthVoList;
        this.stsIncomeForecastTotalVo = response.stsIncomeForecastTotalVo;
        //el-table渲染必须用list装，
        let a = [];
          if (response.stsIncomeForecastTotalVo !== null) {
            a.push(response.stsIncomeForecastTotalVo);
          }
        this.stsIncomeForecastTotalVoList = a;
      });
      this.loading = false;
    },
    /** 第四页返回的值 */
    getDRes(data) {
      if (data === true) {
        this.getList();
        this.compensatoryShow = false;
        this.dayShow = true;
      }
    },
    /** 第三页返回的值 */
    getCRes(data) {
      if (data === true) {
        this.getList();
        this.checkShow = false;
        this.dayShow = true;
      }
    },
    /** 第二页返回的值 */
    getBRes(data) {
      if (data === true) {
        this.getList();
        this.detailShow = false;
        this.dayShow = true;
      }
    },
    /** 进入页面首次查询列表 */
    getList() {
      this.loading = true;
      /** 首次查月份 */
      loanMonthFirst().then(response => {
        this.monthDataList = response;
        this.monthSelectData.firstData = this.monthDataList[0];
        let str = this.monthSelectData.firstData;
          if (str !== undefined) {
            if (str.length === 7) {
              this.queryParams.loanMonth = str.substring(0, 4) + '-0' + str.substring(5, 6);
            }
            if (str.length === 8) {
              this.queryParams.loanMonth = str.substring(0, 4) + '-' + str.substring(5, 7);
            }
          }
        // this.queryParams.loanMonth = str.substring(0,4) + '-' + str.substring(5,6);
        // console.log('111', this.queryParams.loanMonth)

        /** 首次查具体数据 */
        list(this.queryParams).then(response => {
          this.stsIncomeForecastLoanVoList = response.stsIncomeForecastLoanVoList;
          this.merage(this.stsIncomeForecastLoanVoList);
          this.stsIncomeForecastRepayMonthVoList = response.stsIncomeForecastRepayMonthVoList;
          //el-table渲染必须用list装，
          let a = [];
          if (response.stsIncomeForecastTotalVo !== null) {
            a.push(response.stsIncomeForecastTotalVo);
          }
          this.stsIncomeForecastTotalVoList = a;
        });


      });
      // console.log('222', this.queryParams.loanMonth)
      // list(this.queryParams).then(response => {
      //   this.stsIncomeForecastLoanVoList = response.stsIncomeForecastLoanVoList;
      //   this.stsIncomeForecastRepayMonthVoList = response.stsIncomeForecastRepayMonthVoList;
      //   //el-table渲染必须用list装，
      //   let a = [];
      //   a.push(response.stsIncomeForecastTotalVo);
      //   this.stsIncomeForecastTotalVoList = a;
      // });
      this.loading = false;
    },
    /** 导入对账单 */
    importStaOfAcc() {
      this.dayShow = false;
      this.detailShow = true;
      this.$refs.bPage.buttonFlagIsFalse();
    },
    /** 导入还款表 */
    importRepay() {
      this.dayShow = false;
      this.checkShow = true;
      this.$refs.cPage.buttonFlagIsFalse();
    },
    /** 导入代偿明细表 */
    importCompensatory() {
      this.dayShow = false;
      this.compensatoryShow = true;
      this.$refs.dPage.buttonFlagIsFalse();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yybbsc/forecast/export', {
        ...this.queryParams
      }, `收入预测报表_${new Date().getTime()}.xlsx`)
    },



    // 要合并的数组的方法
    merage(tableData) {
      this.merageInit();
      for (var i = 0; i < tableData.length; i++) {
        if (i === 0) {
          // 第一行正常显示 必须存在
          this.merageArr1.push(1);
          this.meragePos1 = 0;
          this.merageArr2.push(1);
          this.meragePos2 = 0;
        } else {
          // 判断当前元素与上一个元素是否相同 根据是否合并的id
          if ((typeof tableData[i].loanMonth !== 'undefined' && tableData[i].loanMonth != null && tableData[i].loanMonth !== '') && tableData[i].loanMonth === tableData[i - 1].loanMonth) {
            this.merageArr1[this.meragePos1] += 1;
            this.merageArr1.push(0);
          } else {
            this.merageArr1.push(1);
            this.meragePos1 = i;
          }

          if ((typeof tableData[i].loanId !== 'undefined' && tableData[i].loanId != null && tableData[i].loanId !== '') && tableData[i].loanId === tableData[i - 1].loanId) {
            this.merageArr2[this.meragePos2] += 1;
            this.merageArr2.push(0);
          } else {
            this.merageArr2.push(1);
            this.meragePos2 = i;
          }
        }
      }
    },
    merageInit() {
      this.merageArr1 = [];
      this.meragePos1 = 0;
      this.merageArr2 = [];
      this.meragePos2 = 0;
    },
    //合并单元格
    overDetailBodyDetailList_v2panMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.merageArr1[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }

      if (columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
        const _row = this.merageArr2[rowIndex];
        const _col = _row > 0 ? 1 : 0; //如果被合并了_row=0则它这个列需要取消
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 3) {
        if (rowIndex === 0) {
          return {
            rowspan: 99999,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    /** 格式化金额 */
    formaterMoney(data) {
      if (data === 0) return '0.00'
      if (!data) return ''
      if (data === '-') return '-'
      // 将数据分割，保留两位小数
      data = data.toFixed(2)
      // 获取整数部分
      const intPart = Math.trunc(data)
      // 整数部分处理，增加,
      const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      // 预定义小数部分
      let floatPart = '.00'
      // 将数据分割为小数部分和整数部分
      const newArr = data.toString().split('.')
      if (newArr.length === 2) { // 有小数部分
        floatPart = newArr[1].toString() // 取得小数部分
        if (1/intPart < 0 && intPart === 0) {
          return '-' + intPartFormat + '.' + floatPart
        }
        return intPartFormat + '.' + floatPart
      }
      if (1/intPart < 0 && intPart === 0) {
        return '-' + intPartFormat + '.' + floatPart
      }
      return intPartFormat + floatPart
    },
    /** 格式化金额 */
    formaterMoney1(data) {
      if (data === 0) return '0.00'
      if (!data) return ''
      if (data === '-') return '-'
      // 将数据分割，保留两位小数
      data = data.toFixed(4)
      // 获取整数部分
      const intPart = Math.trunc(data)
      // 整数部分处理，增加,
      const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      // 预定义小数部分
      let floatPart = '.00'
      // 将数据分割为小数部分和整数部分
      const newArr = data.toString().split('.')
      if (newArr.length === 2) { // 有小数部分
        floatPart = newArr[1].toString() // 取得小数部分
        return intPartFormat + '.' + floatPart
      }
      return intPartFormat + floatPart
    },

  }
};
</script>
<style>
.grid-content2 {
  /* border-radius: 10px;
  height: 50px;
  line-height: 14px; */
  color:#cccccc;
  /* font-weight:bold; */
  font-size:13px;
  text-align: center;
  /*margin-left: 24px;*/
}
.grid-content1 {
  /* border-radius: 10px;
  height: 50px;
  line-height: 14px; */
  color:#ff0000;
  /* font-weight:bold; */
  font-size:13px;
  text-align: center;
  /*margin-left: 24px;*/
}
</style>
