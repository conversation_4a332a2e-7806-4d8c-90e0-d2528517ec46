<template>
  <div class="app-container">
      <!-- 核对div-->
      <div v-show="checkShow" style="text-align: center">
        <!-- 上传组件的div-->
        <span style="">上传附件：</span>
        <el-upload
          ref="checkExcel"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url"
          :before-remove="beforeRemove"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          :file-list="upFileList"
          :data="uploadData"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip" v-if="this.queryFromFirst.productNo === '0'">
            <span>请选择从邮件获取的《富邦银行日放还款整体情况》文件</span><br>
            <span>系统将提取以下字段进行对比：</span><br>
            <span>• 放款金额</span><br>
            <span>• 实还本金</span><br>
            <span>• 用户实还息费</span><br>
            <span>• 借条分润</span><br>
            <span>• 中保分润</span><br>
            <span>• 客户账贷款余额</span>
          </div>
          <div class="el-upload__tip" slot="tip" v-if="this.queryFromFirst.productNo === '1'">
            <span>请选择从邮件获取的《北部湾银行日放还款整体情况》文件</span><br>
            <span>系统将提取以下字段进行对比：</span><br>
            <span>• 放款金额</span><br>
            <span>• 实还本金</span><br>
            <span>• 用户实还息费</span><br>
            <span>• 借条分润</span><br>
            <span>• 中保分润</span><br>
            <span>• 客户账贷款余额</span>
          </div>
        </el-upload>

        <div v-if="buttonFlag === false" style="width: 100%;height:60px; line-height: 45px;">
          <el-button size="mini" type="info" disabled>开始核对</el-button>
          <el-button size="mini" @click="goBackFirst()">取消</el-button>
        </div>
        <div v-if="buttonFlag === true" style="width: 100%;height:60px; line-height: 45px;">
          <el-button size="mini" type="primary" @click="handleImport()">开始核对</el-button>
          <el-button size="mini" @click="goBackFirst()">取消</el-button>
        </div>
      </div>

    <!-- 核对详情列表-->
    <div v-show="checkShowDetail">
      <!-- 前面的字-->
      <div style="text-align: center" v-if="this.queryFromFirst.productNo === '0'">
        <span class="spancol">数据核对结果</span><br>
        <span class="spancol2">白色列为《每日运营报表》数据，蓝色列为《富邦银行日放还款整体情况》</span>
      </div>
      <div style="text-align: center" v-if="this.queryFromFirst.productNo === '1'">
        <span class="spancol">数据核对结果</span><br>
        <span class="spancol2">白色列为《每日运营报表》数据，蓝色列为《北部湾银行日放还款整体情况》</span>
      </div>

      <br><br>

      <el-table :data="checkList" :cell-style="cellStyle" :header-cell-style="rowClass">
        <el-table-column label="业务日期" align="center" prop="reconDate" width="180px">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.reconDate, '{y}/{m}/{d}') }}</span>
          </template>
        </el-table-column>

        <el-table-column label="放款金额" align="center" min-width="360px">
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.loanAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.loanAmt1 }}</span>
              </div>
              <div v-if="scope.row.loanAmtFlag === '0'">
                <span>{{ scope.row.loanAmt1 }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.loanAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.loanAmt2 }}</span>
              </div>
              <div v-if="scope.row.loanAmtFlag === '0'">
                <span>{{ scope.row.loanAmt2 }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="实还本金" align="center" min-width="360px">
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.actPrintAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.actPrintAmt1 }}</span>
              </div>
              <div v-if="scope.row.actPrintAmtFlag === '0'">
                <span>{{ scope.row.actPrintAmt1 }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.actPrintAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.actPrintAmt2 }}</span>
              </div>
              <div v-if="scope.row.actPrintAmtFlag === '0'">
                <span>{{ scope.row.actPrintAmt2 }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="用户实还息费" align="center" min-width="360px">
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.actIntAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.actIntAmt1 }}</span>
              </div>
              <div v-if="scope.row.actIntAmtFlag === '0'">
                <span>{{ scope.row.actIntAmt1 }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.actIntAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.actIntAmt2 }}</span>
              </div>
              <div v-if="scope.row.actIntAmtFlag === '0'">
                <span>{{ scope.row.actIntAmt2 }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="借条分润" align="center" min-width="360px">
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.jtFrAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.jtFrAmt1 }}</span>
              </div>
              <div v-if="scope.row.jtFrAmtFlag === '0'">
                <span>{{ scope.row.jtFrAmt1 }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.jtFrAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.jtFrAmt2 }}</span>
              </div>
              <div v-if="scope.row.jtFrAmtFlag === '0'">
                <span>{{ scope.row.jtFrAmt2 }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="中保分润" align="center" min-width="360px">
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.zbFrAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.zbFrAmt1 }}</span>
              </div>
              <div v-if="scope.row.zbFrAmtFlag === '0'">
                <span>{{ scope.row.zbFrAmt1 }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.zbFrAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.zbFrAmt2 }}</span>
              </div>
              <div v-if="scope.row.zbFrAmtFlag === '0'">
                <span>{{ scope.row.zbFrAmt2 }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="客户贷款余额" align="center" min-width="360px">
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.userBalanceAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.userBalanceAmt1 }}</span>
              </div>
              <div v-if="scope.row.userBalanceAmtFlag === '0'">
                <span>{{ scope.row.userBalanceAmt1 }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="" align="center" width="180px">
            <template slot-scope="scope">
              <div v-if="scope.row.userBalanceAmtFlag === '1'" style="background: #e96363">
                <span>{{ scope.row.userBalanceAmt2 }}</span>
              </div>
              <div v-if="scope.row.userBalanceAmtFlag === '0'">
                <span>{{ scope.row.userBalanceAmt2 }}</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>

      <br><br>
      <!-- 关闭按钮-->
      <div style="text-align: center">
        <el-button size="mini" @click="goBackFirst()">关闭</el-button>
      </div>
    </div>

  </div>
</template>

<script>
import { importData, addDay } from "@/api/yybbsc/day";
import { getToken } from '@/utils/auth'

export default {
  props: {
    queryFromFirst: {
      type: Object,
      default: function() {
        let obj = {
          productNo: null,
        };
        return obj;
      },
      required: false
    }
  },
  // name: "Day",
  data() {
    return {
      //核对的list
      // 每日运营统计表格数据
      checkList: [],
      //显示核对
      checkShow: true,
      //显示核对详情
      checkShowDetail: false,
      //向上一个页面返回的结果-告诉第二个页面展示和隐藏
      CRes: false,
      //按钮是否置灰 false-灰色，true-蓝
      buttonFlag: false,
      //文件集合
      upFileList: [],
      //上传携带的参数
      uploadData: {
        reconDate: null,
        productNo: null,
      },
      // 用户导入参数
      upload: {
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/yybbsc/day/checkData"
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询每日运营统计列表 */
    getList() {
      this.loading = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 单元格颜色 */
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 2 || columnIndex === 4 || columnIndex === 6 || columnIndex === 8 || columnIndex === 10 || columnIndex === 12) {
        return `background-color:rgb(223, 239, 255);color:black`;
      }
      if (columnIndex === 1 || columnIndex === 3 || columnIndex === 5 || columnIndex === 7 || columnIndex === 9 || columnIndex === 11) {
        return `background-color:rgb(255, 255, 255);color:black`;
      }
    },
    /** 单元格合并列 */
    // objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // if (columnIndex === 2 || columnIndex === 4 || columnIndex === 6 || columnIndex === 8 || columnIndex === 10 || columnIndex === 12) {
      //   return `background-color:rgb(0,255,255);color:black`;
      // }
      //row:对象形式，保存了当前行的数据
      //column：对象形式，保存了当前列的参数
      //rowIndex:当前行的行号
      //column：当前列的列号
      // if(rowIndex !== 0){
      //   //columnIndex表示当前列号，这里处理ID,姓名，数值2
      //   if(columnIndex === 2 || columnIndex === 4 || columnIndex === 6 || columnIndex === 8 || columnIndex === 10 || columnIndex === 12){
      //     const _row = this.spanArr[rowIndex]
      //     const _col = _row>0?1:0;
      //     //该形式为行合并
      //     return{
      //       rowspan:_row,
      //       colspan:_col
      //     }
      //   }
      //
      // }
    // },
    /** 表头并列 */
    rowClass({ row, column,rowIndex,columnIndex}) {
      if(rowIndex === 0 && columnIndex === 0) {
        this.$nextTick(()=> {
          if(document.getElementsByClassName(column.id).length !== 0) {
            document.getElementsByClassName(column.id)[0].setAttribute('rowSpan',2);
            return false
          }
        })
      }
      if(rowIndex === 1 && (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 || columnIndex === 3 || columnIndex === 4 || columnIndex === 5 || columnIndex === 6
        || columnIndex === 7 || columnIndex === 8 || columnIndex === 9 || columnIndex === 10 || columnIndex === 11)) {
        return {display:'none'}
      }

    },

    //文件上传前处理
    // 文件上传 - 上传前
    beforeUpload(file, fileList) {
      //定义文件最大的限制，单位：MB
      var maxSize = 2048;
      //文件的大小
      var fileSize = file.size / 1024 / 1024;
      //进行文件的判断
      if (fileSize <= 0){
        this.$message.error('上传文件大小不能为 0 MB');
        return false;
      } else if (fileSize < maxSize) {
        this.uploadData = { productNo: this.queryFromFirst.productNo }; //上传携带的参数名
        let promise = new Promise((resolve) => {
          this.$nextTick(function () {
            resolve(true);
          });
        });
        return promise;
      } else {
        this.$message.error(`上传文件大小不能超过2G!`);
        return false;
      }
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code === 500 || response.length === 0) {
        this.$modal.msgError("文件类型错误，请删除文件后重新选择后上传！");
      }
      if (response.code !== 500 && response.length !== 0) {
        this.checkList = response;
        this.checkShowDetail = true;
        this.checkShow = false;
        this.$refs.checkExcel.clearFiles();
      }
    },
    // 提交上传文件
    handleImport() {
      this.$refs.checkExcel.submit();
    },
    beforeRemove(file, upFileList) {
      this.buttonFlag = false;
    },
    //点击文件列表中已上传文件进行下载
    // handlePreview(file) {
    //   this.buttonFlag = true;
    // },
    handleChange(file, fileList) {
      if (file !== null) {
        this.buttonFlag = true;
      }
    },
    submitFileForm() {
      addDay(this.respObj).then(response => {
        this.$modal.msgSuccess("新增成功");
        this.CRes = true;
        this.$emit('emitToParent', this.CRes);
      })
    },

    goBackFirst() {
      this.CRes = true;
      this.checkShowDetail = false;
      this.checkShow = true;
      this.checkList = [];
      this.$refs.checkExcel.clearFiles();
      this.buttonFlag = false;
      this.$emit('emitToParent', this.CRes);
    }
  }
};
</script>
<style>
.spancol{
  color:#333333;
  font-weight:bold;
  font-size:16px;
  display: inline-block;
  margin-left: 24px;
  /* padding-top:10px; */
}
.spancol2{
  font-size:14px;
  color:#9D9D9D;
  display:block;
}
</style>
