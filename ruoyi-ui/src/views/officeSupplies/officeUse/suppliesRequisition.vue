<template>
  <div class="app-container">
    <el-form :model="form" :rules="rules" label-width="100px">
      <!-- 头部信息 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="申请人" prop="userName">
            <el-input v-model="form.userName" placeholder="请选择申请人" readonly suffix-icon="el-icon-search"
              @click.native="open = true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="申请单编号">
            <el-input v-model="form.applicationCode" disabled placeholder="保存后自动生成" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 物品选择 -->
      <el-form-item>
        <el-button type="primary" @click="handleAddItem">选择物品</el-button>
      </el-form-item>

      <!-- 物品表格 -->
      <el-table :data="form.offReceiveDetailList" border height="400" style="margin-top: 10px">
        <el-table-column align="center" show-overflow-tooltip="" prop="itemName" label="物品名称" width="180">
          <template #default="{ row }">
            <div style="color: rgb(63, 161, 255); cursor: pointer" @click="toDetail(row)">
              {{ row.itemName || row.supplyName }}
            </div>
          </template>
        </el-table-column>

        <el-table-column align="center" show-overflow-tooltip="" prop="categoryName" label="所属类别" />

        <el-table-column align="center" show-overflow-tooltip="" prop="amount" label="数量">
          <template slot-scope="scope">
            {{ scope.row.amount || scope.row.beforeNum }}
          </template>
        </el-table-column>
        <el-table-column label="本次申请数量" width="200">
          <template slot-scope="{ row }">
            <el-input-number v-model="row.applyNum" :min="1"
              :max="row.negativeInventory === '0' ? Infinity : row.amount" :precision="0" controls-position="right"
              size="small" @change="(val) => handleQuantityChange(val, row)" />
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip="" prop="measureUnit" label="计量单位" />
        <el-table-column prop="residueNum" label="库存剩余量" width="120">
          <template slot-scope="{ row }">
            {{ row.residueNum }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" fixed="right">
          <template slot-scope="{ row }">
            <el-button type="text" @click="removeItem(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 申请事由 -->
      <el-form-item label="申请事由" prop="cause">
        <el-input style="width: 50%;" type="textarea" v-model="form.cause" :rows="3" placeholder="请输入申请事由" maxlength="500"
          show-word-limit />
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注">
        <el-input style="width: 50%;" type="textarea" v-model="form.remark" :rows="2" placeholder="请输入备注信息" maxlength="200"
          show-word-limit />
      </el-form-item>

      <!-- 附件上传 -->
      <el-form-item label="附件">
        <el-upload :action="upload.url" :file-list="form.offSupplyFileList" :on-success="handleFileSuccess"
          :before-upload="beforeFileUpload" :on-remove="handleRemoveFile"
          accept=".doc,.docx,.pdf,.xls,.xlsx,.excel,.ppt,.pptx,.txt,.jpg,.png,.gif,.bmp,.jpeg"
          :data="{ fileType: 2, userName }" multiple>
          <el-button size="small" type="primary">点击上传</el-button>
          <template #file="{ file }">
            <li class="el-upload-list__item" style="display: flex; align-items: center">
              <span class="el-upload-list__item-name" style="width: 30%" @click="handlePreview(file)">
                {{ file.name || file.fileName }}
              </span>
              <span class="el-upload-list__item-actions">
                <el-button type="text" icon="el-icon-view" @click="handlePreview(file)" />
                <el-button type="text" icon="el-icon-delete" @click="handleRemoveFile(file)" />
              </span>
            </li>
          </template>
        </el-upload>
      </el-form-item>

      <!-- 操作按钮 -->
      <el-form-item style="text-align: center">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
    <!-- 在template末尾添加 -->
    <select-supplies v-if="showSelectDialog" @close="showSelectDialog = false" @submit="handleItemsSelected"
      :list="form.offReceiveDetailList" :type="type" />
    <UserDepPostSelect title="user" :multiple="false" rowKey="userId" v-model="open"
      @on-submit-success-user="userSuccess" />
    <el-image ref="previewImg" v-show="false" :src="photoUrl" :preview-src-list="imagePreviewUrls"></el-image>
    <SelectCompany v-if="selectCompanyType" @close="selectCompanyType = false" @submit="submitCompany" />
  </div>
</template>

<script>
import {
  receiveMainAdd,
  receiveMainLpAdd,
  receiveMainEdit,
  receiveMainDetail,
  getSupplyFlow,
  getGiftFlow,
} from "@/api/officeSupplies/officeSupplies";
import SelectSupplies from "./components/SelectSupplies.vue";
import { getToken } from "@/utils/auth";
import { downloadByUrl } from "@/api/oa/processTemplate";
import { getFilesPathMapping } from "@/api/cdlb/files";
export default {
  components: {
    SelectSupplies,
  },
  data() {
    return {
      selectCompanyType: false,
      photoUrl: '',
      imagePreviewUrls: [],
      type: "",
      upload: {
        headers: { Authorization: "Bearer " + getToken() },

        url:
          process.env.VUE_APP_BASE_API + "/offSupplyMain/supplyMain/uploadFile",
      },
      userName: "",
      open: false,
      showSelectDialog: false,
      form: {
        userId: "",
        userName: "",
        approvalStatus: "",
        offReceiveDetailList: [
          // 示例数据
        ],
        cause: "",
        remark: "",
        submitTime: "",
        offSupplyFileList: [],
      },
      rules: {
        userName: [{
          required: true,
          message: '请选择申请人',
          trigger: ['blur', 'change'] // 增加 change 触发条件
        }],
        // 移除申请事由的必填规则
        reason: [], // 保留空数组以维持原有结构
      },
    };
  },
  mounted() {
    this.type = this.$route.query.type || "";
    if (this.$route.query.id) {
      this.getDetail(this.$route.query.id); // 调用获取详情的方法
    }else{
      this.form.userName = sessionStorage.getItem("userNickName"); // 获取用户名;
      this.form.userId = sessionStorage.getItem("userId"); // 获取用户名;
    }
    this.userName = sessionStorage.getItem("userName"); // 获取用户名;
  },
  methods: {
    toDetail(v) {
      console.log(v);
      this.$router.push({
        path: '/officeSuppliesOther/maintenanceDetail',
        query: { id: v.supplyId }
      })

    },
    async submitCompany(e) {
      if (this.form.offReceiveDetailList[0].itemType == "BGYP") {
        var data = await getSupplyFlow({ companyId: e });
        console.log(data);
      } else {
        var data = await getGiftFlow({ companyId: e });
      }
      if (data) {
        this.selectCompanyType = false;
        sessionStorage.setItem("officeSupplies", JSON.stringify(this.form));
        this.$router.push({
          path: "/oaWork/updateProcessForm",
          query: {
            templateId: data.templateId,
            classificationId: data.classificationId,
            companyId: data.companyId,
            officeSuppliesType: true,
          },
        });
      }
    },
    getDetail(id) {
      receiveMainDetail(id).then((res) => {
        this.type = res.data.offReceiveDetailList[0].itemType;
        this.form = Object.assign(this.form, res.data);
        this.form.userName = this.form.userNickName;
      });
    },
    userSuccess(v) {
      console.log(v);
      this.form.userId = v[0].userId;
      this.form.userName = v[0].nickName;
      this.open = false;

      // 添加表单验证清除逻辑
      this.$nextTick(() => {
        this.$refs.form.validateField('userName'); // 手动触发字段验证
      });
    },
    removeItem(row) {
      this.$confirm("确认删除该物品吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.form.offReceiveDetailList = this.form.offReceiveDetailList.filter(
          (item) => item.id !== row.id
        );
      });
    },
    handleAddItem() {
      this.showSelectDialog = true;
    },
    // 添加处理选中物品的方法
    handleItemsSelected(selectedItems) {
      // 合并已选物品，过滤重复项
      const newItems = selectedItems.filter(
        (newItem) =>
          !this.form.offReceiveDetailList.some(
            (existing) => existing.id === newItem.id
          )
      );
      this.form.offReceiveDetailList = [
        ...this.form.offReceiveDetailList,
        ...newItems,
      ];

      this.form.offReceiveDetailList.forEach((item) => {
        item.supplyId = item.id;
        item.beforeNum = item.amount;
      });
      console.log(this.form.offReceiveDetailList);
    },
    handleDeleteItem(row) {
      const index = this.form.offReceiveDetailList.findIndex(
        (item) => item.id === row.id
      );
      if (index >= 0) {
        this.form.offReceiveDetailList.splice(index, 1);
      }
    },
    handleFileSuccess(response, file, fileList) {
      // 更新form.files为完整的fileList
      this.form.offSupplyFileList = fileList;

      // 如果需要保存服务器返回的数据，可以这样处理
      if (response && response.data) {
        file.id = response.data.id; // 保存服务器返回的文件ID
        file.url = response.data.filePath; // 保存服务器返回的文件路径
      }
    },

    handleRemoveFile(file) {
      this.form.offSupplyFileList = this.form.offSupplyFileList.filter(
        (f) => f.uid !== file.uid
      );
    },
    handleSave() {
      this.form.approvalStatus = 0;
      this.form.offSupplyFileList = this.form.offSupplyFileList?this.form.offSupplyFileList.map((item) => {
        return item.response && item.response.data ? item.response.data : item;
      }):[]
      console.log(this.form);
      if (this.form.offReceiveDetailList.length == 0) {
        this.$message.error("请选择物品");
        return;
      }
      if (this.form.userName == "") {
        this.$message.error("请选择申请人");
        return;
      }
      if (this.$route.query.id) {
        receiveMainEdit(this.form);
      } else {
        if(this.form.offReceiveDetailList[0].itemType == "BGYP"){
          receiveMainAdd(this.form);
        }else{
          receiveMainLpAdd(this.form);
        }
        
      }

      this.$message({
        message: "保存成功",
        type: "success",
      });
      this.$router.back();
    },
    async handleSubmit(row) {
      this.form.approvalStatus = 0;
      this.form.offSupplyFileList = this.form.offSupplyFileList?this.form.offSupplyFileList.map((item) => {
        return item.response && item.response.data ? item.response.data : item;
      }):[]
      console.log(this.form);
      if (this.form.offReceiveDetailList.length == 0) {
        this.$message.error("请选择物品");
        return;
      }
      if (this.form.userName == "") {
        this.$message.error("请选择申请人");
        return;
      }
      // var data = null
      // if (this.$route.query.id) {
      //   data = await receiveMainEdit(this.form);
      // } else {
      //   data = await receiveMainAdd(this.form);
      // }
      // console.log(data.data.id);
      // this.getDetail(data.data.id);

      this.selectCompanyType = true; // 打开选择公司的弹框



    },
    handleCancel() {
      this.$confirm("确认取消操作？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$router.back();
      });
    },
    // 修改数量变化处理
    handleQuantityChange(val, row) {
      console.log(row);

      // 添加数值类型转换和空值处理
      const amount = row.beforeNum ? row.beforeNum : row.amount;
      const applyNum = Number(val) || 0;

      // 重新计算剩余量
      row.residueNum = amount - applyNum;

      if (row.negativeInventory === "1") {
        // 添加数值有效性校验
        if (!isNaN(applyNum) && applyNum > amount) {
          this.$message.warning("申请数量不能超过库存总量");
          row.applyNum = Math.max(1, amount);
          row.residueNum = amount - (row.applyNum || 0);
        }
      }
    },
    handlePreview(file) {
      console.log(file);
      if (file.hasOwnProperty("fileName")) {
        file.name = file.fileName || file.name;
      }
      if (file.name.endsWith(".pdf") || file.name.endsWith(".html")) {
        //文件是pdf格式
        getFilesPathMapping().then((resp) => {
          let url = resp.msg + file.filePath
          window.open(url);
          return;
        });
        return;
      } else if (
        file.name.endsWith(".jpg") ||
        file.name.endsWith(".jpeg") ||
        file.name.endsWith(".png") ||
        file.name.endsWith(".gif")
      ) {
        //文件是图片格式
        getFilesPathMapping().then((resp) => {
          this.photoUrl = resp.msg + file.filePath

          let array = new Set([]);
          array.add(this.photoUrl);
          let from = Array.from(array);
          this.imagePreviewUrls = from;
          this.$refs.previewImg.showViewer = true;
        });
        // this.showImgViewer = true;
      } else {
        //文件下载
        this.handleDownload(file);
      }
    },
    beforeFileUpload(file) { },
    handleDownload(file) {
      if (file.type) {
        this.downFile(file.url, file.fileName);
      } else {
        if (file.hasOwnProperty("fileName")) {
          file.name = file.fileName;
        }
        const url = file.url || file.filePath; //图片的https链接
        downloadByUrl({
          url: url,
        }).then((res) => {
          let href = window.URL.createObjectURL(new Blob([res])); // 根据后端返回的url对应的文件流创建URL对象
          const link = document.createElement("a"); //创建一个隐藏的a标签
          link.target = "_blank";
          link.href = href; //设置下载的url
          link.download = file.name; //设置下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(href); // 释放掉blob对象
        });
      }
    },
  },
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 22px;
}

.el-table {
  margin: 20px 0;
}
</style>