<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-form :model="params" inline class="search-form">
      <el-form-item label="申请物品名称">
        <el-input v-model="params.itemName" placeholder="请输入名称" clearable />
      </el-form-item>

      <el-form-item label="状态">
        <el-select v-model="params.approvalStatus" placeholder="请选择状态" clearable>
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间">
        <el-date-picker v-model="params.createTimeRange" type="daterange" range-separator="-" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>

    <el-divider />

    <!-- 操作按钮 -->
    <div class="operation-buttons">
      <el-button v-hasPermi="['officeUse:addBgyp']" type="primary" icon="el-icon-plus"
        @click="handleCreate('BGYP')">发起办公用品领用申请</el-button>

      <el-button v-hasPermi="['officeUse:addLp']" type="primary" icon="el-icon-plus" @click="handleCreate('LP')"
        style="margin-left: 15px">发起礼品领用申请</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table :data="tableData" style="width: 100%; margin-top: 20px">
      <el-table-column prop="applicationCode" label="申请单编号" width="180" />
      <el-table-column prop="itemName" label="申请物品名称" width="150" />
      <el-table-column prop="cause" label="申请事由" width="200"/>
      <el-table-column prop="userNickName" label="申请人" />
      <el-table-column prop="approvalStatusLabel" label="状态" width="120">
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="submitTime" label="提交时间" width="180" />
      <el-table-column prop="approvalTime" label="审核通过时间" width="180" />

      <el-table-column label="操作" width="200" fixed="right">
        <template slot-scope="{ row }">
          <el-button v-show="row.approvalStatus == 0" type="text" @click="handleSubmit(row)"
            v-hasPermi="['officeUse:submit']">提交</el-button>
          <el-button type="text" @click="handleEdit(row)" v-show="row.approvalStatus == 0"
            v-hasPermi="['officeUse:edit']">修改</el-button>
          <el-button v-hasPermi="['officeUse:del']" type="text" v-show="row.approvalStatus == 0" style="color: red"
            @click="handleDelete(row)">删除</el-button>
          <el-button type="text" v-if="
            row.approvalStatus == 1 ||
            row.approvalStatus == 2 ||
            row.approvalStatus == 3
          " @click="handleViewProcess(row)">查看流程</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="params.pageNum" :page-sizes="[10, 20, 50, 100]"
      :limit.sync="params.pageSize" @pagination="getList" />
    <SelectCompany v-if="selectCompanyType" @close="selectCompanyType = false" @submit="submitCompany" />
  </div>
</template>

<script>
import {
  receiveMainList,
  getSupplyFlow,
  getGiftFlow,
  receiveMainDetail,
  receiveMainDel,
} from "@/api/officeSupplies/officeSupplies";

export default {
  name: "OfficeUse",
  data() {
    return {
      selectCompanyType: false,
      total: 0,
      // 查询参数
      params: {
        pageNum: 1,
        pageSize: 10,
        createBeginTime: "",
        createEndTime: "",
        itemName: "",
        approvalStatus: "",
        createTimeRange: [],
      },
      // 状态选项
      statusOptions: [
        { value: 0, label: "未提交" },
        { value: 1, label: "审核中" },
        { value: 2, label: "审核不通过" },
        { value: 3, label: "审核通过" },
      ],
      // 表格数据
      tableData: [],
      itemData: null,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    async submitCompany(e) {
      if (this.itemData.offReceiveDetailList[0].itemType == "BGYP") {
        var data = await getSupplyFlow({ companyId: e });
        console.log(data);
      } else {
        var data = await getGiftFlow({ companyId: e });
      }
      if (data) {
        this.selectCompanyType = false;
        sessionStorage.setItem("officeSupplies", JSON.stringify(this.itemData));
        this.$router.push({
          path: "/oaWork/updateProcessForm",
          query: {
            templateId: data.templateId,
            classificationId: data.classificationId,
            companyId: data.companyId,
            officeSuppliesType: true,
          },
        });
      }
    },
    getList() {
      if (this.params.createTimeRange.length > 0) {
        this.params.createBeginTime = this.params.createTimeRange[0];
        this.params.createEndTime = this.params.createTimeRange[1];
      } else {
        this.params.createBeginTime = "";
        this.params.createEndTime = "";
      }
      receiveMainList(this.params).then((res) => {
        this.tableData = res.rows;
        this.total = res.total;
      });
    },

    // 搜索
    handleSearch() {
      this.params.pageNum = 1;
      this.getList();
    },
    // 重置
    handleReset() {
      this.params = {
        itemName: "",
        approvalStatus: null,
        createBeginTime: "",
        createEndTime: "",
        createTimeRange: [],
        pageNum: 1,
        pageSize: 10,

      };
      this.getList();
    },
    // 新建申请
    handleCreate(type) {
      this.$router.push({
        name: "SuppliesRequisition",
        query: { type: type },
      });
    },
    // 提交申请
    handleSubmit(row) {
      console.log("提交申请单:", row);
      receiveMainDetail(row.id).then((res) => {
        this.itemData = res.data;
        this.selectCompanyType = true; // 打开选择公司的弹框
      });
    },
    // 修改申请
    handleEdit(row) {
      console.log("修改申请单:", row);
      this.$router.push({
        name: "SuppliesRequisition",
        query: { id: row.id },
      });
    },
    handleDelete(row) {
      this.$confirm("确认删除该申请单吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        receiveMainDel(row.id).then((res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.getList();
          }
        });
      });
    },
    handleViewProcess(row) {
      console.log("查看流程:", row);
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: row.processId,
          businessId: row.processId,
          financeProcess: "true",
        },
      });
    },
  },
};
</script>

<style scoped>
.search-form {
  margin-bottom: -10px;
}

.operation-buttons {
  margin-bottom: 20px;
}

.el-divider {
  margin: 20px 0;
}
</style>