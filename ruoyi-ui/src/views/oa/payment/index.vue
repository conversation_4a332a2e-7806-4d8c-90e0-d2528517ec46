<template>
  <div class="app-container">
    <div style="width: 100%; height: 30px">
      <span style="color: #9d9d9d; margin-left: 20px"
        >说明：配置OA流程中的付款人信息</span
      >
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="收付款人名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账号" prop="accountNumber">
        <el-input
          v-model="queryParams.accountNumber"
          placeholder="请输入账号"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属账套" prop="accountId">
        <el-select
          v-model="queryParams.accountId"
          placeholder="请选择"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        >
          <el-option
            v-for="item in accountSetList"
            :key="item.id"
            :label="item.companyName"
            :value="item.id"
          ></el-option
        ></el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div style="width: 100%; height: 10px"></div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" style="display: flex; align-items: center">
        <span>类型：</span>
        <el-radio-group v-model="queryParams.traderType" @change="getList">
          <!-- <el-radio-button label="0" @click="querycompony()">公司付款人</el-radio-button>
          <el-radio-button label="1" @click="querypersonagelabel()">个人付款人</el-radio-button>-->
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button label="9">收款/付款 </el-radio-button>
          <el-radio-button label="1">仅收款</el-radio-button>
          <el-radio-button label="0">仅付款</el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="1.5" style="display: flex; align-items: center">
        <span>主体类型：</span>
        <el-radio-group v-model="queryParams.type" @change="changeType()">
          <!-- <el-radio-button label="0" @click="querycompony()">公司付款人</el-radio-button>
          <el-radio-button label="1" @click="querypersonagelabel()">个人付款人</el-radio-button>-->
          <el-radio-button label="2">全部</el-radio-button>
          <el-radio-button label="0">公司</el-radio-button>
          <el-radio-button label="1">个人</el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['payment:add']"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          >新增收付款人</el-button
        >
        <el-button
          type="primary"
          @click="editTypeChange"
          v-hasPermi="['payment:oa']"
          >编辑需审核（{{ changeEditType ? "已开启" : "已关闭" }}）</el-button
        >
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="traderList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="序号" align="center" width="50">
        <template slot-scope="scope">
          {{ scope.$index + 1 + (queryParams.pageNum - 1) * 10 }}
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="traderType">
        <template slot-scope="scope">
          {{
            scope.row.traderType == 1
              ? "收款人"
              : scope.row.traderType == 0
              ? "付款人"
              : scope.row.traderType == 9
              ? "收/付款人"
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column label="主体类型" align="center" prop="type">
        <template slot-scope="scope">
          {{ scope.row.type == 0 ? "公司" : scope.row.type == 1 ? "个人" : "" }}
        </template>
      </el-table-column>
      <el-table-column label="账户名称" align="center" prop="userName" />
      <el-table-column label="开户行" align="center" prop="bankOfDeposit" />
      <el-table-column label="账号" align="center" prop="accountNumber" />
      <el-table-column label="简称" align="center" prop="abbreviation" />
      <el-table-column label="所属公司" align="center" prop="companyName" />
      <el-table-column label="所属账套" align="center" prop="accountName" />
      <el-table-column
        label="审核状态"
        width="120"
        v-if="changeEditType"
        show-overflow-tooltip=""
        align="left"
        prop="remark"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.addNotApprove == 97">删除审核中</span>
          <span v-if="scope.row.addNotApprove == 98">修改审核中</span>
          <span v-if="scope.row.addNotApprove == 99">新增审核中</span>
          <span v-if="scope.row.addNotApprove == 96">-</span>
        </template>
      </el-table-column>
      <el-table-column label="启用状态" align="center" prop="isEnable">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isEnable == 'Y'">正常</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['payment:edit']"
            v-if="scope.row.addNotApprove == 96"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="editRcored(scope.row)"
            v-hasPermi="['payment:editRecord']"
            >编辑记录</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-hasPermi="['payment:del']"
            v-if="scope.row.addNotApprove == 96 && scope.row.isEnable == 'N'"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog title="修改记录" :visible.sync="dinamicListdeilog" width="30%">
      <el-table :data="dynamicDataList">
        <el-table-column
          property="operationTime"
          label="时间"
        ></el-table-column>
        <el-table-column
          property="operationContent"
          label="操作"
        ></el-table-column>
        <el-table-column property="operationBr" label="用户"></el-table-column>
      </el-table>
      <span slot="footer">
        <el-button @click="dinamicListdeilog = false">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="addOrUpdate"
      :visible.sync="updateOrAddDeilog"
      width="30%"
    >
      <span>{{ this.addOrUpdateText }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cencelclose()">取 消</el-button>
        <el-button type="primary" @click="checkrepeatdata()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加或修改【请填写功能名称】对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        :modal-append-to-body="false"
      >
        <el-form-item label="类型" prop="traderType">
          <el-radio-group v-model="form.traderType">
            <el-radio label="9">收款/付款</el-radio>
            <el-radio label="1">仅收款</el-radio>
            <el-radio label="0">仅付款</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="主体类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio label="0">公司</el-radio>
            <el-radio label="1">个人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="账户名称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="开户行" prop="bankOfDeposit">
          <el-input v-model="form.bankOfDeposit" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="账号" prop="accountNumber">
          <el-input v-model="form.accountNumber" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="简称" prop="abbreviation">
          <el-input v-model="form.abbreviation" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属公司" prop="companyNo">
          <el-select
            v-model="form.companyNo"
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option
              v-for="item in projects"
              :key="item.companyId"
              :label="item.name"
              :value="item.companyId"
            ></el-option>
          </el-select>
          <p style="color: #999; line-height: 18px">
            用户能看到或编辑与自己所属公司相同的收付款人信息
          </p>
        </el-form-item>
        <el-form-item label="所属账套" prop="accountId">
          <el-select
            v-model="form.accountId"
            filterable
            size="mini"
            style="width: 300px"
            placeholder="请选择项目类型"
          >
            <el-option
              v-for="item in accountSetList"
              :key="item.id"
              :label="item.companyName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <span style="color: #9d9d9d; margin-left: 80px"
          >如果需要，发起OA打款流程可按照收/付款人所属的账套来生成记账凭证</span
        >

        <el-form-item label="启用状态" prop="isEnable">
          <el-switch v-model="form.isEnable" active-value="Y" inactive-value="N"
            >启用</el-switch
          >
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>

        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="设置"
      :show-close="false"
      :visible.sync="editDialogType"
      width="600px"
    >
      <p>
        编辑需审核：<el-switch v-model="changeEditType2"> </el-switch
        >{{ changeEditType2 ? "已开启" : "已关闭" }}
      </p>
      <div v-if="changeEditType">
        <p>
          关闭 [编辑需审核]
          功能后，新增、修改、删除收付款人都不需要通过OA流程审核，保存即生效
        </p>
        <p>注意：关闭后，当前处于审核中的收付款人信息依然需要等待审核完成</p>
      </div>
      <div v-else>
        开启 [编辑需审核]
        功能后，如果收付款人发生新增、修改、删除，必须通过OA流程进行审核
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogType = false">取 消</el-button>
        <el-button type="primary" @click="submitChangeEdit">确 定</el-button>
      </span>
    </el-dialog>
    <editData
      :oldData="oldForm"
      :newData="form"
      v-if="editDataType"
      :accountSetList="accountSetList"
      @close="editDataType = false"
      @submit="submitEdit"
      :personData="personData"
    />
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="editSubmitType"
      width="500px"
      :show-close="false"
    >
      <p style="font-weight: bold; text-align: center">编辑申请已提交!</p>
      <p style="text-align: center">
        以下人员将在OA系统待办中收到待审核通知，审核通过后编辑内容立即生效。请及时沟通以尽快完成审核
      </p>
      <p style="text-align: center">
        <span style="font-weight: bold">{{ personData.zrr }}</span
        >：{{ personData.zrrList }}
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </span>
    </el-dialog>
    <examine
      v-if="examineType"
      :detailData="detailData"
      @close="examineType = false"
      :accountSetList="accountSetList"
      @submit="submitExamine"
    />
    <know
      v-if="knowType"
      :title="knowTitle"
      @close="knowType = false"
      @confirm="submitKnow"
    />
    <editRecord
      :changeEditType="changeEditType"
      :recordId="recordId"
      v-if="editRecordType"
      @close="editRecordType = false"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="selectCompanyType = false"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import editData from "./editData.vue";
import examine from "./examine.vue";
import know from "./know.vue";
import editRecord from "./editRecord.vue";
import {
  editExamine,
  checkEditExamine,
  getuser,
  traderDetail,
  traderCheck,
  addNewEditInfoTrader,
  traderviewCount,
} from "@/api/oa/voucharRules";

import {
  listTrader,
  getTrader,
  delTrader,
  addTraderNew,
  updateTraderNew,
  changeenableStatus,
  checkTrader,
} from "@/api/oa/trader";
import { selectAccountInfo, selectCompanyInfo } from "@/api/oa/voucharRules";
import { listDynamic } from "@/api/oa/dynamic";
import { getDataByTemplName } from "@/api/oa/deploy";

export default {
  components: {
    editData,
    examine,
    know,
    editRecord,
  },
  name: "Payment",
  data() {
    return {
      selectCompanyType: false,
      knowTitle: "",
      countData: null,
      knowType: false,
      editRecordType: false,
      examineType: false,
      detailData: null,
      editSubmitType: false,
      oldForm: null,
      editDataType: false,
      changeEditType: null,
      changeEditType2: false,
      editDialogType: false,
      accountSetList: [],
      dynamicDataList: [],
      dinamicListdeilog: false,
      addOrUpdate: "",
      addOrUpdateText: "",
      updateOrAddDeilog: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【请填写功能名称】表格数据
      traderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,

        accountId: "",
        traderType: "",
        type: 2,
        userName: null,
        bankOfDeposit: null,
        accountNumber: null,
        abbreviation: null,
        financialStaffList: [],
        salesmanList: [],
        isEnable: null,
        endUpdateTime: null,
      },
      // 表单参数
      form: {
        financialStaffList: [],
        salesmanList: [],
      },
      // 表单校验
      rules: {},
      userList: {},
      yewuType: false,
      caiwuType: false,
      roleList: [],
      userId: "",
      personData: {},
      projects: [],
      recordId: null,
    };
  },
  created() {
    selectCompanyInfo().then((response) => {
      this.projects = response.data;
    });
    this.userId = Number(sessionStorage.getItem("userId"));
    if (sessionStorage.getItem("roleList")) {
      this.roleList = JSON.parse(sessionStorage.getItem("roleList"));
    }

    this.checkEditExamine();
    this.getList();
  },
  methods: {
    submitCompany(v) {
      getDataByTemplName({
        companyId: v,
        templateName: `业务信息配置-${
          this.knowTitle == "新增收付款人"
            ? "新增"
            : this.knowTitle == "编辑收付款人"
            ? "修改"
            : "删除"
        }收付款人申请`,
        isEnableCompanyId: 1,
      }).then((res) => {
        this.selectCompanyType = false;
        let obj = {
          ...this.form,
          editType:
            this.knowTitle == "新增收付款人"
              ? "0"
              : this.knowTitle == "编辑收付款人"
              ? "1"
              : "2",
        };
        if (this.knowTitle == "新增收付款人") {
          sessionStorage.setItem("addPaymentData", JSON.stringify(obj));
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              addPayment: true,
            },
          });
        } else if (this.knowTitle == "编辑收付款人") {
          let obj2 = {
            oldData: this.oldForm,
            newData: this.form,
          };
          sessionStorage.setItem("editPaymentData", JSON.stringify(obj2));
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              editPayment: true,
            },
          });
        } else {
          sessionStorage.setItem("delPaymentData", JSON.stringify(obj));
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              delPayment: true,
            },
          });
        }
        this.selectCompanyType = false;
        this.knowType = false;
        this.open = false;
      });
    },
    editRcored(v) {
      this.recordId = v.id;
      this.editRecordType = true;
    },
    submitKnow() {
      this.selectCompanyType = true;
    },

    getDetail(v) {
      traderDetail({ oaApplyType: 1, oaApplyId: v.oaApplyId || v.id }).then(
        (res) => {
          if (res.code == 200) {
            this.detailData = res.data;
            this.examineType = true;
          }
        }
      );
    },
    submitExamine(v, i) {
      //0通过1驳回
      if (v == 0) {
        this.$confirm("点击确定，本次修改将立即生效?", "审核通过", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            traderCheck({ ...this.detailData, rejectFlag: v }).then((res) => {
              if (res.code == 200) {
                this.examineType = false;
                this.$message.success("审核已通过");
                this.getList();
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消",
            });
          });
      } else {
        traderCheck({
          ...this.detailData,
          rejectFlag: v,
          checkRejectInfo: i,
        }).then((res) => {
          if (res.code == 200) {
            this.examineType = false;
            this.$message.success("审核已驳回");
            this.getList();
          }
        });
      }
    },
    close() {
      this.editSubmitType = false;
      this.delSubmitType = false;
      this.editDataType = false;
      this.open = false;
      this.getList();
    },
    submitEdit(v) {
      addNewEditInfoTrader({ ...v }).then((res) => {
        if (res.code == 200) {
          this.editSubmitType = true;
        }
      });
    },
    checkEditExamine() {
      checkEditExamine({ projectType: 2 }).then((res) => {
        if (res.code == 200) {
          this.changeEditType = res.msg == 0 ? false : true;
          this.changeEditType2 = res.msg == 0 ? false : true;
        }
      });
    },
    submitChangeEdit() {
      editExamine({
        projectType: 2,
        isEnable: this.changeEditType ? 0 : 1,
      }).then((res) => {
        if (res.code == 200) {
          this.editDialogType = false;
          this.checkEditExamine();
          this.$message.success("操作成功");
          this.getList();
        }
      });
    },
    editTypeChange() {
      this.editDialogType = true;
    },
    //修改校验规则
    updaterules() {
      const isNum = (rule, value, callback) => {
        const num = /^[0-9]*$/;
        if (!num.test(value)) {
          callback(new Error("只能输入数字，请检查"));
        } else {
          callback();
        }
      };

      this.rules = {
        traderType: [{ required: true, message: "请选择", trigger: "change" }],
        type: [{ required: true, message: "请选择", trigger: "change" }],
        companyNo: [
          { required: true, message: "请选择公司", trigger: "change" },
        ],
        userName: [
          { required: true, message: "请输入账户名称", trigger: "blur" },
          {
            min: 1,
            max: 40,
            message: "长度在 1 到 40 个字符",
            trigger: "blur",
          },
        ],
        bankOfDeposit: [
          { required: true, message: "请输入开户行", trigger: "blur" },
          {
            min: 1,
            max: 40,
            message: "长度在 1 到 40 个字符",
            trigger: "blur",
          },
        ],
        accountNumber: [
          { required: true, message: "请输入账号", trigger: "blur" },
          { validator: isNum, trigger: "blur" },
        ],
        abbreviation: [
          { required: true, message: "请输入简称", trigger: "blur" },
          {
            min: 1,
            max: 40,
            message: "长度在 1 到 40 个字符",
            trigger: "blur",
          },
        ],
      };
    },
    changeType() {
      if (this.queryParams.type == 0) {
        this.querycompony();
      } else if (this.queryParams.type == 1) {
        this.querypersonage();
      } else {
        this.getList();
      }
    },
    //查询公司付款人
    querycompony() {
      this.queryParams.type = 0;
      this.getList();
    },
    //查询个人付款人
    querypersonage() {
      this.queryParams.type = 1;
      this.getList();
    },
    //修改启用状态
    updateinEnable(row) {
      let text = row.isEnable === "Y" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.userName + '"付款人吗？')
        .then(function () {
          console.log("到这了");
          return changeenableStatus(row.id, row.isEnable);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.isEnable = row.isEnable === "Y" ? "N" : "Y";
        });
    },
    /** 查询【请填写功能名称】列表 */
    getList() {
      this.loading = true;

      listTrader({
        ...this.queryParams,
        type: this.queryParams.type == 2 ? null : this.queryParams.type,
      }).then((response) => {
        this.traderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      selectAccountInfo().then((response) => {
        this.accountSetList = response.data;
      });
      traderviewCount({
        ...this.queryParams,
        type: this.queryParams.type == 2 ? null : this.queryParams.type,
      }).then((res) => {
        this.countData = res.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        traderType: "",
        type: "0",
        userName: null,
        bankOfDeposit: null,
        accountNumber: null,
        abbreviation: null,
        financialStaffList: [],
        salesmanList: [],
        isEnable: "Y",
        isAccount: "N",
        companyNo: null,
        accountId: null,
        endUpdateTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      getuser().then((res) => {
        this.userList = res;
        let yewu = this.userList.yewu.find((item) => {
          return item.value == this.userId;
        });
        if (yewu) {
          this.yewuType = true;
          if (this.form.financialStaffList.includes(yewu.value)) {
            return;
          }
          this.form.financialStaffList.push(yewu.value);
          return;
        }
        let caiwu = this.userList.caiwu.find((item) => {
          return item.value == this.userId;
        });
        if (caiwu) {
          this.caiwuType = true;
          if (this.form.salesmanList.includes(caiwu.value)) {
            return;
          }
          this.form.salesmanList.push(caiwu.value);
        }
      });
      this.title = "新增收付款人";
      this.open = true;
      this.updaterules();
    },
    updateDynamicList(row) {
      this.dinamicListdeilog = true;
      var dynamic = {
        oaTraderId: row.id,
      };
      listDynamic(dynamic).then((response) => {
        this.dynamicDataList = response.rows;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();

      const id = row.id || this.ids;
      this.updaterules();
      getTrader(id).then((response) => {
        this.oldForm = JSON.parse(JSON.stringify(response.data));
        this.form = response.data;

        getuser().then((res) => {
          this.userList = res;
          let yewu = this.userList.yewu.find((item) => {
            return item.value == this.userId;
          });
          if (yewu) {
            this.yewuType = true;
            if (this.form.financialStaffList.includes(yewu.value)) {
              return;
            }
            this.form.financialStaffList.push(yewu.value);
            return;
          }
          let caiwu = this.userList.caiwu.find((item) => {
            return item.value == this.userId;
          });
          if (caiwu) {
            this.caiwuType = true;
            if (this.form.salesmanList.includes(caiwu.value)) {
              return;
            }
            this.form.salesmanList.push(caiwu.value);
          }
        });
        this.open = true;
        this.title = "编辑付款人";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null && this.changeEditType) {
            this.knowTitle = "编辑收付款人";
            this.knowType = true;
            return;
          }
          if (this.form.id == null && this.changeEditType) {
            this.knowTitle = "新增收付款人";
            this.knowType = true;
            // addNewEditInfoTrader({ ...this.form, editType: 0 }).then((response) => {
            //   this.$modal.msgSuccess("新增付款人成功");
            //   this.updateOrAddDeilog = false;
            //   this.open = false;
            //   this.getList();
            // });
            return;
          }
          if (this.form.id != null) {
            this.addOrUpdate = "编辑收付款人";
            this.addOrUpdateText = "是否确定编辑此收付款人信息？";
            this.updateOrAddDeilog = true;
          } else {
            this.updateOrAddDeilog = true;
            this.addOrUpdateText =
              "是否确定创建此收付款人信息？创建后与所属公司相同公司的用户都可使用此信息";
            this.addOrUpdate = "新增收付款人";
          }
        }
      });
    },
    submitData() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            let data = {
              oaTraderId: this.form.id,
              newOaTrader: {
                ...this.form,
                oaApplyRecordsOldData: JSON.stringify(this.oldForm),
                oaApplyRecordsNewData: JSON.stringify(this.form),
              },
            };
            updateTraderNew(data).then((response) => {
              this.$modal.msgSuccess("保存成功");
              this.open = false;
              this.updateOrAddDeilog = false;
              this.getList();
            });
          } else {
            addTraderNew({
              ...this.form,
              oaApplyRecordsOldData: null,
              oaApplyRecordsNewData: JSON.stringify(this.form),
            }).then((response) => {
              this.$modal.msgSuccess("新增收付款人成功");
              this.updateOrAddDeilog = false;
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    checkrepeatdata() {
      checkTrader(this.form).then((response) => {
        if (response.isok == "Y") {
          this.submitData();
        } else if (response.isok == "N") {
          this.$message.error("系统已存在相同账号");
        }
      });
    },
    cencelclose() {
      this.updateOrAddDeilog = false;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      getTrader(row.id).then((res) => {
        this.form = res.data;
        const ids = row.id || this.ids;
        if (!this.changeEditType) {
          this.$modal
            .confirm("是否确认删除此收付款人信息？")
            .then(function () {
              return delTrader(ids);
            })
            .then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            })
            .catch(() => {});
        } else {
          this.knowTitle = "删除收付款人";
          this.knowType = true;
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/trader/export",
        {
          ...this.queryParams,
        },
        `trader_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style lang="less" scoped>
.item {
  margin-bottom: 12px;
  /deep/ .el-input__inner {
    width: 250px !important;
  }
  span {
    display: inline-block;
    width: 100px;
    font-weight: bold;
    text-align: right;
    margin-right: 12px;
    i {
      color: red;
      margin-right: 5px;
    }
  }
}
</style>
 