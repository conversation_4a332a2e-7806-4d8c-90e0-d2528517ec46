<template>
  <div>
    <el-dialog
      :title="'已选择' + tableData.length + '条'"
      :visible.sync="dialogVisible"
      width="900px"
      :before-close="handleClose"
    >
      <el-table
        :data="tableData"
        style="width: 100%; margin-top: 16px; margin-left: 4px"
      >
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="catalogueCode"
          label="目录编号"
          width="180"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="catalogueName"
          label="目录名称"
          width="180"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="orderNum"
          label="排序号"
          width="100"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="parentName"
          label="上级目录"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="catalogueSystemCode"
          label="系统目录编号"
          width="140"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="pertainDeptName"
          label="所属集团"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="deptName"
          label="所属组织"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="createBy"
          label="创建人"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          prop="createTime"
          label="创建时间"
        />
        <el-table-column
          align="center"
          show-overflow-tooltip=""
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button @click="del(scope.row)" type="text" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
    <addItem
      :editData="editData"
      :seeType="seeType"
      v-if="addItemType"
      @close="addItemType = false"
    />
  </div>
</template>

<script>
import addItem from "./addItem.vue";

export default {
  props: {
    multipleSelection: Array,
  },
  components: {
    addItem,
  },
  data() {
    return {
      dialogVisible: true,
      tableData: [],
      seeType: false,
      editData: null,
      addItemType: false,
    };
  },
  mounted() {
    this.tableData = JSON.parse(JSON.stringify(this.multipleSelection));
    console.log(this.tableData);
  },
  methods: {
    see(v) {
      this.seeType = true;
      this.editData = { ...v };
      this.addItemType = true;
    },
    submit() {
      this.$emit("confirm", this.tableData);
    },
    del(v) {
      this.tableData.map((item, index) => {
        if (item.id == v.id) {
          this.tableData.splice(index, 1);
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>