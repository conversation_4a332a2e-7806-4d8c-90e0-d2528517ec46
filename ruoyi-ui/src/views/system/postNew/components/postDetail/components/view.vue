<template>
  <div>
    <div class="flex mb-5">
      <el-button @click="cancel">关闭</el-button>
      <el-button @click="goEdit" type="primary">编辑</el-button>
    </div>
    <div>
      <el-form ref="form" :model="myform" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="岗位名称:">
              {{ myform.postName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="岗位编码:">
              {{ myform.postCode }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属公司:">
              {{ myform.unitName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门:">
              {{ myform.dept&& myform.dept.deptName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="岗位类型:">
              {{ dict.label.sys_post_type[myform.postType] }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="岗位负责人:">
              {{ myform.leaderName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="顺序号:">
              {{ myform.postSort }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="岗位状态:">
              <dict-tag
                :options="dict.type.sys_normal_disable"
                :value="myform.status"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="备注信息:">
              {{ myform.remark }}
            </el-form-item>
          </el-col>
        </el-row>
        <div>
          <div class="font-bold mb-5">功能菜单权限</div>
          <el-checkbox
            v-model="menuExpand"
            @change="handleCheckedTreeExpand($event)"
            >展开/折叠</el-checkbox
          >
          <el-tree
            class="tree-border mb-5 treeLine"
            :data="menuOptions"
            :default-expanded-keys="defaultExpandedKeys"
            ref="menu"
            node-key="id"
            
            :props="defaultProps"
          ></el-tree>
        </div>
        <el-row>
          <el-col :span="24">
            <span class="font-bold mb-5">用户</span>
          </el-col>
        </el-row>
        <el-row>
          <div class="flex mb-2">
            <el-input
              class="w-48 mr-5"
              v-model="queryParams.nickName"
              placeholder="请输入用户名称"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </div>
          <div>共{{ total }}条</div>
          <el-col :span="12">
            <el-table :data="myform.userList">
              <el-table-column
                label="账号"
                prop="userName"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="名称"
                prop="nickName"
                :show-overflow-tooltip="true"
              />
              <el-table-column label="状态" align="center" prop="status">
                <template slot-scope="scope">
                  <dict-tag
                    :options="dict.type.sys_normal_disable"
                    :value="scope.row.status"
                  />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row>
           <el-col :span="12">
           <pagination

            v-show="total > 20"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import config from "./config";
import { postMenuTreeSelect } from "@/api/system/menu";
import { accreditUsers } from "@/api/system/user";
import { filterTreeData } from "@/utils";
export default {
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    id: {
      type: [String, Number],
      required: true,
      default: "",
    },
  },
  dicts: ["sys_normal_disable", "sys_post_type"],
  data() {
    return {
      ...config,
      myform: {},
      menuOptions: [],
      menuExpand: false,
      queryParams: {
        nickName: undefined,
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      defaultExpandedKeys: [],
    };
  },
  watch: {
    form: {
      handler(val) {
        this.myform = XEUtils.clone(val, true);
      },
      immediate: true,
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getPost();
      this.getList();
    },
    handleCheckedTreeExpand(value) {
      let treeList = this.menuOptions;
      for (let i = 0; i < treeList.length; i++) {
        this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
      }
    },
    async getPost() {
      postMenuTreeSelect(this.id).then((response) => {
        this.menuOptions =filterTreeData(response.menus,response.checkedKeys) ;
      });
    },
    async handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    async getList() {
      const { rows, total } = await accreditUsers({...this.queryParams,postId:this.id});
      this.myform.userList = rows;
      this.total = total;
    },
    resetQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 20;
      this.queryParams.nickName = "";
      this.getList();
    },
    cancel() {
      this.$parent.addUpdateCallBack();
    },
    goEdit() {
      this.$emit("goEdit");
    },
  },
};
</script>
