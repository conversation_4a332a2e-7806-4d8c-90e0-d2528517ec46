<template>
  <div>
    <div v-if="personnelData.flag">
      <p>本用户可发起哪些OA流程</p>
      <div class="oa_table" v-for="item in dataList" :key="item.id">
        <div class="left">{{item.isCompany=="0"?item.proCompanyName: item.proClassName }}</div>
        <div class="right">
          <div class="right_item" v-for="i in item.fpiattaformas" :key="i.id">
            <div class="item_title">
              <span style="font-weight: bold">{{ i.proClassName }}</span>
              <i
                @click="i.type = !i.type"
                class="el-icon-arrow-up"
                v-if="i.type"
              ></i>
              <i
                @click="i.type = !i.type"
                class="el-icon-arrow-down"
                v-else
              ></i>
            </div>
            <div class="item_content" v-if="i.type">
              <p v-for="x in i.oaProTemList" :key="x.id">
                <i class="el-icon-document"></i>{{ x.templateName }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else style="text-align: center;margin-top: 30px;">您不是该用户的上级，无法查看用户权限</div>
  </div>
</template>

<script>
import { getUserProcessList, getUser } from "@/api/system/user";
export default {
  props: {
    userDetailId: {
      type: [Number, String],
    },
    personnelData: Object,
  },
  data() {
    return {
      dataList: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      getUserProcessList(this.userDetailId).then((res) => {
        this.dataList = res.rows;
        this.dataList.forEach((item) => {
          item.fpiattaformas.forEach((i) => {
            this.$set(i, "type", false);
          });
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.oa_table {
  display: flex;
  border: 1px solid #e0e7ed;
  margin-bottom: 20px;
  .left {
    width: 120px;
    flex-shrink: 0;
    background: #f2f2f2;
    padding-left: 12px;
    line-height: 100%;
    font-weight: bold;
    display: flex;
    align-items: center;
    border-right: 1px solid #e0e7ed;
  }
  .right {
    flex: 1;
    .right_item {
      .item_title {
        height: 50px;
        background: #f2f2f2;
        padding: 0 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e0e7ed;
        i {
          font-weight: bold;
          color: blue;
        }
      }
      .item_content {
        padding: 16px;
        display: flex;
        flex-wrap: wrap;

        p {
          margin-right: 40px;
        }
      }
    }
  }
}
</style>
