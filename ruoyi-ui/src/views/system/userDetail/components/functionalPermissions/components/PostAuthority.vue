<template>
  <div>
    <el-dialog
      title="查看岗位权限"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <p>岗位：{{ postName }}</p>
      <el-button type="text" @click="changeExpand">展开/折叠</el-button>
      <div style="width: 500px">
        <el-tree
          class="treeLine"
          :data="treeData"
          v-if="treeDataType"
          ref="menu"
          style="border: 1px solid #d7d7d7; padding: 16px 0"
          :props="defaultProps"
          :default-expand-all="defaultExpand"
        ></el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPost } from "@/api/system/post";

export default {
  props: {
    postData: Object,
  },
  data() {
    return {
      postName: "",
      defaultProps: {
        children: "children",
        label: "menuName",
      },
      treeDataType: true,
      defaultExpand: false,
      treeData: [],
      treeDataType: true,
      defaultExpand: false,
      dialogVisible: true,
    };
  },
  mounted() {
    console.log(this.postData);

    getPost(this.postData.postId).then((res) => {
      if (res.data.menuIds && res.data.menuIds.length > 0) {
        this.treeData = this.listToTree(res.data.menuIds)
      }
    });
    this.postName =
      this.postData.unitName +
      ">" +
      this.postData.deptName +
      ">" +
      this.postData.postName;
  },
  methods: {
    listToTree(list) {
      // 创建一个映射，用于快速查找节点的父级
      const map = {};
      list.forEach((item) => (map[item.menuId] = { ...item, children: [] }));

      // 遍历数组，将子节点添加到父节点的children数组中
      const tree = [];
      list.forEach((item) => {
        if (item.parentId === 0) {
          // 根节点直接添加到树中
          tree.push(map[item.menuId]);
          console.log(map[item.menuId]);
        } else {
          // 非根节点，找到其父节点，并添加到父节点的children数组中
          if(map[item.parentId]&&map[item.parentId].children){
            map[item.parentId].children.push(map[item.menuId]);
          }
         
        }
      });

      return tree;
    },
    changeExpand() {
      this.treeDataType = false;
      this.defaultExpand = !this.defaultExpand;
      setTimeout(() => {
        this.treeDataType = true;
      }, 100);
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>