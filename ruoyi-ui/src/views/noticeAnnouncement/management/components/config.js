export default {
  formColumns: [
    {
      label: "创建日期",
      prop: "sxCreateTime",
      type: "datePicker",
      dateType: "date",
      placeholder: "请选择创建日期",
    },
    {
      label: "类型",
      prop: "noticeType",
      type: "treeselect",
      options: [],
      filterable: true,
      placeholder: "请选择类型",
    },
    {
      label: "通知名称",
      prop: "noticeName",
      type: "input",
      placeholder: "请输入通知名称",
    },
  ],
  formColumnsDetail: [
    {
      label: "通知名称",
      prop: "noticeName",
      type: "input",
      maxlength:200,
      placeholder: "请输入通知名称",
    },
    {
      label: "类型",
      prop: "noticeType",
      type: "treeselect",
      options: [],
      filterable: true,
      placeholder: "请选择类型",
    },
    {
      type: "slot",
      slotName: "publisher",
    },
    {
      label: "发布公司",
      prop: "publishCompany",
      type: "select",
      filterable: true,
      options: [],
      dataProp:{value:'id',label:'companyShortName'},
      placeholder: "请选择发布公司",
    },
    {
      type: "slot",
      slotName: "content",
      span:24
    },
    {
      label: "附件上传",
      prop: "fileIds",
      type: "upload",
      span: 24,
      url: "/system/file/uploadNoticeFile",
    },
  ],
  columnsInit: Object.freeze([
    {
      label: "通知名称",
      key: "noticeName",
      minWidth: "150px",
    },
    { label: "类型", prop: "noticeTypeName", minWidth: "100px" },
    { label: "创建人", prop: "createrNickName", minWidth: "100px" },
    { label: "所属公司", prop: "companyShortName", minWidth: "100px" },
    { label: "创建时间", prop: "createTime", minWidth: "100px" },
    { label: "发布版本", prop: "version", minWidth: "100px" },
    { label: "操作", key: "operate", width: "120px" },
  ]),
  rules: Object.freeze({
    noticeName: [
      { required: true, message: "请输入通知名称", trigger: "blur" },
    ],
    noticeType: [
      { required: true, message: "请选择类型", trigger: "change" },
    ],
  }),
  columns: [],
};
