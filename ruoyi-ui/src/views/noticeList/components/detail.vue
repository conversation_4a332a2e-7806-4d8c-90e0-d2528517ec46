<template>
  <div class="p-5">
    <div class="text-center pt-8 pb-2 relative" style="border: 1px solid #ccc">
      <div class="text-3xl font-black text-black">{{ content.noticeName }}</div>
      <div class="text-base w-full text-center">
        类型
        <span class="font-extrabold text-black ml-2">{{
          content.noticeTypeName
        }}</span
        ><span
          style="
            width: 1px;
            height: 20px;
            background: #ccc;
            display: inline-block;
          "
          class="mx-6 mt-4"
        ></span>
        创建人
        <span class="font-extrabold text-black ml-2">{{
          content.createrNickName
        }}</span
        ><span
          style="
            width: 1px;
            height: 20px;
            background: #ccc;
            display: inline-block;
          "
          class="mx-6 mt-4"
        ></span>
        创建时间
        <span class="font-extrabold text-black ml-2">{{
          content.createTime
        }}</span>
      </div>
    </div>
    <Editor ref="myEditor" minHeight="500" :readOnly="true" :value="content.content"></Editor>
    <div
      class="pl-4 pb-10 cursor-pointer"
      style="border: 1px solid #ccc; border-top: none"
    >
      <div
        v-for="(item, index) in files"
        :key="index"
        @click="handlePreviews(item)"
        style="color: #02a7f0"
      >
        {{ item.fileName }}
      </div>
    </div>
    <InBody>
      <div
        class="flex justify-center fixed bottom-0 bg-white z-10 w-full pb-2"
        style="left: 130px"
      >
        <el-button @click="cancel">关闭</el-button>
      </div>
    </InBody>
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import config from "./config";
import privew from "@/mixin/privew";
import { noticeMainNotice,systemDataManageList, } from "@/api/notice/dataSet";
import { readDownloadHistory} from "@/api/notice/homePage";

export default {
  name: "NoticeListDetail",
  mixins: [privew],
  data() {
    return {
      ...config,
      content: {},
      files: [],
    };
  },

  mounted() {
    this.getInitForm();
  },
  methods: {
    async getInitForm() {
      let { rows } = await systemDataManageList();
      const flatData = XEUtils.toTreeArray(rows, {
        children: "fPiattaformas", // 指定子节点字段名
        clear: true,
      });
      const { data } = await noticeMainNotice(this.$route.params.id);
      flatData.forEach((item1) => {
        if (item1.id == data.noticeType) {
          data.noticeTypeName = item1.dataName;
        }
      });
      this.content = data;
      this.files = data.noticesFileList;
    },
    async handlePreviews(value){
      await readDownloadHistory({noticeId:this.content.id,rdType:2,version:this.content.version,fileName:value.fileName});
      this.handlePreview(value);
    },
    cancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .ql-toolbar {
  display: none;
}
::v-deep .ql-container {
  border-bottom: none;
}
</style>