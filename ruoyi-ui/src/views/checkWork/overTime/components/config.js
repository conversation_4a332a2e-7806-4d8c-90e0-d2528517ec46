export default {
  effectiveList: Object.freeze([
    { label: "生效", value: "0" },
    { label: "废弃", value: "1" },
    { label: "处理中", value: "2" },
  ]),
  effectiveObj:Object.freeze(
    {
      0: "生效",
      1: "废弃",
      2: "处理中",
    }
  ),
  pickerOptionsTime:Object.freeze({
    step: '00:30',
    start: '00:30',
    end: '23:30',
  }),
  pickerOptions: Object.freeze({
    shortcuts: [
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        },
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        },
      },
    ],
  }),
  rules: Object.freeze({
    workDate: [{ required: true, message: "请选择日期", trigger: "change" }],
    startTime: [{ required: true, message: "开始日期不能为空", trigger: "change" }],
    startTimePeriod: [{ required: true, message: "开始时间不能为空", trigger: "change" }],
    endTime: [{ required: true, message: "结束日期不能为空", trigger: "change" }],
    endTimePeriod: [{ required: true, message: "结束时间不能为空", trigger: "change" }],
    times: [{ required: true, message: "时长不能为空", trigger: "change" }],
    content: [{ required: true, message: "工作内容不能为空", trigger: "blur" }],
  }),
};
