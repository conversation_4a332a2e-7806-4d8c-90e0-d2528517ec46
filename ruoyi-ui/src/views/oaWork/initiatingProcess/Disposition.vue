<template>
  <div>
    <!-- 配置对话框 -->
    <el-dialog
      title="配置常用模板"
      :visible.sync="openDispositionDeilog"
      width="50%"
      append-to-body
      :before-close="closeMyUseDeilog"
    >
      <div>
        <span>全部流程</span>
        <div style="width: 100%; height: 500px; overflow: auto">
          <div style="width: 30%; float: left">
            <!--      todo 加入xxx表-树形结构-->
            <div style="width: 100%; float: left">
              <el-tree
                :data="deptOptions"
                :props="defaultProps"
                :load="loadNode"
                lazy
                
                ref="tree"
                :default-expand-all="false"
                @node-click="deilogHandleNodeClick"
              >
                <span class="custom-tree-node" slot-scope="{ node }">
                  <span>
                    <!-- <i class="el-icon-house"></i> -->
                    <i class="el-icon-folder-opened"></i>
                  </span>
                  <span>{{ node.label }}</span>
                </span>
              </el-tree>
            </div>
          </div>
          <div style="width: 70%; float: left">
            <el-table
              :data="deilogRoleTemplList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column label prop="selectFlag" width="40px">
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.selectFlag"
                    @change="(val) => handleCheckAllChange(val, scope.row, 2)"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column
                label="模板名称"
                align="center"
                prop="templateName"
              ></el-table-column>
              <el-table-column label="备注说明" align="center" prop="remark" />
             
            </el-table>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMyUse()">提交</el-button>
        <el-button @click="closeMyUseDeilog()">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  roleClassSelectList,
  getDataByRoleAndClassId,
  addMyUseulTempl,
} from "@/api/oa/processTemplate";
export default {
  data() {
    return {
      openDispositionDeilog: true,
      deptOptions: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      processKey: "",
      deilogRoleTemplList: [],
      ids: [],
      single: "",
      multiple: "",
      addMyUsualList: [],
    };
  },
  props: {
    companyId: String | Number,
  },
  mounted() {
    this.getTreeselect(this.companyId);
  },
  methods: {
    loadNode(node, resolve) {
      console.log(node);
      if (node.level === 0) {
        return resolve([{ name: "region" }]);
      }
      if (!node.data.children) return resolve([]);

      setTimeout(() => {
        const data = node.data.children || [];

        resolve(data);
      }, 500);
    },
    submitMyUse() {
      var data = {
        myUseulList: this.addMyUsualList,
      };
      addMyUseulTempl(data).then((response) => {});
      this.$emit("success");
      return;
      this.openDispositionDeilog = false;
      //获取公司列表
      this.getCompanyList();
      // this.getList();
      this.getTreeselect();
    },
    closeMyUseDeilog() {
      this.$emit("close");
    },
    /** 查询部门下拉树结构 */
    getTreeselect(id) {
      roleClassSelectList({companyId:id}).then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 节点单击事件
    deilogHandleNodeClick(data) {
      this.processKey = "";
      this.deiloggetByClassData(data.id);
    },
    //根据分类id获取模板列表接口
    deiloggetByClassData(id) {
      var data = {
        companyId: this.companyId,
        classificationId: id,
        templateName: this.processKey,
      };
      getDataByRoleAndClassId(data).then((response) => {
        this.deilogRoleTemplList = response.rows;
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleCheckAllChange(val, obj) {
      if (val) {
        var data = {
          companyId: this.companyId,
          classificationId: obj.classificationId,
          templateId: obj.id,
        };
        this.addMyUsualList.push(data);
      } else {
        var filter1 = this.addMyUsualList.filter((t) => t.templateId == obj.id);

        var index = this.addMyUsualList.indexOf(filter1[0]);
        if (index !== -1) {
          this.addMyUsualList.splice(index, 1);
        }
      }
    },
  },
};
</script>

<style>
</style>