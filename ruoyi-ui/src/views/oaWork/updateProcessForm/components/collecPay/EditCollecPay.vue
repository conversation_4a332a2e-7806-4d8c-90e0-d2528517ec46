<template>
  <div>
    <div class="data_content">
      <div style="display: flex">
        <p class="title2">修改详情：</p>
        <div>
          <div>项目名称：{{ name || this.data.projectName }}</div>
          <div>本项目的收付款信息发生编辑，请审核</div>
          <div style="font-weight: bold">修改明细</div>
          <div style="margin-bottom: 14px" v-for="item in editList" :key="item.code">
            <div>{{ item.name }}</div>
            <div class="table" style="margin-bottom: 12px" v-if="item.newRemark != item.oldRemark">
              <div class="left">
                <div style="background: #e4e4e4"></div>
                <div>备注</div>
              </div>

              <div class="center">
                <div style="background: #e4e4e4">修改前</div>
                <el-tooltip :content="item.oldRemark" placement="top">
                  <div style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      white-space: nowrap;
                    ">
                    {{ item.oldRemark }}
                  </div>
                </el-tooltip>
              </div>
              <div class="right">
                <div style="background: #e4e4e4">修改后</div>
                <el-tooltip :content="item.newRemark" placement="top">
                  <div style="
                      text-overflow: ellipsis;
                      overflow: hidden;
                      white-space: nowrap;
                    ">
                    {{ item.newRemark }}
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div class="table" v-for="(v, i) in item.list" :key="i">
              <div class="left">
                <div style="background: #e4e4e4; color: red">
                  {{
                    v.type == "del" ? "删除" : v.type == "add" ? "新增" : "修改"
                  }}
                </div>
                <div>付款{{ v.newData?v.newData.serialNum:v.oldData.serialNum }}-账户名称</div>
                <div>付款{{ v.newData?v.newData.serialNum:v.oldData.serialNum }}-账号</div>
                <div>付款{{ v.newData?v.newData.serialNum:v.oldData.serialNum }}-开户行</div>
                <div>收款{{ v.newData?v.newData.serialNum:v.oldData.serialNum }}-账户名称</div>
                <div>收款{{ v.newData?v.newData.serialNum:v.oldData.serialNum }}-账号</div>
                <div>收款{{ v.newData?v.newData.serialNum:v.oldData.serialNum }}-开户行</div>
              </div>
              <div class="center">
                <div style="background: #e4e4e4">修改前</div>
                <div>

                  {{
                    v.type == "add"
                      ? "-"
                      : v.type == "del"
                        ? v.oldData.orderBySerialNum[1]&&v.oldData.orderBySerialNum[1].accountName
                        : v.oldData.orderBySerialNum[1]&&v.oldData.orderBySerialNum[1].accountName
                  }}
                </div>
                <div>
                  {{
                    v.type == "add"
                      ? "-"
                      : v.type == "del"
                        ? v.oldData.orderBySerialNum[1]&&v.oldData.orderBySerialNum[1].accountNumber
                        : v.oldData.orderBySerialNum[1]&&v.oldData.orderBySerialNum[1].accountNumber
                  }}
                </div>
                <div>
                  {{
                    v.type == "add"
                      ? "-"
                      : v.type == "del"
                        ? v.oldData.orderBySerialNum[1]&&v.oldData.orderBySerialNum[1].bankOfDeposit
                        : v.oldData.orderBySerialNum[1]&&v.oldData.orderBySerialNum[1].bankOfDeposit
                  }}
                </div>
                <div>
                  {{
                    v.type == "add"
                      ? "-"
                      : v.type == "del"
                        ? v.oldData.orderBySerialNum[0]&&v.oldData.orderBySerialNum[0].accountName
                        : v.oldData.orderBySerialNum[0]&&v.oldData.orderBySerialNum[0].accountName
                  }}
                </div>
                <div>
                  {{
                    v.type == "add"
                      ? "-"
                      : v.type == "del"
                        ? v.oldData.orderBySerialNum[0]&&v.oldData.orderBySerialNum[0].accountNumber
                        : v.oldData.orderBySerialNum[0]&&v.oldData.orderBySerialNum[0].accountNumber
                  }}
                </div>
                <div>
                  {{
                    v.type == "add"
                      ? "-"
                      : v.type == "del"
                        ?v.oldData.orderBySerialNum[0]&&v.oldData.orderBySerialNum[0].bankOfDeposit
                        :v.oldData.orderBySerialNum[0]&&v.oldData.orderBySerialNum[0].bankOfDeposit
                  }}
                </div>
              </div>
              <div class="right">
                <div style="background: #e4e4e4">修改后</div>
                <div>
                  {{
                    v.type == "del"
                      ? "-"
                      : v.newData.orderBySerialNum[1]&&v.newData.orderBySerialNum[1].accountName
                  }}
                </div>
                <div>
                  {{
                    v.type == "del"
                      ? "-"
                      : v.newData.orderBySerialNum[1]&&v.newData.orderBySerialNum[1].accountNumber
                  }}
                </div>
                <div>
                  {{
                    v.type == "del"
                      ? "-"
                      : v.newData.orderBySerialNum[1]&&v.newData.orderBySerialNum[1].bankOfDeposit
                  }}
                </div>
                <div>
                  {{
                    v.type == "del"
                      ? "-"
                      : v.newData.orderBySerialNum[0]&&v.newData.orderBySerialNum[0].accountName
                  }}
                </div>
                <div>
                  {{
                    v.type == "del"
                      ? "-"
                      : v.newData.orderBySerialNum[0]&&v.newData.orderBySerialNum[0].accountNumber
                  }}
                </div>
                <div>
                  {{
                    v.type == "del"
                      ? "-"
                      : v.newData.orderBySerialNum[0]&&v.newData.orderBySerialNum[0].bankOfDeposit
                  }}
                </div>
              </div>
            </div>
          </div>
          <div style="font-weight: bold; margin: 12px 0">修改后总览</div>
          <div>常规业务收支款项（含助贷平台和资金方）</div>
          <el-table :data="tableData" style="width: 100%" border="" :cell-style="tableRowClassName">
            <el-table-column align="center" width="140" prop="itemName" label="事项">
              <template slot-scope="scope">
                <div style="font-weight: bold">
                  {{ namefilter(scope.row.itemName) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" width="200" prop="remark" label="备注" />
            <el-table-column align="center" width="50" prop="" label="序号" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div v-if="
                  scope.row.orderByItemName[0].orderBySerialNum[0].accountName
                ">
                  <div class="commodityDiscountAmount" :style="{
                    height: item.orderBySerialNum.length * 42 + 'px',
                    lineHeight: item.orderBySerialNum.length * 42 + 'px',
                  }" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                    {{ index + 1 }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" width="60" prop="" label="类型" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{
                      v.traderType == 1
                        ? "收"
                        : v.traderType == 0
                          ? "付"
                          : v.traderType == 9
                            ? "收/付"
                            : ""
                    }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="" label="账户名称" min-width="260" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{ v.accountName }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="" label="账号" min-width="260" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{ v.accountNumber }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="" label="开户行" min-width="260" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{ v.bankOfDeposit }}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin-top: 12px">技术服务方支出款项</div>
          <el-table :data="tableData2" style="width: 100%" border="" :cell-style="tableRowClassName">
            <el-table-column align="center" width="140" prop="itemName" label="事项">
              <template slot-scope="scope">
                <div style="font-weight: bold">
                  {{ namefilter(scope.row.itemName) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" width="200" prop="remark" label="备注" />
            <el-table-column align="center" width="50" prop="" label="序号" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">

                <div class="commodityDiscountAmount" :style="{
                  height: item.orderBySerialNum.length * 42 + 'px',
                  lineHeight: item.orderBySerialNum.length * 42 + 'px',
                }" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  {{ index + 1 }}

                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" width="60" prop="" label="类型" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{
                      v.traderType == 1
                        ? "收"
                        : v.traderType == 0
                          ? "付"
                          : v.traderType == 9
                            ? "收/付"
                            : ""
                    }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="" label="账户名称" min-width="260" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{ v.accountName }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="" label="账号" min-width="260" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{ v.accountNumber }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="" label="开户行" min-width="260" class-name="commodityDiscountAmount1">
              <template slot-scope="scope">
                <div class="commodityDiscountAmount" v-for="(item, index) in scope.row.orderByItemName" :key="index">
                  <div class="commodityDiscountAmount" v-for="(v, i) in item.orderBySerialNum" :key="i">
                    {{ v.bankOfDeposit }}
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div style="display: flex; margin-top: 16px">
        <p class="title2">
          <i style="color: red; margin-right: 5px">*</i>修改原因说明：
        </p>
        <el-input type="textarea" :rows="4" :disabled="disabled" @input="inputChange" show-word-limit maxlength="200"
          placeholder="请输入内容" v-model="textarea">
        </el-input>
      </div>
    </div>
  </div>
</template>

<script>
import { getReceiptAndPaymentInfo, getItemInfo } from "@/api/oa/deploy";

export default {
  props: {
    newData: Object,
    name: String,
    data: Array,
    disabled: Boolean,
    info: String,
  },
  data() {
    return {
      textarea: "",
      editList: [],
      tableData: [],
      tableData2: [],
      nameList: [],
    };
  },
  mounted() {
    getItemInfo().then((res) => {
      this.nameList = res.data;
    });
    console.log(this.data);
    this.editList = this.data;
    console.log(this.newData, '=-=');

    this.textarea = this.info;
    this.tableData =
      this.newData.oaProjectDeployReceiptAndPaymentInfoVoList[0].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType;
    this.tableData2 =
      this.newData.oaProjectDeployReceiptAndPaymentInfoVoList[1].oaProjectDeployReceiptAndPaymentInfo.orderByrRceiptAndPaymentType;
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      // 根据 row.special 字段的值返回类名
      return row &&
        row.orderByItemName &&
        !row.orderByItemName[0].orderBySerialNum[0].accountName
        ? "display:none"
        : "background:#fff";
    },
    namefilter(v) {
      let data = this.nameList.find((item) => {
        return item.code == v;
      });
      if (data) {
        return data.info;
      }
    },
    inputChange(e) {
      console.log(e);
      this.$emit("getText", e);
    },
  },
};
</script>

<style lang="less" scoped>
.data_content {
  margin: 0 auto;

  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    width: 132px;
    display: inline-block;
    text-align: right;
    flex-shrink: 0;
  }

  .table {
    display: flex;
    justify-content: space-between;
    border: 1px solid #cccccc;
    border-bottom: none;
    width: 800px;

    .left,
    .center,
    .right {
      width: 35%;
      flex-shrink: 0;

      >div {
        height: 30px;
        width: 100%;
        border-right: 1px solid #ccc;
        text-align: center;
        line-height: 30px;
        border-bottom: 1px solid #ccc;
        text-align: left;
        padding-left: 10px;
      }
    }

    .left div {
      font-weight: bold;
    }

    .left {
      width: 30%;
    }
  }
}

/deep/ .el-table__cell {
  padding: 0 !important;
}

/deep/ .commodityDiscountAmount1 .is-center .is-leaf .el-table__cell {
  line-height: 42px !important;
  height: 42px;
}

/deep/ .commodityDiscountAmount1 .cell {
  padding: 0 !important;

  .commodityDiscountAmount {
    border-bottom: 1px solid #ebeef5;
    line-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  div:last-child {
    border: none !important;
  }
}

.commodityDiscountAmount2 {
  border-bottom: 1px solid #ebeef5;
  line-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>