<template>
  <div>
    <MyTable :columns="columns" :source="dataList">
      <template #yewuList="{ record }">
        <el-tooltip
          class="item"
          effect="dark"
          :content="record.yewuList && record.yewuList.map((item) => item.name).join()"
          placement="top-start"
        >
          <div class="truncate ...">
            {{
              record.yewuList && record.yewuList.map((item) => item.name).join()
            }}
          </div>
        </el-tooltip>
      </template>
      <template #month="{ record }">
        <div
          v-for="(item, index) in record.achievementEnterQuarterList"
          :key="index"
          class="mb-5"
        >
          {{ item.month }}
        </div>
      </template>
      <template #totalIndex="{ record }">
        <div
          v-for="(item, index) in record.achievementEnterQuarterList"
          :key="index"
          class="mb-5"
        >
          {{ item.totalIndex }}
        </div>
      </template>
      <template #distributionIndex="{ record }">
        <div
          v-for="(item, index) in record.achievementEnterQuarterList"
          :key="index"
          class="mb-5"
        >
          {{ item.distributionIndex }}
        </div>
      </template>
      <template #extensionIndex="{ record }">
        <div
          v-for="(item, index) in record.achievementEnterQuarterList"
          :key="index"
          class="mb-5"
        >
          {{ item.extensionIndex }}
        </div>
      </template>
    </MyTable>
    <pagination
      v-show="total > 10"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
export default {
  name: "Table",
  props: {
    tableData: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
  },

  data() {
    return {
      columns: Object.freeze([
        {
          label: "项目名称",
          prop: "projectName",
          minWidth: "300px",
        },
        { label: "所属年度", prop: "year", width: "100px" },
        { label: "所属公司", prop: "companyShortName", width: "200px" },
        { label: "业务负责人", key: "yewuList", width: "150px" },
        { label: "月份", key: "month", width: "100px" },
        { label: "项目总业绩\n     (万元)", key: "totalIndex", width: "120px" },
        {
          label: "分配项目业绩\n      (万元)",
          key: "distributionIndex",
          width: "120px",
        },
        {
          label: "自拓项目业绩\n      (万元)",
          key: "extensionIndex",
          width: "120px",
        },
      ]),
      dataListInit: [],
      dataList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
    };
  },

  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.dataListInit = XEUtils.clone(this.tableData, true);

      this.getList();
    },
    getList() {
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
      const end = start + this.queryParams.pageSize;
      this.dataList = this.dataListInit.slice(start, end);
      this.total = this.dataListInit.length;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table {
  .el-table__body-wrapper {
    .el-table__cell {
      vertical-align: top;
      padding-bottom: 0px;
      .cell {
        padding-top: 10px;
      }
    }
  }
  .el-table__header-wrapper {
    table {
      thead {
        th {
          font-weight: bold;
          color: #333;
          // 换行
          .cell {
            white-space: pre-wrap;
          }
        }
      }
    }
  }
}
</style>