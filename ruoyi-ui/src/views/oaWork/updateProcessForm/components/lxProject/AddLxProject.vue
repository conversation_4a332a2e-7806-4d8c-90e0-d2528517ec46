<template>
  <div>
    <div class="data_content">
      <div style="display: flex">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          新增立项项目信息:
        </p>
        <div style="width: 600px">
          <div class="row" style="margin-top: 0">
            <div>
              <span>项目名称：</span>{{ myForm.projectForm.projectName }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>项目负责人：</span
              >{{ myForm.projectForm.projectUserName.join(",") }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>渠道方：</span>
              {{
                myForm.projectForm.channelType == 1
                  ? myForm.projectForm.channelFormName.join(",")
                  : myForm.projectForm.channelSide
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>渠道方类型：</span
              >{{ myForm.projectForm.channelType == 1 ? "内部" : "外部" }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>产品分类：</span
              >{{
                myForm.projectForm.xmglBusinessTypeList.length > 0
                  ? myForm.projectForm.xmglBusinessTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>项目类型：</span
              >{{
                myForm.projectForm.xmglProjectTypeList.length > 0
                  ? myForm.projectForm.xmglProjectTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>立项时间：</span>
              {{ $format(myForm.projectForm.projectDate, "yyyy-MM-dd") }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>项目描述：</span>
              {{ myForm.projectForm.projectDescribe }}
            </div>
          </div>
          <el-divider></el-divider>
          <div class="row">
            <div>
              <span>资金方联系人姓名：</span>{{ myForm.userform.fundName }}
            </div>
            <div>
              <span>资产方联系人姓名：</span>{{ myForm.userform.productName }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>资金方联系人电话：</span>{{ myForm.userform.fundTel }}
            </div>
            <div>
              <span>资产方联系人电话：</span>{{ myForm.userform.productTel }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>资金方联系人微信：</span>{{ myForm.userform.fundWx }}
            </div>
            <div>
              <span>资产方联系人微信：</span>{{ myForm.userform.productWx }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>资金方联系人所属部门：</span>{{ myForm.userform.fundDept }}
            </div>
            <div>
              <span>资产方联系人所属部门：</span
              >{{ myForm.userform.productDept }}
            </div>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div style="display: flex">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          新增项目名称信息:
        </p>
        <div style="width: 680px; margin-left: 30px" v-if="myForm.addProject">
          <div class="row" style="margin-top: 0">
            <div>
              <span>项目名称：</span>{{ myForm.addProject.projectName }}
            </div>
            <div><span>创建人：</span>{{ myForm.addProject.createBr }}</div>
          </div>
          <MyTable
            border
            :columns="columnsViewCompany"
            :source="myForm.projectForm.necessityCompanyList"
            :span-method="spanMethod"
          >
          </MyTable>
          <div
            v-if="
              myForm.projectForm.otherUnitList &&
              myForm.projectForm.otherUnitList.length
            "
          >
            <div class="my-2">其他</div>
            <MyTable
              border
              :columns="columnsViewCompany"
              :source="myForm.projectForm.otherUnitList"
              :show-header="false"
            >
            </MyTable>
          </div>
          <div class="row">
            <div>
              <span>产品分类：</span
              >{{
                myForm.projectForm.xmglBusinessTypeList.length > 0
                  ? myForm.projectForm.xmglBusinessTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
            <div>
              <span>项目类型：</span
              >{{
                myForm.projectForm.xmglProjectTypeList.length > 0
                  ? myForm.projectForm.xmglProjectTypeList
                      .map((item) => item.typeName)
                      .join(",")
                  : ""
              }}
            </div>
          </div>
          <div class="row">
            <div>
              <span>启用状态：</span
              >{{ myForm.addProject.isEnable == "Y" ? "正常" : "停用" }}
            </div>
          </div>
        </div>
        <div v-else>本次项目立项未新增项目名称</div>
      </div>
      <el-divider></el-divider>
      <div style="display: flex">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          新增公司信息:
        </p>
        <div style="width: 600px" v-if="myForm.addCompanyList.length > 0">
          <div v-for="(item, index) in myForm.addCompanyList" :key="index">
            <div class="row" style="margin-top: 0">
              <div><span>公司名称：</span>{{ item.companyName }}</div>
              <div>
                <span>公司类型：</span
                >{{
                  item.companyTypeMappingList
                    .map((item) => item.dictLabel)
                    .join(",")
                }}
              </div>
            </div>
            <div class="row">
              <div><span>公司简称：</span>{{ item.companyShortName }}</div>
              <div>
                <span>产品分类：</span
                >{{
                  item.companyBusinessTypeMappingList &&
                  item.companyBusinessTypeMappingList.length > 0
                    ? item.companyBusinessTypeMappingList
                        .map((item) => item.dictLabel)
                        .join(",")
                    : ""
                }}
              </div>
            </div>
            <div class="row">
              <div>
                <span>内部公司：</span>{{ item.isInside == 0 ? "否" : "是" }}
              </div>
              <div>
                <span>启用状态：</span>{{ item.status == 0 ? "启用" : "停用" }}
              </div>
            </div>
            <el-divider></el-divider>
          </div>
        </div>
        <div v-else>本次项目立项未新增公司信息</div>
      </div>
      <div style="display: flex; margin-top: 16px">
        <p class="title2" style="flex-shrink: 0; margin-top: 0">
          <i style="color: red; margin-right: 5px">*</i>新增立项项目说明：
        </p>
        <el-input
          type="textarea"
          :rows="4"
          :disabled="disabled"
          @input="inputChange"
          show-word-limit
          maxlength="200"
          style="width: 500px"
          placeholder="请输入内容"
          v-model="textarea"
        >
        </el-input>
      </div>
    </div>
  </div>
</template>
      
      <script>
import { getDicts } from "@/api/system/dict/data";
import tableSpanMethod from "@/mixin/tableSpanMethod";
export default {
  props: {
    data: Object,
    disabled: Boolean,
    info: String,
  },
  mixins: [tableSpanMethod],
  data() {
    return {
      columnsViewCompany: Object.freeze([
        { label: "类型", prop: "unitType", width: 150 },
        { label: "公司名称", prop: "unitName", minWidth: 250 },
        { label: "公司简称", prop: "unitShortName", minWidth: 150 },
        { label: "占比", prop: "proportion", width: 100 },
      ]),
      myForm: {},
      companytypeList: [],
      paramsLabel: Object.freeze(["担保公司", "资产方", "资金方"]),
      textarea: "",
    };
  },

  mounted() {
    this.textarea = this.info;
    this.myForm = JSON.parse(JSON.stringify(this.data));
    this.hanleMyForm();
    console.log(this.myForm, "1");
  },
  methods: {
    spanMethod({ row, column, rowIndex, columnIndex }) {
      return this.objectSpanMethod({
        row,
        column,
        rowIndex,
        columnIndex,
        data: this.myForm.projectForm.necessityCompanyList,
        sortArr: ["unitType"],
      });
    },
    inputChange(e) {
      console.log(e);
      this.$emit("getText", e);
    },
    hanleMyForm() {
      getDicts("company_type").then((res) => {
        this.companytypeList = res.data;
        this.myForm.projectForm.necessityCompanyList =Object.values(this.myForm.projectForm.tableList).flat();
        this.myForm.projectForm.necessityCompanyList.forEach((item) => {
          item.unitType = this.$store.state.data.KV_MAP.select_name[item.unitType];
          item.proportion = item.proportion;
          item.unitShortName = item.unitShortName || `暂不确定${item.unitType}`;
        });

        const otherCompanyList = this.companytypeList.filter(
          (item) => !this.paramsLabel.includes(item.dictLabel)
        );
        this.myForm.projectForm.otherUnitList.forEach((item) => {
          item.proportion = item.proportion;
          item.unitType = otherCompanyList.filter(
            (item1) => item1.dictCode == item.unitTypeId
          )[0].dictLabel;
          item.unitShortName = item.unitShortName || `暂不确定${item.unitType}`;
        });
        this.myForm.projectForm.projectTypeListShow =
          this.myForm.projectForm.projectTypeList
            ?.map((item) => item.typeName)
            ?.join(",");

        this.myForm = JSON.parse(JSON.stringify(this.myForm));
        console.log(this.myForm, "2");
      });
    },
  },
};
</script>
      
<style lang="less" scoped>
.data_content {
  margin: 0 auto;
  .title2 {
    font-weight: bold;
    margin-bottom: 10px;
    margin-top: 16px;
    width: 140px;
    text-align: right;
  }
}
.row {
  display: flex;
  margin-top: 20px;
  justify-content: space-between;
  div {
    span {
      display: inline-block;
      font-weight: bold;
      width: 175px;
      text-align: right;
    }
  }
}
.left,
.right {
  width: 50%;
  div {
    margin-top: 16px;
    span {
      font-weight: bold;
      display: inline-block;
      width: 210px;
      text-align: right;
    }
  }
}
</style>