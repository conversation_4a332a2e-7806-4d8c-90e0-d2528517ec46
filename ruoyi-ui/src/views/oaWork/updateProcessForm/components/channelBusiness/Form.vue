<template>
  <div>
    <ChannelBusiness
      :detailId="channelBusinessDetailId"
      process
    />
  </div>
</template>

<script>
import ChannelBusiness from "@/views/badSystem/channelBusiness/View/index.vue";
export default {
  name: "Form",
  props: {
    channelBusinessDetailId: {
      type: [Number, String],
      required: true,
    },
  },
  components: {
    ChannelBusiness,
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
};
</script>
