<template>
  <div class="p-5">
    <el-descriptions title="" :column="1" :colon="false" direction="vertical">
      <el-descriptions-item label="委外分案">{{
        myForm.outsourcedProjectNumber
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <el-descriptions title="" :column="4" :colon="false" direction="vertical">
      <el-descriptions-item label="案件状态">
        <el-tag>{{ caseStatusObj[myForm.caseStatus] }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="审批状态">{{
        approveStatusObj[myForm.status]
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <el-descriptions title="" :column="4" :colon="false" direction="vertical">
      <el-descriptions-item
        ><template slot="label">
          <span style="color: red">*</span> 债权机构 </template
        >{{ myForm.creditorInstitutionsName }}</el-descriptions-item
      >
      <el-descriptions-item
        ><template slot="label">
          <span style="color: red">*</span> 受托机构 </template
        >{{ myForm.trusteeInstitutionName }}</el-descriptions-item
      >
      <el-descriptions-item
        ><template slot="label">
          <span style="color: red">*</span> 委案开始日期 </template
        >{{ myForm.outsourcedStart }}</el-descriptions-item
      >
      <el-descriptions-item
        ><template slot="label">
          <span style="color: red">*</span> 委案截至日期 </template
        >{{ myForm.outsourcedEnd }}</el-descriptions-item
      >
    </el-descriptions>
    <el-divider></el-divider>
    <div class="text-lg font-bold">
      <span style="color: red">*</span> 分案策略
    </div>
    <MyTable
      :columns="columnsCase"
      :source="myForm.outsourcedProjectStrategyList"
      :showIndex="true"
    >
      <template #caseRange="{ record }">
        <el-tag
          v-for="(item, index) in record.caseRange"
          :key="index"
          type="info"
          style="margin-right: 8px"
          >{{ item }}</el-tag
        >
      </template>
      <template #allocationRulesLabel="{ record }">
        <el-tag type="info">{{ record.allocationRulesLabel }}</el-tag>
      </template>
      <template #isRepaymentLabel="{ record }">
        <el-tag type="info">{{ record.isRepaymentLabel }}</el-tag>
      </template>
    </MyTable>

    <el-divider></el-divider>
    <el-descriptions title="" :column="2" :colon="false" direction="vertical">
      <el-descriptions-item label="案件数量">{{
        myForm.totalQuantity
      }}</el-descriptions-item>
      <el-descriptions-item label="案件金额">{{
        myForm.totalAmountMoney
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <div class="text-lg font-bold">实际分案合计</div>
    <el-divider></el-divider>
    <el-descriptions title="" :column="4" :colon="false" direction="vertical">
      <el-descriptions-item label="案件总额(实际)">{{
        myForm.realTotalAmountMoney
      }}</el-descriptions-item>
      <el-descriptions-item label="案件总数(实际)">{{
        myForm.realTotalQuantity
      }}</el-descriptions-item>
      <el-descriptions-item label="委后协助还款总额">{{
        myForm.detailRepaymentAmount
      }}</el-descriptions-item>
      <el-descriptions-item label="委后协助还款笔数">{{
        myForm.detailQuantity
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider></el-divider>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="案件明细" name="caseDetailed"
        ><MyTable
          ref="caseDetailed"
          :columns="columnsAssetManagement"
          :source="myForm.assetManagementList"
          :showIndex="true"
        >
        </MyTable
      ></el-tab-pane>
      <el-tab-pane label="委后还款明细" name="repaymentDetails"
        ><MyTable
          ref="repaymentDetails"
          :columns="columnsOutsourcedRepayment"
          :source="myForm.outsourcedRepaymentDetailList"
          :showIndex="true"
        >
        </MyTable
      ></el-tab-pane>
    </el-tabs>

    <div class="w-full h-28"></div>
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
        v-show="!proccess"
      >
        <el-button
          @click="initiate"
          type="primary"
          v-show="
            myForm.status != 2 && myForm.status != 3 && myForm.caseStatus == '1'
          "
          >发起审批流程</el-button
        >
        <!-- <el-button type="primary" v-show="myForm.status == 2">撤回</el-button> -->
        <el-button type="primary" v-show="myForm.status == 3&&myForm.caseStatus!=4" @click="recall"
          >召回</el-button
        >
        <el-button type="primary" v-show="myForm.status == 3" @click="cancel"
          >作废</el-button
        >
        <!-- <el-button
          @click="selectCompanyType = true"
          type="primary"
          v-show="myForm.status != 2 && myForm.status != 3"
          >重新发起</el-button
        > -->
        <el-button @click="cancels">取 消</el-button>
      </div>
    </InBody>
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import {
  getOutsourcedProjectById,
  getBlOutsourcedProjectFlowController,
  updateProcessOutsourced,
} from "@/api/badSystem/outsourced";

import config from "./components/config";
export default {
  name: "OutsourcedDetailView",
  props: {
    detailId: {
      type: [String, Number],
      default: "",
    },
    proccess: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ...config,
      myForm: {
        outsourcedProjectStrategyList: [],
        assetManagementList: [],
        outsourcedRepaymentDetailList: [],
      },
      activeName: "caseDetailed",
      myDetailId: "",
      selectCompanyType: false,
    };
  },
  computed: {},
  mounted() {
    this.myDetailId = this.detailId || this.$route.params.id;
    this.init();
  },
  methods: {
    init() {
      this.getDetail();
    },
    async getDetail() {
      const { data } = await getOutsourcedProjectById(this.myDetailId);
      this.myForm = data;
      this.handlerForm();
    },
    handlerForm() {
      this.myForm.outsourcedProjectStrategyList.forEach((item) => {
        item.amountMoney = item.amountMoney
          ? item.amountMoney.toLocaleString("zh-CN") + "元"
          : "";
        item.quantity = item.quantity
          ? item.quantity.toLocaleString("zh-CN") + "笔"
          : "";
        item.allocationRulesLabel =
          this.allocationRulesObj[item.allocationRules];
        item.isRepaymentLabel = this.isRepaymentObj[item.isRepayment];
      });
      const addYuan = [
        "totalAmountMoney",
        "realTotalAmountMoney",
        "detailRepaymentAmount",
      ];
      const addBi = ["totalQuantity", "realTotalQuantity", "detailQuantity"];
      addYuan.forEach((item) => {
        this.myForm[item] = this.myForm[item]
          ? this.myForm[item].toLocaleString("zh-CN") + "元"
          : "";
      });
      addBi.forEach((item) => {
        this.myForm[item] = this.myForm[item]
          ? this.myForm[item].toLocaleString("zh-CN") + "笔"
          : "";
      });
      this.myForm.assetManagementList.forEach((item) => {
        item.remainingDue = item.remainingDue?.toLocaleString("zh-CN");
      });
      this.myForm.outsourcedRepaymentDetailList.forEach((item) => {
        item.repaymentAmount = item.repaymentAmount?.toLocaleString("zh-CN");
      });
    },
    handleClick() {
      this.$nextTick(() => {
        this.$refs.repaymentDetails.doLayout();
        this.$refs.caseDetailed.doLayout();
      });
    },
    initiate() {
      if (this.myForm.processId && this.myForm.status == 6) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: this.myForm.processId,
            myActiviteType: true,
          },
        });
      } else if (this.myForm.processId) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: this.myForm.processId,
            businessId: this.myForm.processId,
            myActiviteType: true,
          },
        });
      } else {
        this.selectCompanyType = true;
      }
    },
    submitCompany(e) {
      getBlOutsourcedProjectFlowController({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              outsourcedDetailId: this.myDetailId,
            },
          });
        }
      });
    },
    closeCompany() {
      this.selectCompanyType = false;
    },
    async recall() {
      const params = {
        id: this.myDetailId,
        caseStatus: "4",
      };
      await updateProcessOutsourced(params);
      this.$modal.msgSuccess("操作成功");
      this.$router.go(-1);
    },
    async cancel() {
      const params = {
        id: this.myDetailId,
        status: "5",
        caseStatus: "8",
      };
      await updateProcessOutsourced(params);
      this.$modal.msgSuccess("操作成功");
      this.$router.go(-1);
    },
    cancels() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="less" scoped>
</style>
