<template>
  <div>
    <MyTable
      :columns="columns"
      :showIndex="true"
      :source="configList"
      :queryParams="queryParams"
    >
    </MyTable>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getRepaymentDetailList } from "@/api/badSystem/repaymentDetailsAfter";
export default {
  name: "DetailsOfRepaymentBefore",
  components: {},
  data() {
    return {
      columns: Object.freeze([
        { label: "分案批次", prop: "outsourcedProjectNumber", minWidth: "150px" },
        { label: "还款金额", prop: "repaymentAmount", minWidth: "150px" },
        { label: "还款时间", prop: "repaymentTime", minWidth: "150px" },
        { label: "备注", prop: "remark", minWidth: "450px" },
      ]),
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      configList: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    async getList() {
      const { rows, total } = await getRepaymentDetailList({...this.queryParams,loanBill:this.$route.query.promissory});
      this.handleConfigList(rows);
      this.total = total;
    },
    handleConfigList(rows) {
      const addUnit = ["repaymentAmount"];
      rows.forEach((item) => {
        addUnit.forEach((item1) => {
          item[item1] =
            item[item1] && item[item1]
              ? item[item1].toLocaleString() + "元"
              : "-";
        });
      });
      this.configList = rows;
    },
  },
};
</script>
