<template>
  <div class="p-5">
    <MyForm
      ref="form"
      v-model="myForm"
      :columns="formColumnsDetail"
      formType="form"
      :rules="rules"
      labelWidth="140px"
    >
      <template #businessReconciliation>
        <el-form-item label="业务对账单" class="mt-4">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="selectAccountStatement"
            >选择对账单</el-button
          >
          <MyTable
            :columns="columnsAccountStatement"
            :source="myForm.financialSettlementReconciliationList"
            :showIndex="true"
            class="my-4"
          ></MyTable>
        </el-form-item>
      </template>
      <template #paymentDetails>
        <el-form-item label="付款明细" prop="paymentDetails" class="mt-4">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            >添加一行</el-button
          >
        </el-form-item>
        <MyTable
          style="margin-left: 100px"
          class="tables"
          :columns="columnsPayment"
          :source="myForm.financialSettlementPaymentList"
        >
          <template #h_paymentDate
            ><span style="color: red">*</span> 日期
          </template>
          <template #h_paymentAmount
            ><span style="color: red">*</span> 金额
          </template>

          <template #paymentDate="{ record, index }">
            <el-form-item
              label-width="0px"
              :prop="`financialSettlementPaymentList.${index}.paymentDate`"
              :rules="[
                {
                  required: true,
                  message: '请选择日期',
                  trigger: 'change',
                },
              ]"
            >
              <el-date-picker
                v-model="record.paymentDate"
                align="right"
                type="date"
                placeholder="选择日期"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </template>
          <template #paymentAmount="{ record, index }">
            <el-form-item
              label-width="0px"
              :prop="`financialSettlementPaymentList.${index}.paymentAmount`"
              :rules="[
                {
                  required: true,
                  message: '请输入金额',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input v-model.trim="record.paymentAmount"></el-input>
            </el-form-item>
          </template>

          <template #remark="{ record }">
            <el-form-item label-width="0px">
              <el-input v-model.trim="record.remark"></el-input>
            </el-form-item>
          </template>

          <template #paymentState="{ record }">
            <el-form-item label-width="0px">
              <el-select
                v-model="record.paymentState"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in paymentStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>

          <template #delete="{ record }">
            <el-button @click="deletes(record)" type="text" style="color: red"
              >删除</el-button
            >
          </template>
        </MyTable>
      </template>
    </MyForm>
    <AccountDialog v-model="openAccountStatement" :settlementInstitutionId="myForm.settlementEntity" @on-save-success="getList" />
    <InBody>
      <div
        class="text-center fixed bottom-0 bg-white z-10 pb-2"
        style="width: calc(100% - 260px); left: 260px"
      >
        <el-button @click="onSave" type="primary">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </InBody>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import config from "./components/config";
import { getMechanismList } from "@/api/badSystem/organizationalManagement";
import {
  addFinancialSettlement,
  getFinancialSettlementById,
  updateFinancialSettlement,
} from "@/api/badSystem/financialSettlement";
import { floatAdd, floatSub } from "@/utils";

import AccountDialog from "./components/AccountDialog.vue";
export default {
  name: "FinancialSettlementDetail",
  components: { AccountDialog },
  data() {
    return {
      ...config,
      myForm: {
        status: "1",
        financialSettlementReconciliationList: [],
        financialSettlementPaymentList: [],
      },
      openAccountStatement: false,
    };
  },
  watch: {
    "myForm.financialSettlementPaymentList": {
      handler(val) {
        this.$set(
          this.myForm,
          "paymentDetails",
          val && val.length ? "true" : undefined
        );
        this.getSettlementAmount(val);
      },
      immediate: true,
      deep: true,
    },
    "myForm.settlementAmount": {
      handler(val) {
        this.getUnsettledAmount();
      },
    },
    "myForm.totalAmountCollected": {
      handler(val) {
        this.getUnsettledAmount();
      },
    },
  },
  computed: {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getOrganizational();
      this.getMyForm();
    },
    async getMyForm() {
      if (this.$route.params.id != "add") {
        const { data } = await getFinancialSettlementById(
          this.$route.params.id
        );
        this.myForm = data;
        this.handlerMyForm();
      }
    },
    handlerMyForm() {
      this.myForm.financialSettlementReconciliationList.forEach((item) => {
        item.totalAmountCollectedLabel =
          item.totalAmountCollected?item.totalAmountCollected.toLocaleString("zh-CN") + "元":"";
      });
      this.myForm.financialReceiptsAmountLabel =
        this.myForm.financialReceiptsAmount?this.myForm.financialReceiptsAmount.toLocaleString("zh-CN") + "元":"";
    },
    async getOrganizational() {
      const { rows } = await getMechanismList();
      this.formColumnsDetail[1].options = rows;
    },
    getList(value) {
      this.myForm.financialSettlementReconciliationList = XEUtils.clone(
        value,
        true
      );
      this.myForm.financialSettlementReconciliationList.forEach((item) => {
        item.reconciliationId = item.id;
      });
      const financialReceiptsAmount = value.reduce(
        (sum, current) => floatAdd(sum, current.totalAmountCollected),
        0
      );
      const financialReceiptsAmountLabel =
        financialReceiptsAmount?financialReceiptsAmount.toLocaleString("zh-CN") + "元":'';
      this.$set(
        this.myForm,
        "financialReceiptsAmount",
        financialReceiptsAmount
      );
      this.$set(
        this.myForm,
        "financialReceiptsAmountLabel",
        financialReceiptsAmountLabel
      );
    },
    selectAccountStatement() {
      if(this.myForm.settlementEntity){
        this.openAccountStatement = true;
      }else{
        this.$message.warning("请选择结算主体");
      }
    },
    handleAdd() {
      this.myForm.financialSettlementPaymentList.push({
        paymentDate: "",
        paymentAmount: "",
        beizhu: "",
        paymentState: "",
      });
    },
    deletes(record) {
      this.myForm.financialSettlementPaymentList =
        this.myForm.financialSettlementPaymentList.filter(
          (item) => item.xh !== record.xh
        );
    },
    getSettlementAmount(value) {
      const settlementAmount = value.reduce(
        (sum, current) => floatAdd(sum, current.paymentAmount),
        0
      );
      const settlementAmountLabel = value.length
        ? settlementAmount?.toLocaleString("zh-CN") + "元"
        : undefined;
      this.$set(this.myForm, "settlementAmount", settlementAmount);
      this.$set(this.myForm, "settlementAmountLabel", settlementAmountLabel);
    },
    getUnsettledAmount(value) {
      const unsettledAmount = floatSub(
        this.myForm.totalAmountCollected,
        this.myForm.settlementAmount
      );
      const unsettledAmountLabel =
        unsettledAmount?.toLocaleString("zh-CN") + "元";

      this.$set(this.myForm, "unsettledAmount", unsettledAmount);
      this.$set(this.myForm, "unsettledAmountLabel", unsettledAmountLabel);
    },
    onSave() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (this.$route.params.id != "add") {
            await updateFinancialSettlement(this.myForm);
          } else {
            await addFinancialSettlement(this.myForm);
          }

          this.$message.success("操作成功");
          this.$router.go(-1);
        }
      });
    },
    cancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="less" scoped>
.tables {
  ::v-deep .el-form-item__error {
    position: absolute;
    top: 23px;
  }
}
</style>
