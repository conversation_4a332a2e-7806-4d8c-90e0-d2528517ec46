<template>
  <div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="handleClose"
    >
    <el-input
        placeholder="请输入目录名称"
        v-model="filterText">
      </el-input>
      <el-tree
        class="filter-tree"
        :data="treeData"
        :props="defaultProps"
        default-expand-all
        @node-click="handleNodeClick"
        ref="tree"
        :filter-node-method="filterNode"
      >
      </el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="move(2)">移动到该目录</el-button>
        <!-- <el-button type="primary" @click="move(1)">复制到该目录</el-button> -->
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  archivistMainList,
  moveOrCopy,
} from "@/api/directoryArchives/directoryArchives";
export default {
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  props: {
    multipleSelection: Array,
    treeData: Array,
  },
  data() {
    return {
      dialogVisible: true,
      moveId: null,
      defaultProps: {
        children: "fpiattaformas",
        label: "catalogueName",
      },
      filterText:undefined
    };
  },
  methods: {
    handleNodeClick(data) {
      console.log(data);
      this.moveId = data.id;
    },
    filterNode(value, data) {
      console.log(data,11)
      if (!value) return true;
      return data.catalogueName.indexOf(value) !== -1;
    },
    move(type) {
      if (!this.moveId) {
        this.$message.warning("请选择目录");
        return;
      }
      let list = [];
      this.multipleSelection.map((item) => {
        list.push({
          archivistId: item.id,
          catalogueId: item.catalogueId,
        });
      });
      let params = {
        targetCataId: this.moveId,
        operate: type,
        oldArchivistCatalogueMiddleVo: list,
      };
      moveOrCopy({ ...params }).then((res) => {
        if (res.code == 200) {
          this.$message.success("操作成功");
          this.$emit("success");
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style>
</style>