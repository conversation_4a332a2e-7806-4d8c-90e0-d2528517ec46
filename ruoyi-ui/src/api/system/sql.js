import request from '@/utils/request'

// 查询外部系统平台数据查询sql配置列表
export function listSql(query) {
  return request({
    url: '/system/sql/list',
    method: 'get',
    params: query
  })
}

// 查询外部系统平台数据查询sql配置详细
export function getSql(id) {
  return request({
    url: '/system/sql/' + id,
    method: 'get'
  })
}

// 新增外部系统平台数据查询sql配置
export function addSql(data) {
  return request({
    url: '/system/sql',
    method: 'post',
    data: data
  })
}

// 修改外部系统平台数据查询sql配置
export function updateSql(data) {
  return request({
    url: '/system/sql',
    method: 'put',
    data: data
  })
}

// 删除外部系统平台数据查询sql配置
export function delSql(id) {
  return request({
    url: '/system/sql/' + id,
    method: 'delete'
  })
}
