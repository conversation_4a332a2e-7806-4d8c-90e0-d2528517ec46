import request from "@/utils/request";

export function getTemConfig(data) {
    return request({
        url: "/system/template/getTemConfig",
        method: "post",
        data

    });
}
export function getUnit() {
    return request({
        url: "/system/template/getUnit",
        method: "get",

    });
}
export function getFunct() {
    return request({
        url: "/system/template/getFunct",
        method: "get",

    });
}
export function getModuleType(type) {
    return request({
        url: "/system/module/" + type,
        method: "get",

    });
}
export function templateList(data) {
    return request({
        url: "/system/template/list",
        method: "get",
        params: data
    });
}
export function getTempDetail(id) {
    return request({
        url: "/system/template/" + id,
        method: "get",

    });
}
export function delTemp(id) {
    return request({
        url: "/system/template/" + id,
        method: "delete",

    });
}
export function addProjectParameter(data) {
    return request({
        url: "/project/parameter",
        method: "post",
        data,
    });
}
export function addTemplate(data) {
    return request({
        url: "/system/template",
        method: "post",
        data,
    });
}
export function editTemplate(data) {
    return request({
        url: "/system/template",
        method: "put",
        data,
    });
}
export function authTempUser(data) {
    return request({
        url: "/system/template/authTempUser	",
        method: "post",
        data,
    });
}
