import request from "@/utils/request";

export function punishmentList(params) {
  return request({
    url: "/reward/punishment/list",
    method: "get",
    params,
  });
}
export function addUpdatePunishment(data) {
  return request({
    url: '/reward/punishment',
    method: 'post',
    data
  })
}
export function punishmentId(id) {
  return request({
    url: "/reward/punishment/"+id,
    method: "get",
  });
}
export function deletePunishment(id) {
  return request({
    url: "/reward/punishment/"+id,
    method: "delete",
  });
}
export function voidRewardPunishment(data) {
  return request({
    url: '/reward/punishment/voidRewardPunishment',
    method: 'put',
    data
  })
}
export function getRewardsPunishment(params) {
  return request({
    url: "/informationFlowController/getRewardsPunishment",
    method: "get",
    params,
  });
}
export function getRewardsPunishmentList(params) {
  return request({
    url: "informationFlowController/getRewardsPunishmentList",
    method: "get",
    params,
  });
}
export function getProcessIdPunishment(params) {
  return request({
    url: "/reward/punishment/getProcessId",
    method: "get",
    params,
  });
}
export function processRewardPunishment(data) {
  return request({
    url: '/reward/punishment/processRewardPunishment',
    method: 'post',
    data
  })
}
export function unpassRewardPunishment(data) {
  return request({
    url: '/reward/punishment/unpassRewardPunishment',
    method: 'post',
    data
  })
}
export function passRewardPunishment(data) {
  return request({
    url: '/reward/punishment/passRewardPunishment',
    method: 'post',
    data
  })
}
export function punishmentHandleId(id) {
  return request({
    url: "/reward/punishment/handle/"+id,
    method: "get",
  });
}
export function punishmentQueryUserInfoList(params) {
  return request({
      url: "/reward/punishment/queryUserInfoList",
      method: "get",
      params,
  });
}
