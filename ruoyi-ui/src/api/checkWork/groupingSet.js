import request from "@/utils/request";

export function groupList(params) {
    return request({
        url: "/day/log/group/list",
        method: "get",
        params,
    });
}


export function groupAdd(data) {
    return request({
        url: '/day/log/group',
        method: 'post',
        data
    })
}
export function groupEdit(data) {
    return request({
        url: '/day/log/group',
        method: 'put',
        data
    })
}
export function groupDel(id) {
    return request({
        url: '/day/log/group/' + id,
        method: 'delete',
    })
}
export function groupDetail(id) {
    return request({
        url: '/day/log/group/' + id,
        method: 'get',
    })
}
export function groupUserInfoList(params) {
    return request({
        url: "/day/log/queryUserInfoList",
        method: "get",
        params,
    });
}

