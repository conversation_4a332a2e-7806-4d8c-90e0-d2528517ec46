package com.ruoyi.nocode.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalDetailsBo implements Serializable {

    //节点名称
    private String taskNodeName;

    //处理人
    private String detailName;

    //接收时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    //处理时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date detailTime;

    //状态
    private String status;

    //耗时
    private String elapsedTime;

    private String csnum;
}
