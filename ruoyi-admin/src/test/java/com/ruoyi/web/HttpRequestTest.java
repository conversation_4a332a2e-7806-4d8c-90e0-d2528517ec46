package com.ruoyi.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.RequestCodeEnums;

import com.ruoyi.common.qiyeVX.AccessTokenUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.web.service.EncryptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.ruoyi.core.cdlb.domain.CdlbFiles;
import org.ruoyi.core.tool.DataRequestTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @className: HttpRequestTest
 * @author: zuo
 * @description: HTTP 请求测试模拟伪代码例子
 **/
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class HttpRequestTest {

    @Test
    public void getData() {
        Map<String, String> map = new HashMap<>();
        // Key 为 表名.字段名 value 为值
        map.put("student.name", "张三");
        map.put("teacher.name", "李老师");

        // hashMap  为 请求响应体
        HashMap<String, String> hashMap = httpSend(map);


    }

    /**
     * http发送
     *
     * @param map 地图
     * @return {@link Object}
     */
    public static HashMap<String, String> httpSend(Map<String, String> map) {
        return Maps.newHashMap();
    }

    /**
     * 此流程是我们正常访问的定时任务模拟代码 （仅供参考）
     */

    @Resource
    private EncryptService encryptService;

    @Test
    public void getOtherRequestData() {

        // 查询SQL表总查询出来的数据
        HashMap<String, Object> mapBySql = Maps.newHashMap();
        // 获取配置 SQL 表中请求码
        String requestCode = String.valueOf(mapBySql.get("requestCode"));
        // 反射请求方法
//        String methodName = RequestCodeEnums.getMethodName(requestCode);
        RequestCodeEnums requestCodeEnums = RequestCodeEnums.getRequestCodeEnums(requestCode);
        String data = (String) mapBySql.get("data");
        //通过 requestCodeEnums.getKeyCode()  redis 查询回来的模拟数据
        String keyAlgorithm = (String) mapBySql.get("keyAlgorithm");
        String charSet = (String) mapBySql.get("charSet");
        String useType = (String) mapBySql.get("useType");
        String workType = (String) mapBySql.get("workType");

        Map<String, Object> map = encryptService.commonToEncrypt(mapBySql.get("pushCode") + ":1", requestCode, data);

        JSONObject jsonObject = new JSONObject(map);
        // 校验机密结果
        if ("0000".equals(jsonObject.getString("code"))) {
            try {
                // 通过自己包装的HTTP请求进行发送
                Map<String, Object> responseMaps = DataRequestTool.toGetRequest("http://localhost:8080/test/getData", map, null);
                JSONObject body = new JSONObject();
                String encryptData = body.getString("encryptData");
                String encryptKey = body.getString("encryptKey");
                String encryptSign1 = body.getString("encryptSign1");
                String encryptSign2 = body.getString("encryptSign2");
                String encryptSign3 = body.getString("encryptSign3");
                // 数据请求回来进行解密
                Map<String, Object> responseMap = encryptService.commonToDecrypt(mapBySql.get("pushCode") + ":1", requestCode, encryptData, encryptSign1, encryptSign2, encryptSign3, encryptKey);
                JSONObject responseJson = new JSONObject(responseMap);
                // 校验请求是否成功 防止 SQL 报错
                if ("0000".equals(responseJson.getString("code"))) {
                    JSONObject decryptData = responseJson.getJSONObject("decryptData");

                    // 请求回来的数据如果是每次都要插入一张表的话，就一个大对象就好，如果是多个配置枚举类
                    Class classCoverObject = RequestCodeEnums.getClassCoverObject(requestCode);
                    List list = JSONArray.parseArray(decryptData.toJSONString(), classCoverObject);
                    //查询 SQL 的表中进行查询出落库的表名  用作反射的方法， 统一落库
                    String tableName = String.valueOf(mapBySql.get("tableName"));
                    HashMap<String, Object> dataList = Maps.newHashMap();
                    dataList.put("list", list);
                    //入库
//                    reflectionService.invokeService("reflectionService", tableName, dataList);
                    //结束一个定时任务 可以循环
                }
            } catch (Exception e) {
                //记录异常
                log.error("HTTP 请求异常 e:{},mapBySql:{}", e, JSONObject.toJSONString(mapBySql));
            }
        }
    }

    @Resource
    private EncryptService service;

    private static Map<String, Object> list = new ConcurrentSkipListMap<>();

    @Test
    public void test() throws ExecutionException, InterruptedException {
        List<CompletableFuture<Object> > completableFutures = Lists.newArrayList();

        for (int j = 0; j < 10; j++) {
            CompletableFuture<Object> objectCompletableFuture = CompletableFuture.supplyAsync(() -> {
                for (int i = 0; i < 50000; i++) {
                    Long pushCode = getPushCode();
                    if (list.containsKey(String.valueOf(pushCode))) {
                        System.out.println(true +"pushCode"+ pushCode);
                    } else {
                        list.put(pushCode.toString(), i);
                    }
                }
                return null;
            }, new ScheduledThreadPoolExecutor(10));
            completableFutures.add(objectCompletableFuture);
        }
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()])).get();

    }

    public Long getPushCode() {
        return service.getPushCodeByRedis("AAAA-test-key");
    }

    //企业应用id
    @Value("${qiyewx.enterpriseAppId}")
    private String enterpriseAppId;
    private String SEND_MSG_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=";
    //数据平台
    @Value("${qiyewx.datasecret}")
    private String dataSecret;
    @Test
    public void getAccCode() throws IOException {

            String user = "WangZeYu|jiem4";
            String dataRedisKey = "qy_wx_token:dataTocken";
        AccessTokenUtils bean = SpringUtils.getBean(AccessTokenUtils.class);
        String dataAccessToken = bean.getAccessToken(dataRedisKey, dataSecret);
            String url = SEND_MSG_URL+ dataAccessToken;
            JSONObject jsonObject = new JSONObject();



            jsonObject.put("touser",user);
            jsonObject.put("msgtype","text");
            jsonObject.put("agentid",Integer.parseInt(enterpriseAppId));
            jsonObject.put("content","测试测试测试123123123");
            jsonObject.put("touser",user);

            String json =jsonObject.toJSONString();

            String s = this.doPostWithJson(url, json);

            System.out.println(s);
    }


    public String getVXUserList (String accessToken, int limit) throws IOException {
        ArrayList<Map<String,Object>> returnList = new ArrayList<>();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/list_id?access_token="+accessToken;
        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("cursor","");
        jsonObject.put("limit",10000);
        String json =jsonObject.toJSONString();
        String s = this.doPostWithJson(url, json);
        JSONObject jsonObject1 = JSONObject.parseObject(s);
        List<Map<String, Object>> list = JSONObject.parseObject(jsonObject1.get("dept_user").toString(), List.class);
        for (Map<String, Object> map : list) {
            System.out.println(map);
        }
      return s;
    }


    public static String doPostWithJson(String url, String json) {
        String returnValue = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        try {
            //第一步：创建HttpClient对象
            httpClient = HttpClients.createDefault();

            //第二步：创建httpPost对象
            HttpPost httpPost = new HttpPost(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(30000)
                    .setSocketTimeout(30000).setConnectTimeout(10000).build();
            //第三步：给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(json, "utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            httpPost.setConfig(requestConfig);

            //第四步：发送HttpPost请求，获取返回值
            returnValue = httpClient.execute(httpPost, responseHandler); //调接口获取返回值时，必须用此方法
//            System.out.println("请求返回：" + returnValue);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        //第五步：处理返回值
        return returnValue;
    }
}
