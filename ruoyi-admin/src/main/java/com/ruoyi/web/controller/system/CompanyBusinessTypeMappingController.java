package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.service.ICompanyBusinessTypeMappingService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.CompanyBusinessTypeMapping;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 公司支持业务类型映射Controller
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@RestController
@RequestMapping("/company/business/type/mapping")
public class CompanyBusinessTypeMappingController extends BaseController
{
    @Autowired
    private ICompanyBusinessTypeMappingService companyBusinessTypeMappingService;

    /**
     * 查询公司支持业务类型映射列表
     */
   //@PreAuthorize("@ss.hasPermi('system:mapping:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompanyBusinessTypeMapping companyBusinessTypeMapping)
    {
        startPage();
        List<CompanyBusinessTypeMapping> list = companyBusinessTypeMappingService.selectCompanyBusinessTypeMappingList(companyBusinessTypeMapping);
        return getDataTable(list);
    }

    /**
     * 导出公司支持业务类型映射列表
     */
   //@PreAuthorize("@ss.hasPermi('system:mapping:export')")
    @Log(title = "公司支持业务类型映射", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CompanyBusinessTypeMapping companyBusinessTypeMapping)
    {
        List<CompanyBusinessTypeMapping> list = companyBusinessTypeMappingService.selectCompanyBusinessTypeMappingList(companyBusinessTypeMapping);
        ExcelUtil<CompanyBusinessTypeMapping> util = new ExcelUtil<CompanyBusinessTypeMapping>(CompanyBusinessTypeMapping.class);
        util.exportExcel(response, list, "公司支持业务类型映射数据");
    }

    /**
     * 获取公司支持业务类型映射详细信息
     */
   //@PreAuthorize("@ss.hasPermi('system:mapping:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(companyBusinessTypeMappingService.selectCompanyBusinessTypeMappingById(id));
    }

    /**
     * 新增公司支持业务类型映射
     */
   //@PreAuthorize("@ss.hasPermi('system:mapping:add')")
    @Log(title = "公司支持业务类型映射", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CompanyBusinessTypeMapping companyBusinessTypeMapping)
    {
        return toAjax(companyBusinessTypeMappingService.insertCompanyBusinessTypeMapping(companyBusinessTypeMapping));
    }

    /**
     * 修改公司支持业务类型映射
     */
   //@PreAuthorize("@ss.hasPermi('system:mapping:edit')")
    @Log(title = "公司支持业务类型映射", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CompanyBusinessTypeMapping companyBusinessTypeMapping)
    {
        return toAjax(companyBusinessTypeMappingService.updateCompanyBusinessTypeMapping(companyBusinessTypeMapping));
    }

    /**
     * 删除公司支持业务类型映射
     */
   //@PreAuthorize("@ss.hasPermi('system:mapping:remove')")
    @Log(title = "公司支持业务类型映射", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(companyBusinessTypeMappingService.deleteCompanyBusinessTypeMappingByIds(ids));
    }
}
