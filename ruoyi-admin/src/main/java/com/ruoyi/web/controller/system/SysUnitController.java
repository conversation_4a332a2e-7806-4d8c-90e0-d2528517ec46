package com.ruoyi.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUnit;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.service.ISysUnitService;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 全量公司信息Controller
 * 
 * <AUTHOR>
 * @date 2023-08-09
 */
@RestController
@RequestMapping("/system/unit")
public class SysUnitController extends BaseController
{
    @Autowired
    private ISysUnitService sysUnitsService;

    /**
     * 查询全量公司信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:unit:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUnit sysUnits)
    {
        startPage();
        List<SysUnit> list = sysUnitsService.selectSysUnitList(sysUnits);
        return getDataTable(list);
    }

    /**
     * 导出全量公司信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:unit:export')")
    @Log(title = "全量公司信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUnit sysUnits)
    {
        List<SysUnit> list = sysUnitsService.selectSysUnitList(sysUnits);
        ExcelUtil<SysUnit> util = new ExcelUtil<SysUnit>(SysUnit.class);
        util.exportExcel(response, list, "全量公司信息数据");
    }

    /**
     * 获取全量公司信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:unit:query')")
    @GetMapping(value = "/{unitId}")
    public AjaxResult getInfo(@PathVariable("unitId") Long id)
    {
        return AjaxResult.success(sysUnitsService.selectSysUnitById(id));
    }

    /**
     * 新增全量公司信息
     */
    @PreAuthorize("@ss.hasPermi('system:unit:add')")
    @Log(title = "全量公司信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysUnit sysUnits)
    {
    	if (UserConstants.NOT_UNIQUE.equals(sysUnitsService.checkUnitNameUnique(sysUnits)))
        {
            return AjaxResult.error("修改公司信息'" + sysUnits.getUnitName() + "'失败，公司名称已存在");
        }
        else if (UserConstants.NOT_UNIQUE.equals(sysUnitsService.checkUnitShortNameUnique(sysUnits)))
        {
            return AjaxResult.error("修改公司信息'" + sysUnits.getUnitShortName() + "'失败，公司简称已存在");
        }
        else if (UserConstants.NOT_UNIQUE.equals(sysUnitsService.checkUnitCodeUnique(sysUnits)))
        {
            return AjaxResult.error("修改公司信息'" + sysUnits.getUnitShortName() + "'失败，公司编码已存在");
        }
    	sysUnits.setCreateBy(getUsername());
        return toAjax(sysUnitsService.insertSysUnit(sysUnits));
    }

    /**
     * 修改全量公司信息
     */
    @PreAuthorize("@ss.hasPermi('system:unit:edit')")
    @Log(title = "全量公司信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUnit sysUnits)
    {
    	if (UserConstants.NOT_UNIQUE.equals(sysUnitsService.checkUnitNameUnique(sysUnits)))
        {
            return AjaxResult.error("修改公司信息'" + sysUnits.getUnitName() + "'失败，公司名称已存在");
        }
        else if (UserConstants.NOT_UNIQUE.equals(sysUnitsService.checkUnitShortNameUnique(sysUnits)))
        {
            return AjaxResult.error("修改公司信息'" + sysUnits.getUnitShortName() + "'失败，公司简称已存在");
        }
        else if (UserConstants.NOT_UNIQUE.equals(sysUnitsService.checkUnitCodeUnique(sysUnits)))
        {
            return AjaxResult.error("修改公司信息'" + sysUnits.getUnitShortName() + "'失败，公司编码已存在");
        }
    	sysUnits.setUpdateBy(getUsername());
        return toAjax(sysUnitsService.updateSysUnit(sysUnits));
    }

//    /**
//     * 删除全量公司信息
//     */
//    @PreAuthorize("@ss.hasPermi('system:units:remove')")
//    @Log(title = "全量公司信息", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{unitIds}")
//    public AjaxResult remove(@PathVariable Long[] unitIds)
//    {
//        return toAjax(sysUnitsService.deleteSysUnitByIds(ids));
//    }
    
    
    /**
     * 获取公司列表,用于下拉选择，所有正常公司（不含禁用）
     */
    @GetMapping("/getUnitListEnable")
    public AjaxResult getUnitListEnable()
    {
    	List<SysUnit> list = sysUnitsService.selectUnitListEnable();
    	if (StringUtils.isNull(list)){
    		list=new ArrayList<SysUnit>();
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取公司列表,用于下拉选择，所有公司(含禁用)
     */
    @GetMapping("/getUnitListAll")
    public AjaxResult getUnitListAll()
    {
    	List<SysUnit> list = sysUnitsService.selectUnitListAll();
    	if (StringUtils.isNull(list)){
    		list=new ArrayList<SysUnit>();
        }
        return AjaxResult.success(list);
    }
}
